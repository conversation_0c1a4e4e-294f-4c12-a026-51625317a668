package com.hdec.data.util;

import org.apache.poi.xwpf.usermodel.ParagraphAlignment;
import org.apache.poi.xwpf.usermodel.TableRowAlign;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public class ElementRendererUtil {

    /**
     * 根据 style 属性获取是 左对齐/右对齐/居中
     *
     * @param style style属性值
     * @return {@link TableRowAlign}
     */
    public static Align getAlignmentFromStyle(String style) {

        if (style == null || style.isEmpty()) {
            return Align.NONE;
        }

        Map<String, String> attributes = new HashMap<>();
        /* 分割 CSS 属性字符串 */
        Arrays.stream(style.split(";")).filter(item -> item.contains("margin")).forEach(margin -> {
            // 再次分割属性对以获取键和值
            String[] keyValue = margin.split(":");
            // 检查键值对是否有效
            if (keyValue.length == 2) {
                String key = keyValue[0].trim();
                String value = keyValue[1].trim();
                attributes.put(key, value);
            }
        });

        /* 无 margin 属性直接返回 */
        if (attributes.isEmpty()) {
            return Align.NONE;
        }

        String marginLeft = null;
        String marginRight = null;

        /* 先过滤 margin: 0 0 0 格式属性 */
        if (attributes.containsKey("margin")) {
            String value = attributes.get("margin");
            String[] directions = value.split(" ");
            switch (directions.length) {
                case 2: {
                    marginLeft = marginRight = directions[1];
                    break;
                }
                case 4: {
                    marginRight = directions[1];
                    marginLeft = directions[3];
                    break;
                }
                default: {
                    break;
                }
            }
        }

        /* 再过滤 margin-left、margin-right 格式属性 */
        marginLeft = attributes.containsKey("margin-left") ? attributes.get("margin-left").replace("px", "") : marginLeft;
        marginRight = attributes.containsKey("margin-right") ? attributes.get("margin-right").replace("px", "") : marginRight;

        /* 根据提取出的 margin-left、margin-right 判断居中 */
        if (Objects.equals(marginLeft, "auto") && Objects.equals(marginRight, "auto")) {
            return Align.CENTER;
        } else if ((Objects.equals(marginLeft, "0")) && Objects.equals(marginRight, "0")) {
            return Align.CENTER;
        } else if ((Objects.equals(marginLeft, "auto")) && Objects.equals(marginRight, "0")) {
            return Align.RIGHT;
        } else if ((Objects.equals(marginLeft, "0")) && Objects.equals(marginRight, "auto")) {
            return Align.LEFT;
        } else {
            return Align.NONE;
        }
    }

    /**
     * 根据 table 的 style 属性获取 table 是左对齐/右对齐/居中
     *
     * @param style style属性值
     * @return {@link TableRowAlign}
     */
    public static TableRowAlign getTableAlignmentFromStyle(String style) {
        Align align = getAlignmentFromStyle(style);
        TableRowAlign tableAlign = null;
        switch (align) {
            case LEFT: {
                tableAlign = TableRowAlign.LEFT;
                break;
            }
            case CENTER: {
                tableAlign = TableRowAlign.CENTER;
                break;
            }
            case RIGHT: {
                tableAlign = TableRowAlign.RIGHT;
                break;
            }
        }
        return tableAlign;
    }

    /**
     * 根据 table 的 style 属性获取 table 是左对齐/右对齐/居中
     *
     * @param style style属性值
     * @return {@link ParagraphAlignment }
     */
    public static ParagraphAlignment getParagraphAlignmentFromStyle(String style) {
        Align align = getAlignmentFromStyle(style);
        ParagraphAlignment paragraphAlignment = null;
        switch (align) {
            case LEFT: {
                paragraphAlignment = ParagraphAlignment.LEFT;
                break;
            }
            case CENTER: {
                paragraphAlignment = ParagraphAlignment.CENTER;
                break;
            }
            case RIGHT: {
                paragraphAlignment = ParagraphAlignment.RIGHT;
            }
        }
        return paragraphAlignment;
    }

    public enum Align {
        LEFT,
        CENTER,
        RIGHT,
        NONE;
    }
}
