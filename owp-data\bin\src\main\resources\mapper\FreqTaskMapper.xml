<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hdec.data.mapper.FreqTaskMapper">

    <select id="list" resultType="com.hdec.data.domain.FreqTask">
        SELECT
            id, target, point_nos AS pointNoStr, attr_name, wave_filter, filter_type, filter_param, start_time, end_time, time_span, time_span_unit, user_id, username, create_time, process, field_num
        FROM
            owp_freq_task
        <where>
            field_num = #{fieldNum}
            <if test="qo.target != null and qo.target != ''">
                AND target = #{qo.target}
            </if>
            <if test="qo.waveFilter != null and qo.waveFilter != ''">
                AND wave_filter = #{qo.waveFilter}
            </if>
            <if test="qo.filterType != null and qo.filterType != ''">
                AND filter_type = #{qo.filterType}
            </if>
            <if test="qo.status == 0">
                AND process &lt; 100
            </if>
            <if test="qo.status == 1">
                AND process &gt;= 100
            </if>
            <if test="qo.pointNos != null and qo.pointNos != ''">
                AND point_nos LIKE CONCAT('%', #{qo.pointNos}, '%')
            </if>
            <if test="qo.startTime != null and qo.startTime != '' and qo.endTime != null and qo.endTime != ''">
                AND start_time BETWEEN #{qo.startTime} AND #{qo.endTime}
                AND end_time BETWEEN #{qo.startTime} AND #{qo.endTime}
            </if>
        </where>
        ORDER BY
            create_time DESC
    </select>

    <select id="picList" resultType="com.hdec.data.domain.FreqPic">
        SELECT
            id, target, attr_name, url, pic_size, user_id, username, create_time, field_num
        FROM
            owp_freq_pic
        <where>
            field_num = #{fieldNum}
            <if test="qo.target != null and qo.target != ''">
                AND target = #{qo.target}
            </if>
            <if test="qo.status != null">
                AND status = #{qo.status}
            </if>
        </where>
        ORDER BY
        create_time DESC
    </select>

    <select id="getById" resultType="com.hdec.data.domain.FreqTask">
        SELECT
            *
        FROM
            owp_freq_task
        WHERE
            id = #{id}
    </select>

    <insert id="save" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
            owp_freq_task (
            target,
            point_ids,
            point_nos,
            attr_name,
            wave_filter,
            filter_type,
            filter_param,
            fs,
            start_time,
            end_time,
            time_span,
            time_span_unit,
            user_id,
            username,
            create_time,
            process,
            field_num)
        VALUES
            (#{target},
            #{pointIdStr},
            #{pointNoStr},
            #{attrName},
            #{waveFilter},
            #{filterType},
            #{filterParam},
            #{fs},
            #{startTime},
            #{endTime},
            #{timeSpan},
            #{timeSpanUnit},
            #{userId},
            #{username},
            NOW(),
            #{process},
            #{fieldNum})
    </insert>

    <insert id="picSave" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
            owp_freq_pic (
            target,
            attr_name,
            url,
            pic_size,
            user_id,
            username,
            create_time,
            field_num)
        VALUES
            (#{target},
            #{attrName},
            #{url},
            #{picSize},
            #{userId},
            #{username},
            NOW(),
            #{fieldNum})
    </insert>

    <delete id="delete">
        DELETE
        FROM
            owp_freq_task
        WHERE
            id IN
        <foreach collection="ids" item="id" separator=", " open="(" close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="picDelete">
        DELETE
        FROM
            owp_freq_pic
        WHERE
            id IN
        <foreach collection="ids" item="id" separator=", " open="(" close=")">
            #{id}
        </foreach>
    </delete>

    <update id="update" parameterType="com.hdec.data.domain.FreqTask">
        UPDATE
            owp_freq_task
        <set>
            process = #{process}
        </set>
        WHERE
            id = #{id}
    </update>

    <select id="getPicById" resultType="com.hdec.data.domain.FreqPic">
        SELECT
            id, target, attr_name, url, pic_size, status, user_id, username, create_time, field_num
        FROM
            owp_freq_pic
        WHERE
            id = #{id}
    </select>

    <select id="updatePicStatus" resultType="com.hdec.data.domain.FreqPic">
        update owp_freq_pic
            set status = #{status}
        WHERE
            id = #{id}
    </select>

</mapper>