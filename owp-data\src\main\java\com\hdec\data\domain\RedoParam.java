package com.hdec.data.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hdec.common.util.TimeUtil;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Objects;

/**
 * 重做任务参数实体类
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class RedoParam {

    /** 测点ID */
    private Integer pointId;

    /** 哪一天 */
    @JSONField(format = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone="GMT+8")
    private Date day;

    public RedoParam(Integer pointId, Date day) {
        this.pointId = pointId;
        this.day = day;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        RedoParam redoParam = (RedoParam) o;
        return Objects.equals(pointId, redoParam.pointId) &&
                Objects.equals(day, redoParam.day);
    }

    @Override
    public int hashCode() {
        return Objects.hash(pointId, day);
    }

    @Override
    public String toString() {
        return "RedoParam{" +
                "pointId=" + pointId +
                ", day=" + TimeUtil.format2Day(day) +
                '}';
    }
}
