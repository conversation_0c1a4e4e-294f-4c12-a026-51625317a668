package com.hdec.data.domain.report;

import com.hdec.data.domain.Datum;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/27
 */
@Data
@ToString
@NoArgsConstructor
public class Series {

    /** 线宽 */
    private Integer lineWidth = 1;

    /** 线条样式 */
    private String type = "line";

    /** 标题 */
    private String name;

    private String monitorName;

    private String chartAttrName;
    private String chartAttrName2;

    private String fileAttrName;
    private String fileAttrName2;

    /** 数据 */
    private List<Datum> data;
    private List<Datum> data2;

    private String seaType;
    private String leftAttrNameWithoutUnit;
    private String rightAttrNameWithoutUnit;

    public Series(String name, String monitorName, List<Datum> data) {
        this.name = name;
        this.monitorName = monitorName;
        this.data = data;
    }

    public Series(String name, String monitorName, String chartAttrName, String fileAttrName, List<Datum> data, String seaType) {
        this.name = name;
        this.monitorName = monitorName;
        this.chartAttrName = chartAttrName;
        this.fileAttrName = fileAttrName;
        this.data = data;
        this.seaType = seaType;
    }
    public Series(String name, String monitorName, String chartAttrName, String chartAttrName2, String fileAttrName, List<Datum> data, String seaType, List<Datum> data2) {
        this.name = name;
        this.monitorName = monitorName;
        this.chartAttrName = chartAttrName;
        this.chartAttrName2 = chartAttrName2;
        this.fileAttrName = fileAttrName;
        this.data = data;
        this.seaType = seaType;
        this.data2 = data2;
    }

    public Series(String name, String monitorName, String chartAttrName, String chartAttrName2, String fileAttrName, List<Datum> data, String seaType, List<Datum> data2,
                  String leftAttrNameWithoutUnit, String rightAttrNameWithoutUnit) {
        this.name = name;
        this.monitorName = monitorName;
        this.chartAttrName = chartAttrName;
        this.chartAttrName2 = chartAttrName2;
        this.fileAttrName = fileAttrName;
        this.data = data;
        this.seaType = seaType;
        this.data2 = data2;
        this.leftAttrNameWithoutUnit = leftAttrNameWithoutUnit;
        this.rightAttrNameWithoutUnit = rightAttrNameWithoutUnit;
    }
    public Series(String name, String monitorName, String chartAttrName, String chartAttrName2, String fileAttrName, String fileAttrName2, List<Datum> data, String seaType, List<Datum> data2,
                       String leftAttrNameWithoutUnit, String rightAttrNameWithoutUnit) {
        this.name = name;
        this.monitorName = monitorName;
        this.chartAttrName = chartAttrName;
        this.chartAttrName2 = chartAttrName2;
        this.fileAttrName = fileAttrName;
        this.fileAttrName2 = fileAttrName2;
        this.data = data;
        this.seaType = seaType;
        this.data2 = data2;
        this.leftAttrNameWithoutUnit = leftAttrNameWithoutUnit;
        this.rightAttrNameWithoutUnit = rightAttrNameWithoutUnit;
    }
}
