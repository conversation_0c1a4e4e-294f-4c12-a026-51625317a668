package com.hdec.data.qo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 测值评判查询类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class ValueJudgeQo {

    /** 开始时间 */
    private String startTime;

    /** 结束时间 */
    private String endTime;

    /** 分量ID */
    private Integer attrId;

    /** 分量名称 */
    private String attrName;

    /** 缺测时间间隔(秒) */
    private Integer missInterval;

    private String fieldNum;

}
