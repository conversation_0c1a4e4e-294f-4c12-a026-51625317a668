package com.hdec.data.qo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 数据日志查询类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class DataLogQo {

    /** 当前页 */
    private Integer pageNum;

    /** 每页条数 */
    private Integer pageSize;

    /** 用户名关键字 */
    private String usernameKw;

    /** 测点编号关键字 */
    private String pointNoKw;

    /** 开始时间 */
    private String startTime;

    /** 结束时间 */
    private String endTime;

    /** 类型（1：整编日志  2：删除日志） */
    private Integer type;

}
