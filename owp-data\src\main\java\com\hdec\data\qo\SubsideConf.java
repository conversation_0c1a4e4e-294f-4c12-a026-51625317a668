package com.hdec.data.qo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class SubsideConf implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer instId;

    private Integer calcAttrId;

    private Integer h2AttrId;

    private Integer h3AttrId;

    private Integer h4AttrId;
}
