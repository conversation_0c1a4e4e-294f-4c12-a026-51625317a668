package com.hdec.data.qo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * Excel录入类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class ExcelImportQo {

    /** 是否覆盖历史数据 */
    private Boolean isOverride;

    /** 频率 */
    private String rate;

    /** 是否批量录入 */
    private Boolean isBatch;

    /** 文件名 */
    private String excelName;

    /** 是否根据Sheet名称自动识别测点名称 */
    private Boolean isAutoPointName;

    /** 导入方式（1：预置格式录入  2：Excel表格录入） */
    private Integer importType = 2;

    private Integer type;

    /** 是否是单向 */
    private Boolean isOneWay;

    /** 匹配关系 */
    private List<Map<String, String>> mappings;

    /** 收藏匹配关系 */
    private String savedMapping;

    private String filepath;

    /** 文件路径数组 */
    private List<String> filePaths;

    /**
     * 仪器ID
     */
    private Integer instId;

}
