package com.hdec.data.service;

import com.hdec.data.domain.ReportBaseInfo;
import com.hdec.data.domain.ReportPerson;
import com.hdec.data.mapper.ReportBaseInfoMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 报告基本信息业务
 *
 * <AUTHOR>
 */
@Service
public class ReportBaseInfoService {

    @Autowired
    private ReportBaseInfoMapper baseInfoMapper;

    /**
     * 获取某项目报告基本信息
     */
    public ReportBaseInfo getBaseInfo(String fieldNum) {
        return baseInfoMapper.getBaseInfo(fieldNum);
    }

    /**
     * 更新某项目报告基本信息
     */
    public void updateBaseInfo(ReportBaseInfo baseInfo) {
        ReportBaseInfo existBaseInfo = baseInfoMapper.getBaseInfo(baseInfo.getFieldNum());
        if (existBaseInfo == null) {
            baseInfoMapper.saveBaseInfo(baseInfo);
        } else {
            baseInfoMapper.updateBaseInfo(baseInfo);
        }
    }

    /**
     * 获取某项目人员信息
     */
    public ReportPerson getPersonConf(String fieldNum) {
        return baseInfoMapper.getPersonConf(fieldNum);
    }

    /**
     * 修改某项目人员信息
     */
    public void updatePersonConf(String fieldNum, ReportPerson reportPerson) {
        reportPerson.setFieldNum(fieldNum);
        if (baseInfoMapper.getPersonConf(fieldNum) != null) {
            baseInfoMapper.updatePersonConf(reportPerson);
        } else {
            baseInfoMapper.savePersonConf(reportPerson);
        }
    }

}
