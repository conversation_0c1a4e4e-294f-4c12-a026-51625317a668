package com.hdec.data.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

@Data
@ToString
@NoArgsConstructor
public class HighTask {

    /** 主键 */
    private Integer id;

    /** 频率 */
    private String rate;

    /** 类型 */
    private String type;

    /** 测点 */
    private Integer point;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createTime;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date finishTime;

    @JSONField(format = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone="GMT+8")
    private Date day;

    private Integer status;

    public HighTask(String rate, String type, Integer point, Date day) {
        this.rate = rate;
        this.type = type;
        this.point = point;
        this.day = day;
        this.createTime = new Date();
        this.status = 0;
    }
}
