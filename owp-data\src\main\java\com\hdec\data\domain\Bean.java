package com.hdec.data.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@NoArgsConstructor
public class Bean {

    private Long ts;

    private Short subDeviceNo;

    private Short channel;

    private Float[] vs;

    public Bean(Long ts, Short subDeviceNo, Short channel, Float[] vs) {
        this.ts = ts;
        this.subDeviceNo = subDeviceNo;
        this.channel = channel;
        this.vs = vs;
    }
}
