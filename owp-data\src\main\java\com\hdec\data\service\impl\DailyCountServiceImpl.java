package com.hdec.data.service.impl;

import com.hdec.common.domain.PointCommon;
import com.hdec.common.util.TimeUtil;
import com.hdec.data.cache.LocalCacheManager;
import com.hdec.data.domain.DailyCount;
import com.hdec.data.mapper.TaosMapper;
import com.hdec.data.service.DailyCountService;
import com.hdec.data.service.TaosService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class DailyCountServiceImpl implements DailyCountService {

    private static final Logger logger = LoggerFactory.getLogger(DailyCountServiceImpl.class);

    @Resource
    private TaosService taosService;

    @Resource
    private LocalCacheManager localCacheManager;

    @Override
    public Long selectDailyCount(Integer point, Date date) {
        if (point == null) {
            return null;
        }
        PointCommon pointCache = localCacheManager.getPointCache(point);
        if (pointCache == null || pointCache.getInstId() == null) {
            return null;
        }
        Integer instId = pointCache.getInstId();
        Date endTime = TimeUtil.addDays(date, 1);
        return taosService.selectDailyCount(instId, point, date, endTime);
    }

    @Override
    public void insectDailyCount(List<DailyCount> dailyCounts) {
        if (dailyCounts == null || dailyCounts.isEmpty()) {
            return;
        }
        taosService.insectDailyCount(dailyCounts);
    }

    @Override
    public Long selectDataCount(List<Integer> points, Date start, Date end) {
        if (points == null || points.isEmpty()) {
            return 0L;
        }
        Long count = taosService.selectDataCount(points, start, end);
        return count == null ? 0L : count;
    }

}
