package com.hdec.data.netty;

import cn.hutool.core.util.ByteUtil;
import com.hdec.common.domain.msg.Inst;
import com.hdec.data.service.DataAccessService;
import com.hdec.data.service.KafkaService;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * DM客户端消息处理类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class NettyHandlerDM extends ChannelInboundHandlerAdapter {

    private static NettyHandlerDM handler;

    /** 缓存大小 */
    private static final int CACHE_SIZE = 50 * 2;

    /** 缓存容器 */
    private static Map<String, List<byte[]>> cacheMap = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        handler = this;
    }

    @Autowired
    private MsgService msgService;

    @Autowired
    private KafkaService kafkaService;

    @Resource
    private DataAccessService accessService;

    /** 单线程池 */
    private final static ThreadPoolExecutor singleThread = new ThreadPoolExecutor(1, 1, 0, TimeUnit.DAYS, new ArrayBlockingQueue<>(1000), new ThreadPoolExecutor.DiscardOldestPolicy());

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) {
        ByteBuf byteBuf = (ByteBuf) msg;
        byte[] bytes = new byte[byteBuf.readableBytes()];
        byteBuf.readBytes(bytes);
        byteBuf.release();

        try {
            singleThread.submit(() -> {
                handleMsg(ctx, bytes);
            });
        } catch (Exception e) {
            log.info("队列已满，提交失败！");
            log.error("队列已满，提交失败！");
        }
    }

    private void handleMsg(ChannelHandlerContext ctx, byte[] bytes) {
        /* 将消息发送到队列 */
        List<Inst> instList = handler.msgService.parse(bytes);
        handleResponse(instList, ctx);

        if (!ObjectUtils.isEmpty(instList)) {
//            if (instList.get(0).getDeviceNo().contains("10042")) {
//                if (Math.random() > 0.9995) {
//                    for (Inst inst : instList) {
//                        log.info(inst.toString());
//                    }
//                }
//            }
//            handler.kafkaService.consume(instList, "dynamic");
            handler.accessService.input(instList, "dynamic");

//            log.info();
//            if (instList.get(0).getDeviceNo().contains("10019")) {
//                if (Math.random() > 0.9) {
//                    for (Inst inst : instList) {
//                        log.info(inst.toString());
//                    }
//
////                    log.error("数据@{}@{}", JSON.toJSONString(instList), TimeUtil.format2Ms(instList.get(0).getTime()));
//                }
//            }
        }
    }

    /**
     * 回复客户端
     */
    private void handleResponse(List<Inst> instList, ChannelHandlerContext ctx) {
        if (ObjectUtils.isEmpty(instList)) {
            return;
        }

        /* 由设备编号找到设备对应的容器 */
        String deviceNo = instList.get(0).getDeviceNo();
        List<byte[]> times = cacheMap.get(deviceNo);
        if (times == null) {
            times = new ArrayList<>(CACHE_SIZE);
            cacheMap.put(deviceNo, times);
        }

        times.add(ByteUtil.longToBytes(instList.get(0).getTime().getTime()));
        if (times.size() >= CACHE_SIZE) {
            byte[] resBytes = new byte[times.size() * 8];
            for (int i = 0; i < times.size(); i++) {
                byte[] bytes = times.get(i);
                for (int j = 0; j < bytes.length; j++) {
                    resBytes[i * 8 + j] = bytes[j];
                }
            }
            ctx.writeAndFlush(Unpooled.copiedBuffer(resBytes));
            times.clear();
        }
    }

}
