<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hdec.data.mapper.HighTaskMapper">

    <insert id="addBatch" parameterType="com.hdec.data.domain.HighTask" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO owp_task_high (
            `point`, `day`, rate, `type`, status, create_time
        )
        VALUES
        <foreach collection="tasks" item="task" separator="," >
            (#{task.point}, #{task.day}, #{task.rate}, #{task.type}, #{task.status}, #{task.createTime})
        </foreach>
    </insert>

    <select id="select" resultType="com.hdec.data.domain.HighTask">
        SELECT
            *
        FROM
            owp_task_high
        WHERE
            `status` = 0
            <if test="task.type != null"> AND `type` = #{task.type}</if>
            <if test="task.point != null"> AND `point` = #{task.point}</if>
            <if test="task.day != null"> AND `day` = #{task.day}</if>
        ORDER BY create_time
        limit 1
    </select>

    <update id="resetTaskStatus">
        UPDATE owp_task_high
        SET
            `status` = 0
        WHERE
            `status` = 1
    </update>

    <select id="selectFirstTaskByLevel" resultType="com.hdec.data.domain.HighTask">
        SELECT
            *
        FROM
            owp_task_high
        WHERE
            status = 0 AND `type` = #{type}
        LIMIT 1
    </select>

    <update id="update">
        UPDATE
            owp_task_high
        <set>
            <if test="task.status != null">`status` = #{task.status},</if>
            <if test="task.startTime != null">`start_time` = #{task.startTime},</if>
            <if test="task.finishTime != null">`finish_time` = #{task.finishTime},</if>
        </set>
        WHERE
            id = #{task.id}
    </update>

    <delete id="delete">
        DELETE
        FROM
            owp_task_high
        WHERE
            id = #{id}
    </delete>

    <delete id="delOldData">
        DELETE FROM owp_task_high
        WHERE create_time &lt; DATE_SUB(NOW(), INTERVAL #{days} DAY)
    </delete>
</mapper>