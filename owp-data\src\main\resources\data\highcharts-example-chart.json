{
  "title": {
    "text": ""
  },

  "credits": {
    "enabled": false
  },
  "exporting": {
    "scale": 2
  },
  "tooltip": {
    "dateTimeLabelFormats": {
      "millisecond": "%Y-%m-%d",
      "second": "%Y-%m-%d",
      "minute": "%Y-%m-%d",
      "hour": "%Y-%m-%d",
      "day": "%Y-%m-%d",
      "week": "%Y-%m-%d",
      "month": "%Y-%m-%d",
      "year": "%Y-%m-%d"
    }
  },
  "chart": {
    "height": customHeight,
    "width": customWidth,
    "plotBorderWidth": 1,
    "plotBorderColor": "#333333",
    "spacing": [0, 45, 0, 0],

    "style": {
      "fontSize": "16px",
      "fontFamily": "Times New Roman"
    }
  },
  "yAxis": [
    {
      "opposite": false,
      "tickWidth": 1,
      "tickLength": -5,
      "lineColor": "#333333",
      "tickColor": "#333333",
      "tickAmount": 5,
      "title": {
        "floating": true,
        "align": "high",
        "x": 10,
        "y": -10,
        "rotation": 0,

        "text": "分量名",
        "style": {
          "fontWeight": "bold",
          "color": "#ccccc",
          "fontSize": "16px"
        }
      },
      "labels": {
        "padding": 0,
        "reserveSpace": false,

        "style": {"fontSize": "16px", "color": "#333333"},
        "x": -5
      },
      "allowDecimals": true,
      "minPadding": 0,
      "tickPixelInterval": 40
      CustomYAxis
    },

    {
      "title": {
        "enabled": false,
        "floating": true,
        "align": "high",
        "x": -65,
        "y": -10,
        "rotation": 0,

        "text": "xxx",
        "style": {
          "fontWeight": "bold",
          "color": "#ccccc",

          "fontSize": "16px"
        }
      },
      "lineColor": "#333333",
      "tickColor": "#333333",
      "opposite": true,
      "tickWidth": 1,
      "tickLength": -5,
      "labels": {
        "enabled": false,
        "reserveSpace": false,

        "padding": 0,

        "style": {"fontSize": "16px", "color": "#333333"},
        "x": 5
      },
      "allowDecimals": true,
      "minPadding": 0,
      "tickPixelInterval": 40
      CustomYAxis
    }
  ],

  "xAxis": [
    {
      "lineColor": "#333333",
      "tickColor": "#333333",
      "opposite": false,
      "gridLineWidth": 1,
      "tickWidth": 1,
      "tickLength": -5,
      "type": "datetime",
      "labels": {
        "style": {
          "textOverflow": "none",
          "whiteSpace": "nowrap",
          "fontSize": "16px",
          "color": "#333333"
        }
      },
      "startOnTick": true,
      "endOnTick": true,

      "tickPositions": [millisXXX],
      "dateTimeLabelFormats": {
        "millisecond": "%Y-%m-%d",
        "second": "%Y-%m-%d",
        "minute": "%Y-%m-%d",
        "hour": "%Y-%m-%d",
        "day": "%Y-%m-%d",
        "week": "%Y-%m-%d",
        "month": "%Y-%m-%d",
        "year": "%Y-%m-%d"
      },
      "title": {
        "enabled": true,
        "floating": true,

        "text": "日期(年-月-日)",
        "y": -4,
        "x": 40,
        "align": "high",
        "style": {
          "fontWeight": "bold",
          "color": "#ccccc",

          "fontSize": "16px"
        }
      }
    },

    {
      "lineColor": "#333333",
      "tickColor": "#333333",
      "opposite": true,
      "tickWidth": 1,
      "tickLength": -5,
      "type": "datetime",
      "labels": {
        "enabled": false,
        "reserveSpace": false,
        "style": {
          "textOverflow": "none",
          "whiteSpace": "nowrap",
          "fontSize": "16px",
          "color": "#333333"
        }
      },
      "startOnTick": true,
      "endOnTick": true,

      "tickPositions": [millisXXX]
    }
  ],

  "legend": {
    "align": "center",
    "verticalAlign": "top",
    "x": 50
  },

  "series": [
    element
  ]
}
