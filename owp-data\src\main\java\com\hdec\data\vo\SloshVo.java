package com.hdec.data.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class SloshVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date time;

    /** 主方向偏移 */
    private Double mainDis;

    /** 垂直主方向偏移 */
    private Double verMainDis;

    public SloshVo(Date time, Double mainDis, Double verMainDis) {
        this.time = time;
        this.mainDis = mainDis;
        this.verMainDis = verMainDis;
    }

}
