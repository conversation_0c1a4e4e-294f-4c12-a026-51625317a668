<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hdec.data.mapper.FreqResultMapper">

    <select id="list" resultType="com.hdec.data.domain.FreqResult">
        SELECT
            *
        FROM
            owp_freq_result
        WHERE
            point_id = #{pointId}
            AND direct_id = #{directId}
            AND `time` between #{startTime} and #{endTime}
            AND wave_filter_alg is not null
        ORDER BY
            `time`
    </select>

    <select id="picList" resultType="com.hdec.data.domain.FreqResult">
        SELECT
            *
        FROM
            owp_freq_result
        WHERE
            point_id = #{pointId}
            AND direct_id = #{directId}
            AND `time` between #{startTime} and #{endTime}
            AND wave_filter_alg is null
        ORDER BY
            `time`
    </select>

    <select id="getById" resultType="com.hdec.data.domain.FreqResult">
        SELECT
            *
        FROM
            owp_freq_result
        WHERE
            id = #{id}
    </select>

    <select id="getByIds" resultType="com.hdec.data.domain.FreqResult">
        SELECT
            *
        FROM
            owp_freq_result
        WHERE
            <foreach collection="array" item="id" separator=" or ">
            id = #{id,jdbcType=INTEGER}
            </foreach>
    </select>

    <insert id="save" parameterType="com.hdec.data.domain.FreqResult">
        INSERT INTO
            owp_freq_result (
            point_id,
            point_no,
            direct_id,
            refer_num,
            wave_filter_alg,
            `time`,
            duration,
            duration_unit,
            `img_url`,
            `task_param`,
            field_num,
            val
        )
        VALUES
            (#{pointId}, #{pointNo}, #{directId}, #{referNum}, #{waveFilterAlg}, #{time}, #{duration}, #{durationUnit}, #{imgUrl},  #{taskParam}, #{fieldNum}, #{val})
    </insert>

    <update id="update">
        UPDATE
            owp_freq_result
        SET
            point_id = #{pointId},
            direct_id = #{directId},
            refer_num = #{referNum},
            wage_filter_alg = #{wageFilterAlg},
            `time` = #{time},
            duration = #{duration},
            duration_unit = #{durationUnit},
            `data` = #{data}
        WHERE
            id = #{id}
    </update>

    <delete id="delete">
        DELETE
        FROM
            owp_freq_result
        WHERE
            id = #{id}
    </delete>

    <delete id="delByCondition">
        DELETE
        FROM
            owp_freq_result
        WHERE
            point_id = #{pointId}
            AND direct_id = #{directId}
            AND `time` = #{time}
            AND duration = #{duration}
            AND duration_unit = #{durationUnit}
    </delete>

    <select id="selectTimesByPoints" resultType="com.hdec.data.domain.FreqResult">
        SELECT DISTINCT
            point_id,
            DATE_FORMAT( `time`, '%Y-%m-%d' ) AS `time`
        FROM
            owp_freq_result
        WHERE
            point_id IN
            <foreach item="pointId" collection="pointIds" open="(" separator="," close=")">
                #{pointId}
            </foreach>
            <if test="startTime != null and startTime != '' and endTime != null and endTime != ''">
                AND `time` BETWEEN #{startTime} AND #{endTime}
            </if>
    </select>

    <update id="mark">
        UPDATE owp_freq_result
        SET refer_num = CASE
            WHEN refer_num = 0 THEN 1
            WHEN refer_num = 1 THEN 0
            ELSE refer_num
        END
        where id = #{id}
    </update>

    <select id="getByPoint" resultType="string">
        select img_url from owp_freq_result where point_id = #{pointId} and direct_id = #{direct} and refer_num = 1
        <if test="startTime != null and startTime != '' and endTime != null and endTime != ''">
            AND `time` BETWEEN #{startTime} AND #{endTime}
        </if>
        limit 1
    </select>

</mapper>