<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hdec.data.mapper.ExcelImportMapper">

    <sql id="common_sql" >
        `time`, `type`, `rate`, `is_override`, `is_auto_point_name`, is_one_way, mappings, status, filename, filepath, user_id, user_name, import_res, field_num, inst_id
    </sql>

    <select id="importList" resultType="com.hdec.data.domain.Import">
        SELECT
            id, <include refid="common_sql"/>, saved_mapping, original_name
        FROM
            owp_import
        WHERE
            user_id = #{userId} AND field_num = #{fieldNum} AND `status` = #{qo.status}
        <if test="qo.startTime != null and qo.startTime != '' and qo.endTime != null and qo.endTime != ''">
            AND `time` between #{qo.startTime} and #{qo.endTime}
        </if>
        <if test="qo.keyword != null and qo.keyword != ''">
            AND `filename` LIKE CONCAT('%', #{qo.keyword}, '%')
        </if>
        <if test="qo.isBatch != null">
            AND `is_batch` = #{qo.isBatch}
        </if>
        <if test="qo.type != null">
            AND `type` = #{qo.type}
        </if>
        <if test="qo.importRes != null and qo.importRes == '成功'">
            AND (`status` = 1 AND import_res is null or trim(import_res) = '') or `status` = 0
        </if>
        <if test="qo.importRes != null and qo.importRes == '失败'">
            AND (`status` = 1 AND trim(import_res) != '') or `status` = 0
        </if>
        ORDER BY `time` DESC
    </select>

    <select id="getImportById" resultType="com.hdec.data.domain.Import">
        SELECT
            *
        FROM
            owp_import
        WHERE
            id = #{id}
    </select>

    <insert id="saveImport" useGeneratedKeys="true" keyProperty="id" parameterType="com.hdec.data.domain.Import">
        INSERT INTO owp_import (<include refid="common_sql"/>, saved_mapping, is_batch, original_name)
        VALUES
        (NOW(), #{imp.type}, #{imp.rate}, #{imp.isOverride}, #{imp.isAutoPointName}, #{imp.isOneWay}, #{imp.mappings},
        #{imp.status}, #{imp.filename}, #{imp.filepath}, #{imp.userId}, #{imp.userName}, #{imp.importRes}, #{imp.fieldNum},#{imp.instId},
         #{imp.savedMapping}, #{imp.isBatch}, #{imp.originalName})
    </insert>

    <update id="updateFilepath" parameterType="com.hdec.data.domain.Import">
        UPDATE owp_import
        SET
            `status` = 1
        WHERE
            id = #{imp.id}
    </update>

    <update id="updateFilePaths" parameterType="com.hdec.data.domain.Import">
        UPDATE owp_import
        SET
            `mappings` = #{mappings}
        WHERE
            id = #{id}
    </update>

    <update id="setFinish">
        UPDATE owp_import
        SET
            `status` = 1,
            `import_res` = #{errFilepath}
        WHERE
            id = #{id}
    </update>

    <delete id="delete">
        DELETE
        FROM
            owp_import
        WHERE
            id IN
            <foreach collection="ids" item="id" separator=", " open="(" close=")">
                #{id}
            </foreach>
    </delete>

    <insert id="saveImportUrls" parameterType="com.hdec.data.domain.ImportUrl">
        INSERT INTO owp_import_url (
            `import_id`,
            `url`,
            `upload_status`,
            `import_status`
        )
        VALUES
        <foreach collection ="urls" item="url" separator =",">
            (#{url.importId}, #{url.url}, #{url.uploadStatus}, #{url.importStatus})
        </foreach>
    </insert>

    <select id="isImportFinished" resultType="boolean">
        SELECT
            import_status
        FROM
            owp_import_url
        WHERE
            import_id = #{importId}
        AND url = #{url}
        LIMIT 1
    </select>

    <select id="getImportUrlById" resultType="com.hdec.data.domain.ImportUrl">
        SELECT
            *
        FROM
            owp_import_url
        WHERE
            import_id = #{importId}
    </select>

    <select id="getImportUrlsByIds" resultType="com.hdec.data.domain.ImportUrl">
        SELECT
            *
        FROM
            owp_import_url
        WHERE
            import_id IN
        <foreach collection="importIds" item="importId" separator=", " open="(" close=")">
            #{importId}
        </foreach>
    </select>

    <update id="updateBatchUrl" parameterType="com.hdec.data.domain.ImportUrl">
        UPDATE owp_import_url
        <trim prefix="set" suffixOverrides=",">
            <if test="importUrl.uploadStatus != null">`upload_status` = #{importUrl.uploadStatus},</if>
            <if test="importUrl.importStatus != null">import_status = #{importUrl.importStatus},</if>
            err_msg = #{importUrl.errMsg},
        </trim>
        WHERE
            import_id = #{importUrl.importId}
        AND url = #{importUrl.url}
    </update>

    <select id="getNotFinishedImports" resultType="boolean">
        SELECT
            import_status
        FROM
            owp_import_url
        WHERE
            import_id = #{importId}
        AND import_status = 0
        LIMIT 1
    </select>

</mapper>