package com.hdec.data.util;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import sun.misc.BASE64Decoder;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Html解析工具类
 *
 * <AUTHOR>
 */
public class JsoupUtil {

    public static BufferedImage getImageInfo(String chart) throws IOException {
        String base64Image = chart.replaceFirst("data:image/png;base64,", "");
        if(StringUtils.isNotEmpty(chart)){
            byte [] decoder = new BASE64Decoder().decodeBuffer(base64Image);
            InputStream is=new ByteArrayInputStream(decoder);
            return ImageIO.read(is);
        }
        return null;
    }

    public static List<Map<String, List<Map<String, Object>>>> getRichText(String htmlContent) {
        if (ObjectUtils.isEmpty(htmlContent)) {
            return Collections.emptyList();
        }

        int i = 200;
        htmlContent = htmlContent.replace("&nbsp;", "");
        List<Map<String, List<Map<String, Object>>>> resList = new ArrayList<>();

        Document document = Jsoup.parse(htmlContent, "utf-8");
        Elements elements = document.body().select("p,h2");
        for (Element element : elements) {
            if (element.toString().startsWith("<h2")) {
                Map<String, List<Map<String, Object>>> textMap = new HashMap<>();
                List<Map<String, Object>> textList = new ArrayList<>();
                Map<String, Object> objMap = new HashMap<>();
                objMap.put("str", element.text());
                textList.add(objMap);
                textMap.put("titleList", textList);
                resList.add(textMap);
            } else {
                String html = element.html();
                if (html.startsWith("<img")) {
                    Elements imgTags = element.select("img[src]");
                    String base64 = imgTags.attr("src").split(",")[1];

                    BufferedImage imageInfo;
                    int[] widths = null;
                    try {
                        imageInfo = getImageInfo(base64);
                        widths = perfectImageInfo(imageInfo);
                    } catch (IOException e) {
                        e.printStackTrace();
                    }

                    Map<String, List<Map<String, Object>>> picMap = new HashMap<>();
                    List<Map<String, Object>> picList = new ArrayList<>();
                    Map<String, Object> objMap = new HashMap<>();
                    objMap.put("url", base64);
                    objMap.put("id", i++);
                    objMap.put("width", widths[0]);
                    objMap.put("height", widths[1]);
                    picList.add(objMap);
                    picMap.put("picList", picList);
                    resList.add(picMap);
                } else {
                    if ("<br>".equals(html)) {
                        html = "";
                    }
                    Map<String, List<Map<String, Object>>> textMap = new HashMap<>();
                    List<Map<String, Object>> textList = new ArrayList<>();
                    Map<String, Object> objMap = new HashMap<>();
                    objMap.put("str", delHTMLTag(html));
                    textList.add(objMap);
                    textMap.put("textList", textList);
                    resList.add(textMap);
                }
            }
        }
        return resList;
    }

    private static int[] perfectImageInfo(BufferedImage imageInfo) {
        int threshold = 450;
        int[] arr = new int[2];
        int width = imageInfo.getWidth();
        int height = imageInfo.getHeight();
        if (width > threshold) {
            int dis = width - threshold;
            double ra = dis * 1.0 / width;
            arr[0] = (int) (width * (1 - ra));
            arr[1] = (int) (height * (1 - ra));
        } else {
            arr[0] = width;
            arr[1] = height;
        }
        return arr;
    }

    public static String delHTMLTag(String htmlStr) {
        //定义script的正则表达式
        String regEx_script = "<script[^>]*?>[\\s\\S]*?<\\/script>";
        //定义style的正则表达式
        String regEx_style = "<style[^>]*?>[\\s\\S]*?<\\/style>";
        //定义HTML标签的正则表达式
        String regEx_html = "<[^>]+>";
        Pattern p_script = Pattern.compile(regEx_script, Pattern.CASE_INSENSITIVE);
        Matcher m_script = p_script.matcher(htmlStr);
        //过滤script标签
        htmlStr = m_script.replaceAll("");
        Pattern p_style = Pattern.compile(regEx_style, Pattern.CASE_INSENSITIVE);

        Matcher m_style = p_style.matcher(htmlStr);
        //过滤style标签
        htmlStr = m_style.replaceAll("");
        Pattern p_html = Pattern.compile(regEx_html, Pattern.CASE_INSENSITIVE);
        Matcher m_html = p_html.matcher(htmlStr);
        //过滤html标签
        htmlStr = m_html.replaceAll("");
        //返回文本字符串
        return htmlStr.trim();
    }

    /**
     * 按属性值获取节点
     */
    public static List<Element> getElementsByAttr(Document document, String attr, String attrVal) {
        if (ObjectUtils.isEmpty(attr) || ObjectUtils.isEmpty(attrVal)) {
            return Collections.emptyList();
        }

        List<Element> elements = new ArrayList<>();
        Elements attrElements = document.getElementsByAttribute(attr);
        for (Element attrElement : attrElements) {
            if(attrVal.equals(attrElement.attr(attr))) {
                elements.add(attrElement);
            }
        }
        return elements;
    }

}
