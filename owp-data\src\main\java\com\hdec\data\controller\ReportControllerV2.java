package com.hdec.data.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hdec.common.domain.R;
import com.hdec.data.domain.report2.Report;
import com.hdec.data.qo.Report2Qo;
import com.hdec.data.service.ReportServiceV2;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 报告管理版本2
 *
 * <AUTHOR>
 */
@Api(tags = "报告管理版本2")
@Validated
@RestController
@RequestMapping("api/data/report2")
public class ReportControllerV2 {

    @Autowired
    private ReportServiceV2 reportServiceV2;

    /**
     * 报告列表
     */
    @ApiOperation("报告列表")
    @PostMapping("list")
    public R list(@RequestHeader("fieldNum") String fieldNum, @RequestBody Report2Qo qo) {
        PageHelper.startPage(qo.getPageNum(), qo.getPageSize());
        List<Report> moulds = reportServiceV2.list(fieldNum);
        return R.success(new PageInfo<>(moulds));
    }

    /**
     * 批量删除报告
     */
    @ApiOperation("批量删除报告")
    @DeleteMapping("{ids}")
    public R deleteBatch(@NotNull(message = "请勾选待删除项") @PathVariable Integer[] ids) {
        reportServiceV2.delete(ids);
        return R.success("删除成功");
    }

    /**
     * 新增报告
     */
    @ApiOperation("新增报告")
    @PostMapping("add")
    public R add(@RequestHeader("fieldNum") String fieldNum, @RequestBody Report report) {
        report.setFieldNum(fieldNum);
        reportServiceV2.add(report);
        return R.success("新增成功");
    }

}
