package com.hdec.data.qo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class DataQueryQo {

    /** 测点编号ID */
    private Integer[] pointIds;

    /** 测点编号 */
    private String[] pointNos;

    /** 方向 */
    private Integer[] directs;

    /** 分量ID */
    private String[] attrIds;

    /** 分量名称 */
    private String[] attrNames;

    /** 开始时间 */
    private String startTime;

    /** 结束时间 */
    private String endTime;

    /** 当前页 */
    private Integer pageNum;

    /** 每页条数 */
    private Integer pageSize;

    /** 排序项 */
    private String orderBy;

    /** 方向筛选 */
    private Integer direct;

    /** 排序规则（升序：asc  降序：desc） */
    private String orderRule;

    /** 审核状态（0：未审核  1：通过  2：未通过） */
    private Integer[] approval;

    /** 频率（高频：high 分钟：min 小时：hour） */
    private String rate;

    /** 最小值 */
    private Float min;

    /** 最大值 */
    private Float max;

    private String fieldNum;
}
