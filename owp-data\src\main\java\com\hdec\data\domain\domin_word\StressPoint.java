package com.hdec.data.domain.domin_word;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * @date 2022/12/28
 */
@Data
@ToString
@NoArgsConstructor
public class StressPoint {

    /** 测点编号 */
    private String pointNo;

    /** 安装高程 */
    private String installEle;

    /** 安装方位 */
    private String installOrient;

    private Double min;
    private Double max;
    private Double avg;

    public StressPoint(String pointNo, String installEle, String installOrient, Double min, Double max, Double avg) {
        this.pointNo = pointNo;
        this.installEle = installEle;
        this.installOrient = installOrient;
        this.min = min;
        this.max = max;
        this.avg = avg;
    }

    public String getInstallEle() {
        if (ObjectUtils.isEmpty(installEle) || ObjectUtils.isEmpty(installEle.trim())) {
            return " / ";
        }
        return installEle;
    }

    public String getInstallOrient() {
        if (ObjectUtils.isEmpty(installOrient) || ObjectUtils.isEmpty(installOrient.trim())) {
            return " / ";
        }
        return installOrient;
    }
}
