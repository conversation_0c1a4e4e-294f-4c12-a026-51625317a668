package com.hdec.data.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.ObjectUtils;

import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExportTask {

    private Integer id;

    private Integer userId;

    private String username;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date time;

    /** 审核状态（0：未审核  1：通过  2：未通过） */
    private Integer[] approval;

    /** 方向筛选 */
    private Integer direct;

    private String approvals;

    private String progress;

    private String url;

    private String rate;

    private String startTime;

    private String endTime;

    private List<Integer> pointIds;

    private List<String> pointNos;

    private String pointNo;

    private String fieldNum;

//    public String getApprovals() {
//        if (ObjectUtils.isEmpty(approval)) {
//            return approvals;
//        }
//
//        StringBuilder sb = new StringBuilder();
//        for (Integer i : approval) {
//            if (i == 0) {
//                sb.append("未审核，");
//            } else if (i == 1) {
//                sb.append("审核通过，");
//            } else if (i == 2) {
//                sb.append("审核未通过，");
//            } else {
//                sb.append("未知，");
//            }
//        }
//        sb.deleteCharAt(sb.length() - 1);
//        return sb.toString();
//    }
}
