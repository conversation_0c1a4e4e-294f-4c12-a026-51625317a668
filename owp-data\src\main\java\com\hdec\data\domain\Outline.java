package com.hdec.data.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hdec.data.domain.report2.Report;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * 报告大纲实体类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class Outline {

    /** 自增主键 */
    private Integer id;

    /** 名称 */
    private String name;

    /** 类型 */
    private String type;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createTime;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updateTime;

    /** 操作人ID */
    private Integer userId;

    /** 操作人用户名 */
    private String username;

    /** 风场编码 */
    private String fieldNum;

    private Integer mouldId;

    private String navName;

    /** 引用大纲的报告 */
    private List<Report> reports;

}
