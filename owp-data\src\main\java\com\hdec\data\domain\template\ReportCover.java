package com.hdec.data.domain.template;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hdec.data.domain.report2.Report;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * 报告封面
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class ReportCover {

    /** 自增主键 */
    private Integer id;

    /** 名称 */
    private String name;

    /** 类型 */
    private Integer type;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createTime;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updateTime;

    /** 操作人ID */
    private Integer userId;

    /** 操作人用户名 */
    private String username;

    /** 备注 */
    private String remark;

    /** 内容 */
    private String content;

    /** 风场编码 */
    private String fieldNum;

    /** 引用封面的报告 */
    private List<Report> reports;

}
