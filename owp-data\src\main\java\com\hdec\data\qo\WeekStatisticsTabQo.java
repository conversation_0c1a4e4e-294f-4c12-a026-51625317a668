package com.hdec.data.qo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 自定义表格查询类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class WeekStatisticsTabQo {
    /**
     * 海上设施
     */
    String facilityId;

    /**
     * 统计分量
     */
    @NotNull(message = "统计分量不能为空！")
    StatAttr statAttr;
    /**
     * 典型风机/非典型风机/所有风机/升压站
     */
    @NotBlank(message = "设施类型不能为空！")
    private String type;
    /**
     * 仪器ID
     */
    @NotNull(message = "仪器ID不能为空！")
    private Integer instId;

}
