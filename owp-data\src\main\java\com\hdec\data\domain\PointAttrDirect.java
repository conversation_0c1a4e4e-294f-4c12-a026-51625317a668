package com.hdec.data.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 测点、分量、方向实体类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class PointAttrDirect {

    /** 测点ID */
    private Integer pointId;

    /** 分量ID */
    private Integer attrId;

    /** 方向ID */
    private Integer directId;

    public PointAttrDirect(String str) {
        String[] arr = str.split("-");
        if (arr.length >= 3) {
            this.pointId = Integer.parseInt(arr[0]);
            this.attrId = Integer.parseInt(arr[1]);
            this.directId = Integer.parseInt(arr[2]);
        }
    }
}
