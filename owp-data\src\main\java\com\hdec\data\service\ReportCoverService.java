package com.hdec.data.service;

import com.hdec.common.domain.R;
import com.hdec.common.domain.ResourceCommon;
import com.hdec.common.util.FileUtil;
import com.hdec.common.util.ReportUtil;
import com.hdec.common.util.TimeUtil;
import com.hdec.data.domain.report2.Report;
import com.hdec.data.domain.template.ReportCover;
import com.hdec.data.mapper.ReportCoverMapper;
import com.hdec.data.qo.ReportCoverQo;
import com.hdec.data.util.EasyWord;
import com.hdec.data.util.JsoupUtil;
import com.hdec.data.util.WordToPdfUtil;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 封面业务类
 *
 * <AUTHOR>
 */
@Service
public class ReportCoverService {

    @Autowired
    private ReportCoverMapper coverMapper;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Lazy
    @Autowired
    private ReportServiceV2 reportServiceV2;

    /** 访问映射 */
    @Value("${file.visitMapping}")
    private String visitMapping;

    /** 文件本地路径 - Linux */
    @Value("${file.linuxPath}")
    private String linuxPath;

    /**
     * 报告封面列表
     */
    public List<ReportCover> list(String fieldNum, ReportCoverQo qo) {
        List<ReportCover> covers = coverMapper.list(fieldNum, qo.getName(), qo.getType(), qo.getStartTime(), qo.getEndTime());

        /* 设置引用数量 */
        if (!ObjectUtils.isEmpty(covers)) {
            Map<Integer, List<Report>> coverReportsMap = getCoverReportsMap(fieldNum);
            for (ReportCover cover : covers) {
                cover.setReports(coverReportsMap.get(cover.getId()));
            }
        }
        return covers;
    }

    /**
     * 获取所有报告并按封面分组
     */
    private Map<Integer, List<Report>> getCoverReportsMap(String fieldNum) {
        List<Report> fieldReports = reportServiceV2.list(fieldNum);
        if (ObjectUtils.isEmpty(fieldReports)) {
            return Collections.emptyMap();
        }
        return fieldReports.stream().collect(Collectors.groupingBy(Report::getCoverId));
    }

    /**
     * 更新封面
     */
    public Integer update(ReportCover cover) {
        ReportCover existCover = coverMapper.selectByName(cover.getFieldNum(), cover.getName(), cover.getId());
        if (existCover != null) {
            return -1;
        }

        coverMapper.update(cover);
        return cover.getId();
    }

    /**
     * 新增封面
     */
    public Integer add(ReportCover cover, String sessionId) {
        ReportCover existCover = coverMapper.selectByName(cover.getFieldNum(), cover.getName(), cover.getId());
        if (existCover != null) {
            return -1;
        }

        /* 设置创建人信息 */
        ResourceCommon resource = (ResourceCommon) redisTemplate.opsForValue().get(sessionId);
        if (resource != null) {
            cover.setUserId(resource.getUserId());
            cover.setUsername(resource.getUsername());
        }

        coverMapper.add(cover);
        return cover.getId();
    }

    /**
     * 获取某个封面详情
     */
    public ReportCover detail(Integer id) {
        return coverMapper.getById(id);
    }

    /**
     * 批量删除
     */
    public R delete(String fieldNum, Integer[] ids) {
        /* 存在引用的封面无法删除 */
        Map<Integer, List<Report>> coverReportsMap = getCoverReportsMap(fieldNum);
        for (Integer coverId : ids) {
            if (!ObjectUtils.isEmpty(coverReportsMap.get(coverId))) {
                ReportCover cover = coverMapper.getById(coverId);
                return R.error("'" + (cover == null ? "未知" : cover.getName()) + "' 被报告引用，请先删除报告");
            }
        }

        coverMapper.delete(ids);
        return R.success("删除成功");
    }

    /**
     * 封面预览
     */
    public String coverPreview(Integer coverId) throws Exception {
        ReportCover cover = coverMapper.getById(coverId);
        if (ObjectUtils.isEmpty(cover) || ObjectUtils.isEmpty(cover.getContent())) {
            return null;
        }

        /* 替换变量 */
        replaceVar(cover);

        /* 生成word并转为pdf */
        String pre = "/reports_review/" + coverId + "/";
        String docPath = linuxPath + pre;
        FileUtil.makeSureDirExist(docPath);
        EasyWord.html2NormalWord(cover.getContent(), "/home/<USER>", docPath + "封面预览.docx");

        WordToPdfUtil.word2pdf(docPath + "封面预览.docx");
        return visitMapping + pre + "封面预览.pdf";
    }

    /**
     * 替换变量
     */
    private void replaceVar(ReportCover cover) {
        String content = cover.getContent();
        if (ObjectUtils.isEmpty(content)) {
            return;
        }

        Document doc = Jsoup.parse(cover.getContent());
        List<Element> baseElements = JsoupUtil.getElementsByAttr(doc, "data-type", "base");
        for (Element baseEle : baseElements) {
            String text = baseEle.text();
            if ("@{监测年份}".equals(text)) {
                baseEle.html(TimeUtil.format(new Date(), "yyyy"));
            } else if ("@{月报期数}".equals(text)) {
                baseEle.html("1");
            } else if ("@{总月报期数}".equals(text)) {
                baseEle.html("1");
            } else if ("@{编写日期(yyyy年mm月dd日)}".equals(text)) {
                baseEle.html(ReportUtil.addNewRome2EngNumber(TimeUtil.format(new Date(), "yyyy年MM月dd日")));
            } else if ("@{编写日期(yyyy年mm月)}".equals(text)) {
                baseEle.html(ReportUtil.addNewRome2EngNumber(TimeUtil.format(new Date(), "yyyy年MM月")));
            } else if ("@{监测时段(yyyy年mm月dd日～yyyy年mm月dd日)}".equals(text)) {
                baseEle.html(ReportUtil.addNewRome2EngNumber("2024年01月01日～2024年01月31日"));
            } else if ("@{监测时段(yyyy-mm-dd～yyyy-mm-dd)}".equals(text)) {
                baseEle.html(ReportUtil.addNewRome2EngNumber("2024-01-01～2024-01-31"));
            }
        }
        cover.setContent(doc.body().html());
    }

    /**
     * 判断封面是否需要填写期数
     */
    public List<String> isNeedIssue(Integer id) {
        List<String> res = new ArrayList<>();
        ReportCover cover = coverMapper.getById(id);
        if (cover == null || cover.getContent() == null) {
            return res;
        }

        Document doc = Jsoup.parse(cover.getContent());
        List<Element> baseElements = JsoupUtil.getElementsByAttr(doc, "data-type", "base");
        for (Element baseEle : baseElements) {
            if ("@{月报期数}".equals(baseEle.text())) {
                res.add("月报期数");
                break;
            }
        }
        for (Element baseEle : baseElements) {
            if ("@{总月报期数}".equals(baseEle.text())) {
                res.add("总月报期数");
                break;
            }
        }
        return res;
    }
}
