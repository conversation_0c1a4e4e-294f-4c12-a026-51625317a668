package com.hdec.data.domain.domin_word;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * @date 2022/12/28
 */
@Data
@ToString
@NoArgsConstructor
public class SettlePoint {

    /** 测点编号 */
    private String pointNo;

    /** 安装高程 */
    private String installEle;

    private Double min;
    private Double max;
    private Double avg;

    public SettlePoint(String pointNo, String installEle, Double min, Double max, Double avg) {
        this.pointNo = pointNo;
        this.installEle = installEle;
        this.min = min;
        this.max = max;
        this.avg = avg;
    }

    public String getInstallEle() {
        if (ObjectUtils.isEmpty(installEle) || ObjectUtils.isEmpty(installEle.trim())) {
            return null;
        }
        return installEle;
    }
}
