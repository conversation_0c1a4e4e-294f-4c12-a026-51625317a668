package com.hdec.data.cache;

import com.hdec.data.qo.DataQueryQo;
import com.hdec.data.qo.StatQo;
import io.netty.channel.Channel;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
public class Cache {

    public static Map<String, DataQueryQo> exportMap = new ConcurrentHashMap<>();
    public static Map<String, StatQo> exportStatMap = new ConcurrentHashMap<>();

    public static Map<String, Double> map = new ConcurrentHashMap<>();

    public static volatile Boolean hasAlarmFlag = false;

    public static Map<String, Channel> channelMap = new ConcurrentHashMap<>();

    public static void put(String id, Double process) {
        map.put(id, process);
    }

    public static void remove(String id) {
        map.remove(id);
    }


    /** 存放通道ID和设备编号对应关系 */
    public final static Map<String, String> channelIdDeviceNoMap = new ConcurrentHashMap<>();

    public final static Map<String, Long> userIdTimerTsMap = new ConcurrentHashMap<>();
    public final static Map<String, Long> sTimes = new ConcurrentHashMap<>();

}
