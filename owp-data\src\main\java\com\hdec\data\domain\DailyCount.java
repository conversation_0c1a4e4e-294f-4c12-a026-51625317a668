package com.hdec.data.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class DailyCount implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 测点
     */
    private Integer point;

    /**
     * 时间
     */
    private Date ts;

    /**
     * 数据量
     */
    private Long dataCount;

    public DailyCount(Integer point, Date ts) {
        this.point = point;
        this.ts = ts;
    }
}
