package com.hdec.data.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * 批量导入配置类
 */
@Data
@ToString
@NoArgsConstructor
public class BatchImportCfg {

    private Integer id;

    /**
     * 类型（1：原始量  2成果量）
     */
    private Integer type;

    /**
     * 是否覆盖历史数据
     */
    private Boolean isOverride;

    /**
     * 配置参数
     */
    private String param;

    /**
     * 批量导入配置参数
     */
    private List<ImportCfgParam> params;

    /**
     * 风场编码
     */
    private String fieldNum;
}