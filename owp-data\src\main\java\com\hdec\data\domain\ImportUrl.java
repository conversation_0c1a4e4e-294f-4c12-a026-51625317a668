package com.hdec.data.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 批量录入文件信息
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class ImportUrl {

    private Integer id;

    private Integer importId;

    private String url;

    private Boolean uploadStatus;

    private Boolean importStatus;

    private String errMsg;

    public ImportUrl(Integer importId, String url) {
        this.importId = importId;
        this.url = url;
        this.uploadStatus = false;
        this.importStatus = false;
    }

    public ImportUrl(Integer importId, String url, Boolean uploadStatus, Boolean importStatus) {
        this.importId = importId;
        this.url = url;
        this.uploadStatus = uploadStatus;
        this.importStatus = importStatus;
    }

    public ImportUrl(Integer importId, String url, Boolean uploadStatus, Boolean importStatus, String errMsg) {
        this.importId = importId;
        this.url = url;
        this.uploadStatus = uploadStatus;
        this.importStatus = importStatus;
        this.errMsg = errMsg;
    }
}
