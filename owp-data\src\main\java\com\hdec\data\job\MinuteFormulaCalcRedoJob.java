package com.hdec.data.job;

import com.hdec.common.constant.Constant;
import com.hdec.common.formula.AttrVal;
import com.hdec.data.domain.RedoJob;
import com.hdec.data.enums.RedoJobType;
import com.hdec.data.service.AlarmService;
import com.hdec.data.service.FormulaCalcService;
import com.hdec.data.service.RedoJobService;
import com.hdec.data.service.TaosService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 分钟公式重做任务
 *
 * <AUTHOR>
 * @date 2025-08-05
 */

@Component
public class MinuteFormulaCalcRedoJob {
    private static final Logger logger = LoggerFactory.getLogger(MinuteFormulaCalcRedoJob.class);


    @Value("${deploy.fieldNum}")
    private String fieldNum;


    @Resource
    private RedoJobService redoJobService;

    @Resource
    private FormulaCalcService formulaCalcService;

    @Resource
    private TaosService taosService;

    @Resource
    private AlarmService alarmService;

    @Scheduled(cron = "0 * * * * ?")
    public void minuteFormulaCalc() {
        long start = System.currentTimeMillis();
        List<RedoJob> redoJobs = redoJobService.selectUndoTask(RedoJobType.MINUTE, 60);
        if (redoJobs == null || redoJobs.isEmpty()) {
            return;
        }
        for (RedoJob job : redoJobs) {
            /*  计算分钟公式  */
            Integer point = job.getPoint();
            Date startTime = job.getTs();
            Date endTime = new Date(startTime.getTime() + 60 * 1000);
            List<AttrVal> values = formulaCalcService.computeDep(point, Constant.RATE_MIN, startTime, endTime);
            taosService.batchSave(values);
            alarmService.processAlarm(values,fieldNum);
            /*  标记任务完成  */
            job.setStatus(1);
        }
        redoJobService.completeJob(RedoJobType.MINUTE, redoJobs);
        long end = System.currentTimeMillis();
        logger.info("分钟公式重做任务耗时: {}ms", end - start);
    }
}
