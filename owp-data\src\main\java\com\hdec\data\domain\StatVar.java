package com.hdec.data.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * 统计变量
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class StatVar {

    /** 类型（true: 风机 false: 升压站） */
    private Boolean isFan;

    /** 海上设施ID */
    private List<String> seaFacilityIds;

    /** 安装部位 */
    private Boolean installLocation;

    /** 分量名 */
    private String attrName;

    /** 方向 */
    private Integer direct;

    /** 统计项 */
    private String statItem;

    public StatVar(Boolean isFan, List<String> seaFacilityIds, Boolean installLocation, String attrName, Integer direct, String statItem) {
        this.isFan = isFan;
        this.seaFacilityIds = seaFacilityIds;
        this.installLocation = installLocation;
        this.attrName = attrName;
        this.direct = direct;
        this.statItem = statItem;
    }

    public StatVar(Boolean isFan, List<String> seaFacilityIds, String attrName, Integer direct, String statItem) {
        this.isFan = isFan;
        this.seaFacilityIds = seaFacilityIds;
        this.attrName = attrName;
        this.direct = direct;
        this.statItem = statItem;
    }
}
