package com.hdec.data.job;

import com.hdec.common.util.TimeUtil;
import com.hdec.data.domain.DailyCount;
import com.hdec.data.domain.RedoJob;
import com.hdec.data.enums.RedoJobType;
import com.hdec.data.service.DailyCountService;
import com.hdec.data.service.RedoJobService;
import com.hdec.data.service.SimpleService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 日统计重做任务
 *
 * <AUTHOR>
 * @date 2025-08-05
 */

@Component
public class DailyCountRedoJob {
    private static final Logger logger = LoggerFactory.getLogger(DailyCountRedoJob.class);

    @Resource
    private RedoJobService redoJobService;
    @Resource
    private DailyCountService dailyCountService;
    @Resource
    private SimpleService simpleService;

    @Scheduled(initialDelay = 10, fixedDelay = 180, timeUnit = TimeUnit.SECONDS)
    public void redoDailyCount() {
        long start = System.currentTimeMillis();
        List<RedoJob> redoJobs = redoJobService.selectUndoTask(RedoJobType.DAILY_COUNT, 0);
        List<DailyCount> dailyCounts = new ArrayList<>();
        for (RedoJob job : redoJobs) {
            /*  统计数量  */
            Long count = dailyCountService.selectDailyCount(job.getPoint(), job.getTs());
            if (count != null) {
                dailyCounts.add(new DailyCount(job.getPoint(), job.getTs(), count));
            }
            /*  处理高频预采样  */
            job.setStatus(1);
        }

        dailyCountService.insectDailyCount(dailyCounts);

        Map<Date, List<RedoJob>> timeMap = redoJobs.stream().collect(Collectors.groupingBy(RedoJob::getTs));
        timeMap.forEach((time, jobs) -> {
            /*  高频预采样  */
            List<Integer> pointIds = jobs.stream().map(RedoJob::getPoint).collect(Collectors.toList());
            simpleService.doHighSample(pointIds, time, TimeUtil.addDays(time, 1));
        });

        // redoJobService.completeJob(RedoJobType.DAILY_COUNT, redoJobs);
        long end = System.currentTimeMillis();
        logger.info("重做日数量统计任务完成, 耗时: {}ms", end - start);
    }
}
