package com.hdec.data.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.hdec.common.domain.*;
import com.hdec.common.util.CommonUtil;
import com.hdec.common.util.FileUtil;
import com.hdec.data.domain.ContentVo;
import com.hdec.data.domain.Outline;
import com.hdec.data.domain.OutlineNav;
import com.hdec.data.domain.ReportOutlineNav;
import com.hdec.data.domain.report2.Report;
import com.hdec.data.domain.template.Mould;
import com.hdec.data.domain.template.ReportVar;
import com.hdec.data.feign.MonitorService;
import com.hdec.data.feign.WindService;
import com.hdec.data.mapper.NavMouldMapper;
import com.hdec.data.mapper.ReportOutlineMapper;
import com.hdec.data.mapper.VarMapper;
import com.hdec.data.qo.MouldVal;
import com.hdec.data.qo.OutlineQo;
import com.hdec.data.util.EasyWord;
import com.hdec.data.util.JsoupUtil;
import com.hdec.data.vo.ParamVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 报告大纲业务类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ReportOutlineService {

    @Autowired
    private ReportOutlineMapper outlineMapper;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private WindService windService;

    @Autowired
    private MonitorService monitorService;

    @Autowired
    private VarService varService;

    @Lazy
    @Autowired
    private ReportServiceV2 reportServiceV2;

    @Autowired
    private NavMouldMapper navMouldMapper;

    @Lazy
    @Autowired
    private ReportMouldService mouldService;

    @Autowired
    private VarMapper varMapper;

    /**
     * 大纲导航树
     */
    public List<ReportOutlineNav> nav(String fieldNum, Integer outlineId) {
        // 用于设置下标
        AtomicInteger index = new AtomicInteger(0);

        List<OutlineNav> navList = outlineMapper.navList(outlineId);
        List<ReportOutlineNav> navTree = createOutlineNavTree(navList, 0, index);

        /* 添加序号 */
        if (!ObjectUtils.isEmpty(navTree)) {
            for (int i = 0; i < navTree.size(); i++) {
                ReportOutlineNav nav = navTree.get(i);
                String parentSeqNum = String.valueOf(i + 1);
                nav.setSeqNum(parentSeqNum);
                recursiveLoopCreateSeqNum(nav.getChildren(), parentSeqNum);
            }
        }
        return navTree;
    }

    private MouldVal findMouldVal(Integer navId, Integer mouldId, List<MouldVal> mouldValues) {
        return mouldValues.stream().filter(e -> navId.equals(e.getNavId()) && mouldId.equals(e.getMouldId())).findFirst().get();
    }

    private List<Mould> getNavMoulds(Integer navId, List<Mould> fieldMoulds, List<MouldVal> mouldValues) {
        List<Integer> mouldIds = mouldValues.stream().filter(e -> navId.equals(e.getNavId())).map(MouldVal::getMouldId).collect(Collectors.toList());
        return fieldMoulds.stream().filter(e -> mouldIds.contains(e.getId())).collect(Collectors.toList());
    }

    /**
     * 递归创建导航树
     */
    private List<ReportOutlineNav> createOutlineNavTree(List<OutlineNav> navList, Integer pid, AtomicInteger index) {
        List<ReportOutlineNav> branch = new ArrayList<>();
        for (OutlineNav nav : navList) {
            if (pid.equals(nav.getPid())) {
                // 找到子节点就放到下面
                ReportOutlineNav outlineNav = new ReportOutlineNav(nav.getId(), index.incrementAndGet(), String.valueOf(nav.getId()), nav.getName(), nav.getIsWarning());
                branch.add(outlineNav);
                // 递归封装子节点
                outlineNav.setChildren(createOutlineNavTree(navList, nav.getId(), index));
            }
        }
        return ObjectUtils.isEmpty(branch) ? null : branch;
    }

    /**
     * 新增大纲导航
     */
    public R navAdd(String fieldNum, OutlineNav nav) {
        List<OutlineNav> existNavs = outlineMapper.selectNavByNameAndPid(nav.getName(), nav.getPid(), nav.getOutlineId(), fieldNum, null);
        if (!ObjectUtils.isEmpty(existNavs)) {
            return R.error("节点名称已存在");
        }

        /* 同级节点需要调整顺序 */
        if (nav.getNavId() != null) {
            OutlineNav brotherNav = outlineMapper.getNavById(nav.getNavId());
            if (brotherNav != null) {
                nav.setPid(brotherNav.getPid());
                nav.setOrder(brotherNav.getOrder() == null ? 1 : (brotherNav.getOrder() + 1));
                outlineMapper.updateOrder(brotherNav.getPid(), nav.getOrder());
            }
        } else {
            Integer maxOrder = outlineMapper.getMaxOrder(nav.getPid());
            nav.setOrder(maxOrder == null ? 1 : maxOrder + 1);
        }

        outlineMapper.addNav(fieldNum, nav);
        return R.success("新增成功");
    }

    /**
     * 修改大纲导航
     */
    public R navUpdate(OutlineNav nav) {
        OutlineNav oldNav = outlineMapper.getNavById(nav.getId());
        oldNav.setName(nav.getName());
        List<OutlineNav> existNavs = outlineMapper.selectNavByNameAndPid(oldNav.getName(), oldNav.getPid(), oldNav.getOutlineId(), oldNav.getFieldNum(), oldNav.getId());
        if (!ObjectUtils.isEmpty(existNavs)) {
            return R.error("节点名称已存在");
        }

        outlineMapper.updateNav(oldNav);
        return R.success("修改成功");
    }

    /**
     * 删除大纲导航
     */
    public void navDelete(Integer id) {
        outlineMapper.navDelete(id);
        /* 删除该导航下所有模板关联关系 */
        navMouldMapper.deleteByNavId(id);

        List<OutlineNav> childrenNav = outlineMapper.getChildrenById(id);
        for (OutlineNav childNav : childrenNav) {
            navDelete(childNav.getId());
        }
    }

    /**
     * 一键生成大纲导航
     */
    public void oneTouch(String fieldNum, Integer outlineId) {
        /*  删除该大纲下所有导航节点 */
        deleteNavByOutline(outlineId);

        /*  获取当前导航树起始ID */
        int startNavId = outlineMapper.getMaxNavId() + 1;

        /* 获取当前风场所有典型风机 */
        List<SeaFacilityCommon> seaFacilities = windService.allSeaFacility(fieldNum);
        List<SeaFacilityCommon> typicalFans = seaFacilities.stream().filter(e -> e.getId().startsWith("1-")).filter(e -> e.getIsTypical() != null && e.getIsTypical()).collect(Collectors.toList());
        List<SeaFacilityCommon> nonTypicalFans = seaFacilities.stream().filter(e -> e.getId().startsWith("1-")).filter(e -> !(e.getIsTypical() != null && e.getIsTypical())).collect(Collectors.toList());

        List<OutlineNav> navs = getPreOutlineNavs(outlineId, startNavId, typicalFans);

        System.out.println("典型风机：");
        typicalFans.forEach(System.out::println);
        System.out.println("导航节点：");
        navs.forEach(System.out::println);

        outlineMapper.addNavs(fieldNum, navs);
    }

    /**
     * 一键生成大纲导航
     */
    public void oneTouch2(String fieldNum, Integer outlineId, Integer type, String[] vars) {
        /*  删除该大纲下所有导航节点 */
        deleteNavByOutline(outlineId);

        /*  获取当前导航树起始ID */
        int startNavId = outlineMapper.getMaxNavId() + 1;

        /* 获取当前风场所有典型风机 */
        List<SeaFacilityCommon> seaFacilities = windService.allSeaFacility(fieldNum);
        List<SeaFacilityCommon> typicalFans = seaFacilities.stream().filter(e -> e.getId().startsWith("1-")).filter(e -> e.getIsTypical() != null && e.getIsTypical()).collect(Collectors.toList());
        List<SeaFacilityCommon> nonTypicalFans = seaFacilities.stream().filter(e -> e.getId().startsWith("1-")).filter(e -> !(e.getIsTypical() != null && e.getIsTypical())).collect(Collectors.toList());

        sortSeaFacility(seaFacilities);
        sortSeaFacility(typicalFans);
        sortSeaFacility(nonTypicalFans);

        List<OutlineNav> navs = null;
        if (type == 1) {
            // 默认模板
            navs = getPreOutlineNavs(outlineId, startNavId, typicalFans);
        } else if (type == 2) {
            // 所有海上设施模板
            navs = getPreOutlineNavs2(outlineId, startNavId, vars, typicalFans, nonTypicalFans);
        } else if (type == 3) {
            // 基础型式模板
            navs = getPreOutlineNavs3(outlineId, startNavId, vars, seaFacilities, typicalFans, nonTypicalFans);
        }

//        System.out.println("导航节点：");
//        navs.forEach(System.out::println);
        outlineMapper.addNavs(fieldNum, navs);
    }

    private void sortSeaFacility(List<SeaFacilityCommon> seaFacilities) {
        if (ObjectUtils.isEmpty(seaFacilities)) {
            return;
        }

        // 排序
        seaFacilities.sort((f1, f2) -> {
            Integer no1 = CommonUtil.extractUniqueNum(f1.getName());
            Integer no2 = CommonUtil.extractUniqueNum(f2.getName());
            if (no1 == null && no2 == null) {
                return 0;
            }
            if (no1 == null) {
                return 1;
            }
            if (no2 == null) {
                return -1;
            }
            return no1 - no2;
        });
    }

    /**
     * 删除大纲下所有导航
     */
    private void deleteNavByOutline(Integer outlineId) {
        /* 获取该大纲下的所有一级导航 */
        List<OutlineNav> rootNavs = outlineMapper.getRootNavsByOutlineId(outlineId);
        for (OutlineNav rootNav : rootNavs) {
            navDelete(rootNav.getId());
        }
    }

    /**
     * 大纲列表
     */
    public List<Outline> list(String fieldNum, OutlineQo qo) {
        List<Outline> outlines = outlineMapper.list(fieldNum, qo.getName(), qo.getType(), qo.getStartTime(), qo.getEndTime());

        /* 设置引用数量 */
        if (!ObjectUtils.isEmpty(outlines)) {
            Map<Integer, List<Report>> outlineReportsMap = getOutlineReportsMap(fieldNum);
            for (Outline outline : outlines) {
                outline.setReports(outlineReportsMap.get(outline.getId()));
            }
        }
        return outlines;
    }

    /**
     * 获取所有报告并按大纲分组
     */
    private Map<Integer, List<Report>> getOutlineReportsMap(String fieldNum) {
        List<Report> fieldReports = reportServiceV2.list(fieldNum);
        if (ObjectUtils.isEmpty(fieldReports)) {
            return Collections.emptyMap();
        }
        return fieldReports.stream().collect(Collectors.groupingBy(Report::getOutlineId));
    }

    /**
     * 新增大纲
     */
    public R add(String fieldNum, Outline outline, HttpServletRequest request) {
        outline.setFieldNum(fieldNum);
        /* 设置创建人信息 */
        String sessionId = request.getHeader("sessionId");
        ResourceCommon resource = (ResourceCommon) redisTemplate.opsForValue().get(sessionId);
        if (resource != null) {
            outline.setUserId(resource.getUserId());
            outline.setUsername(resource.getUsername());
        }

        outlineMapper.add(outline);
        return R.success("操作成功");
    }

    /**
     * 修改大纲
     */
    public R update(Outline outline) {
        outlineMapper.update(outline);
        return R.success("操作成功");
    }

    /**
     * 批量删除大纲
     */
    public R delete(String fieldNum, Integer[] ids) {
        /* 存在引用的大纲无法删除 */
        Map<Integer, List<Report>> coverReportsMap = getOutlineReportsMap(fieldNum);
        for (Integer outlineId : ids) {
            if (!ObjectUtils.isEmpty(coverReportsMap.get(outlineId))) {
                Outline outline = outlineMapper.getById(outlineId);
                return R.error("'" + (outline == null ? "" : outline.getName()) + "' 被报告引用，请先删除报告");
            }
        }

        /* 删除大纲下所有导航 */
        for (Integer id : ids) {
            deleteNavByOutline(id);
        }

        outlineMapper.delete(ids);
        return R.success("删除成功");
    }

    /**
     * 模板绑定到导航
     */
    public R addMould2Nav(Integer navId, Integer mouldId) {
        Integer id = navMouldMapper.getByNavMould(navId, mouldId);
        if (id != null) {
            return R.error("该模板已绑定到当前导航节点");
        }

        String fanVal = null;
        String content = mouldService.getById(mouldId).getContent();
        if (content.contains("@{风机编号}") || content.contains("@{升压站}")) {
            fanVal = "";
            System.out.println("here");
        }

        navMouldMapper.addMould2navId(navId, mouldId, fanVal);
        return R.success("添加成功");
    }

    /**
     * 模板移除大纲
     */
    public void removeMould2Nav(Integer navId, Integer mouldId) {
//        navMouldMapper.delByRefId(mouldId);
        delRef(navId, mouldId);

        navMouldMapper.removeMould2Nav(navId, mouldId);
    }

    /**
     * 删除引用关系
     */
    private void delRef(Integer navId, Integer mouldId) {
        // 查询所有使用到该模板的引用信息
        MouldVal mouldVal = navMouldMapper.selectRefInfo(navId, mouldId);
        if (mouldVal == null || ObjectUtils.isEmpty(mouldVal.getRefVal())) {
            return;
        }

        System.out.println("查询所有使用到该模板的引用信息：" + mouldVal.toString());
        String ref = delRef(mouldVal.getRefVal(), navId + "_u-" + mouldId + "-var");
        navMouldMapper.updateRef(mouldVal.getId(), ref);
    }

    private static String delRef(String json, String delId) {
        if (ObjectUtils.isEmpty(json)) {
            return json;
        }

        JSONArray array = JSON.parseArray(json);
        JSONArray filteredArray = new JSONArray();
        for (int i = 0; i < array.size(); i++) {
            JSONObject obj = array.getJSONObject(i);
            String refId = obj.getString("refId");
            if (!refId.contains(delId)) {
                filteredArray.add(obj);
            }
        }

        String result = JSON.toJSONString(filteredArray);
        return result;
    }

    /**
     * 为大纲内的模板设置值
     */
    public void setMouldValue(String fieldNum, MouldVal mouldVal) {
        /* fanVal */
        if (!ObjectUtils.isEmpty(mouldVal.getFanVal())) {
            Map<String, SeaFacilityCommon> fieldSeaMap = getFieldSeaMap(fieldNum);
            SeaFacilityCommon seaFacility = fieldSeaMap.get(mouldVal.getFanVal());
            if (seaFacility != null) {
                mouldVal.setFanValName(seaFacility.getName());
            }
        }

        /* refVal */
        MouldVal existMouldVal = navMouldMapper.getNavMouldValue(mouldVal.getNavId(), mouldVal.getMouldId());
        if (!ObjectUtils.isEmpty(mouldVal.getRefVal())) {
            String[] arr = mouldVal.getRefVal().split("#@#");
            ParamVo newParamVo = new ParamVo(arr[0], arr[1]);

            List<ParamVo> existParamVos = null;
            if (existMouldVal != null) {
                existParamVos = JSON.parseArray(existMouldVal.getRefVal(), ParamVo.class);
            }

            if (ObjectUtils.isEmpty(existParamVos)) {
                existParamVos = new ArrayList<>();
            }
            if (existParamVos.contains(newParamVo)) {
                existParamVos.remove(newParamVo);
            }
            existParamVos.add(newParamVo);

            fillRefName(existParamVos);
            mouldVal.setRefVal(JSON.toJSONString(existParamVos));
        }
        navMouldMapper.setMouldValue(mouldVal);
    }

    private void fillRefName(List<ParamVo> vos) {
        if (ObjectUtils.isEmpty(vos)) {
            return;
        }

        for (ParamVo vo : vos) {
            /* 修复大纲引用重复异常问题时，refId加了navId，这里需要移除 */
            String refId = vo.getRefId().contains("_") ? vo.getRefId().split("_")[1] : vo.getRefId();
            String refName = mouldService.getRefNameByRefId(Integer.parseInt(refId.split("-")[1]), refId);
            vo.setRefContent(refName == null ? "警告：引用内容已删除" : refName);
        }
    }

    private Map<String, SeaFacilityCommon> getFieldSeaMap(String fieldNum) {
        Map<String, SeaFacilityCommon> map = new HashMap<>();
        List<SeaFacilityCommon> fieldSeaFacilities = windService.allSeaFacility(fieldNum);
        for (SeaFacilityCommon seaFacility : fieldSeaFacilities) {
            map.put(seaFacility.getId(), seaFacility);
        }
        return map;
    }

    /**
     * 获取导航下所有模板
     */
    public List<Mould> getMouldByNav(String fieldNum, Integer navId) {
        List<Mould> moulds = outlineMapper.getMouldByNavId(navId);
        for (Mould mould : moulds) {
            MouldVal navMouldValue = navMouldMapper.getNavMouldValue(navId, mould.getId());
            if (navMouldValue == null) {
                navMouldValue = new MouldVal();
            }

            mould.setFanVal(navMouldValue.getFanVal());
            mould.setFanValName(navMouldValue.getFanValName());

            if (!ObjectUtils.isEmpty(navMouldValue.getRefVal())) {
                mould.setRefValArr(JSON.parseArray(navMouldValue.getRefVal(), ParamVo.class));
            }

            boolean b = isExistMultiVar(fieldNum, mould.getContent());
            if (b && ObjectUtils.isEmpty(navMouldValue.getFanVal())) {
                mould.setIsWarning(true);
            }
        }
        return moulds;
    }

    /**
     * 判断模板是否含有复用变量
     */
    private boolean isExistMultiVar(String fieldNum, String content) {
        if (ObjectUtils.isEmpty(content)) {
            return false;
        }

        Document doc = Jsoup.parse(content);

        /* 复用变量 */
        List<Element> multiElements = JsoupUtil.getElementsByAttr(doc, "data-type", "multi");
        if (!ObjectUtils.isEmpty(multiElements)) {
            return true;
        }

        /* 分量 */
        List<Element> attrs = JsoupUtil.getElementsByAttr(doc, "data-type", "attr");
        for (Element ele : attrs) {
            String json = ele.attr("data-json");
            if (!ObjectUtils.isEmpty(json)) {
                String seaType = JSON.parseObject(json).getString("seaType");
                if (!ObjectUtils.isEmpty(seaType) && !"所有风机".equals(seaType) && !"典型风机".equals(seaType) && !"非典型风机".equals(seaType)) {
                    /* 此处也包含其他类型：
                    比如【升压站】
                    其他类型名称去查询时返回的var为空
                    结果都是返回告警，但是有其他类型去要考虑
                    */
                    /* 全局自定义变量不需要绑定设施，剔除全局变量 */
                    ReportVar var = varMapper.getPointIdsByVarName(fieldNum, seaType);
                    if (var == null) {
                        // 变量不存在，告警（导入的模版，没有变量）
                        return true;
                    } else if (!Objects.equals("global", var.getType())) {
                        return true;
                    }
                }
            }
        }

        /* 表格 */
        List<Element> tables = JsoupUtil.getElementsByAttr(doc, "data-type", "table");
        for (Element ele : tables) {
            String json = ele.attr("data-json");
            if (!ObjectUtils.isEmpty(json)) {
                String customVar = JSON.parseObject(json).getString("customVar");
                if (!ObjectUtils.isEmpty(customVar)) {
                    return true;
                }
            }
        }

        /* 过程线 */
        List<Element> lines = JsoupUtil.getElementsByAttr(doc, "data-type", "line");
        for (Element ele : lines) {
            String json = ele.attr("data-json");
            if (!ObjectUtils.isEmpty(json)) {
                String seaType = JSON.parseObject(json).getString("seaType");
                if (!ObjectUtils.isEmpty(seaType) && ("@{风机编号}".equals(seaType) || "@{升压站}".equals(seaType))) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 大纲生成PDF
     */
    public void genPdf(String fieldNum, Integer outlineId, HttpServletResponse response) throws Exception {
        /* 需要用到的数据 */
        List<SeaFacilityCommon> fieldSeaFacility = windService.allSeaFacility(fieldNum);
        List<PointCommon> fieldPoints = monitorService.getPointsByFieldNum(fieldNum);
        List<AttrCommon> fieldAttrs = monitorService.allAttrByField(fieldNum);
        Map<Integer, AttrCommon> fieldAttrMap = fieldAttrs.stream().collect(Collectors.toMap(AttrCommon::getId, Function.identity()));

        // 获取该大纲下所有导航树
        List<ReportOutlineNav> navTree = this.nav(fieldNum, outlineId);

        //根节点生成序号
        for (int i = 0; i < navTree.size(); i++) {
            ReportOutlineNav nav = navTree.get(i);
            String parentSeqNum = i + 1 + "";
            nav.setSeqNum(parentSeqNum);
            recursiveLoopCreateSeqNum(nav.getChildren(), parentSeqNum);
        }

        AtomicInteger level = new AtomicInteger(1);
        List<ContentVo> navContents = new ArrayList<>();
        iteratorNavTree(navTree, navContents, level, fieldSeaFacility, fieldPoints, fieldAttrMap, fieldNum);

        System.out.println("-----------");
        for (ContentVo navContent : navContents) {
            System.out.println(navContent);
        }

        try {
            String outPath = "/home/<USER>";
            EasyWord.html2word(null, navContents, "/home/<USER>", outPath, "XXXXXXX", new Date());
            FileUtil.outputFile(response, outPath);
            System.out.println("下载success");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    void recursiveLoopCreateSeqNum(List<ReportOutlineNav> treeList, String parentSeqNum) {
        if (null == treeList || treeList.size() < 1) {
            return;
        }
        for (int i = 0; i < treeList.size(); i++) {
            ReportOutlineNav treeVO = treeList.get(i);
            String childSeqNum = parentSeqNum + "." + (i + 1);
            treeVO.setSeqNum(childSeqNum);
            recursiveLoopCreateSeqNum(treeVO.getChildren(), childSeqNum);
        }
    }

    /**
     * 遍历导航树
     */
    private void iteratorNavTree(List<ReportOutlineNav> navTree, List<ContentVo> navContents, AtomicInteger level,
                                 List<SeaFacilityCommon> fieldSeaFacility, List<PointCommon> fieldPoints, Map<Integer, AttrCommon> fieldAttrMap, String fieldNum) throws Exception {
        for (ReportOutlineNav nav : navTree) {
            List<Mould> moulds = outlineMapper.getMouldByNavId(nav.getBid());
            if (ObjectUtils.isEmpty(moulds)) {
                navContents.add(new ContentVo(nav.getName(), level.get(), nav.getSeqNum(), ""));
            } else {
                for (Mould mould : moulds) {
                    /* 替换复用风机编号 */
                    String seaName = null;
                    String seaId = outlineMapper.getMouldValue(Integer.parseInt(nav.getBid()), mould.getId());
                    List<SeaFacilityCommon> res = fieldSeaFacility.stream().filter(e -> e.getId().equals(seaId)).collect(Collectors.toList());
                    if (!ObjectUtils.isEmpty(res)) {
                        seaName = res.get(0).getName();
                    }

                    /* 替换复用变量 */
                    replaceMultiVar(mould, seaId, fieldSeaFacility, fieldPoints, fieldAttrMap, fieldNum);

                    navContents.add(new ContentVo(nav.getName(), level.get(), nav.getSeqNum(), mould.getContent(), seaName));
                }
            }

            if (!ObjectUtils.isEmpty(nav.getChildren())) {
                level.incrementAndGet();
                iteratorNavTree(nav.getChildren(), navContents, level, fieldSeaFacility, fieldPoints, fieldAttrMap, fieldNum);
            }
        }
    }

    /**
     * 替换复用变量
     */
    private void replaceMultiVar(Mould mould, String seaId, List<SeaFacilityCommon> fieldSeaFacility, List<PointCommon> fieldPoints, Map<Integer, AttrCommon> fieldAttrMap, String fieldNum) throws Exception {
        String htmlStr = mould.getContent();
        if (ObjectUtils.isEmpty(htmlStr)) {
            return;
        }

        Document document = Jsoup.parse(htmlStr);
        Elements dataMsgElements = document.getElementsByAttribute("data-json");
        if (ObjectUtils.isEmpty(dataMsgElements)) {
            return;
        }

        for (Element dataMsgElement : dataMsgElements) {
            String varJson = dataMsgElement.attr("data-json");
            if (ObjectUtils.isEmpty(varJson)) {
                continue;
            }
            String dateType = dataMsgElement.attr("data-type");
            String res = varService.getDataByVar(varJson, dateType, "2022-08-01 00:00:00", "2022-08-31 23:59:59", seaId, fieldSeaFacility, fieldPoints, fieldAttrMap, Collections.emptyMap(), fieldNum);
            // 返回很多个图片的本地地址
            String attr = dataMsgElement.attr("data-paths");
            if (ObjectUtils.isEmpty(attr)) {
                String replace = mould.getContent().replace(dataMsgElement.text(), res);
                mould.setContent(replace);
            } else {
                String replace = mould.getContent().replace("@{img-paths}", res);
                mould.setContent(replace);
            }
        }
    }

    /**
     * 获取预制大纲导航树
     */
    private List<OutlineNav> getPreOutlineNavs(Integer outlineId, int startNavId, List<SeaFacilityCommon> typicalFans) {
        int pid = 0;
        List<OutlineNav> navs = new ArrayList<>();

        /* 第1章 */
        navs.add(new OutlineNav(startNavId++, "工程概况", 0, outlineId));

        /* 第2章 */
        navs.add(new OutlineNav(startNavId++, "监测概况", 0, outlineId));
        pid = startNavId - 1;
        navs.add(new OutlineNav(startNavId++, "单桩基础风机监测布置", pid, outlineId));
        navs.add(new OutlineNav(startNavId++, "海上升压站监测布置", pid, outlineId));

        /* 第3章 */
        navs.add(new OutlineNav(startNavId++, "监测系统运行及维护情况", 0, outlineId));

        /* 第4章 */
        navs.add(new OutlineNav(startNavId++, "监测数据分析", 0, outlineId));
        pid = startNavId - 1;
        for (SeaFacilityCommon typicalFan : typicalFans) {
            navs.add(new OutlineNav(startNavId++, typicalFan.getName() + "监测数据分析", pid, outlineId));
        }
        navs.add(new OutlineNav(startNavId++, "非典型风机倾斜数据分析", pid, outlineId));
        navs.add(new OutlineNav(startNavId++, "海上升压站监测数据分析", pid, outlineId));

        /* 第5章 */
        navs.add(new OutlineNav(startNavId++, "结论", 0, outlineId));
        pid = startNavId - 1;
        navs.add(new OutlineNav(startNavId++, "典型监测风机", pid, outlineId));
        navs.add(new OutlineNav(startNavId++, "非典型监测风机", pid, outlineId));
        navs.add(new OutlineNav(startNavId++, "海上升压站", pid, outlineId));

        /* 第6章 */
        navs.add(new OutlineNav(startNavId++, "附图及附表", 0, outlineId));
        pid = startNavId - 1;
        navs.add(new OutlineNav(startNavId++, "风机", pid, outlineId));
        int sPid = startNavId - 1;
        navs.add(new OutlineNav(startNavId++, "风机监测布置图", sPid, outlineId));
        navs.add(new OutlineNav(startNavId++, "风机基础及塔筒倾斜角度过程线", sPid, outlineId));
        navs.add(new OutlineNav(startNavId++, "倾斜监测成果统计表", sPid, outlineId));
        navs.add(new OutlineNav(startNavId++, "风机振动加速度有效值过程线", sPid, outlineId));
        navs.add(new OutlineNav(startNavId++, "风机振动频谱图", sPid, outlineId));
        navs.add(new OutlineNav(startNavId++, "振动监测成果统计表", sPid, outlineId));

        navs.add(new OutlineNav(startNavId++, "海上升压站", pid, outlineId));
        sPid = startNavId - 1;
        navs.add(new OutlineNav(startNavId++, "海上升压站监测布置图", sPid, outlineId));
        navs.add(new OutlineNav(startNavId++, "海上升压站倾斜角度过程线", sPid, outlineId));
        navs.add(new OutlineNav(startNavId++, "海上升压站倾斜角度过程线", sPid, outlineId));
        navs.add(new OutlineNav(startNavId++, "海上升压站振动加速度有效值过程线", sPid, outlineId));
        navs.add(new OutlineNav(startNavId++, "海上升压站振动频谱图", sPid, outlineId));
        navs.add(new OutlineNav(startNavId++, "海上升压站振动频谱图", sPid, outlineId));

        /* 设置排序 */
        for (int i = 0; i < navs.size(); i++) {
            navs.get(i).setOrder(i + 1);
        }
        return navs;
    }

    /**
     * 获取预制大纲导航树
     */
    private List<OutlineNav> getPreOutlineNavs2(Integer outlineId, int startNavId, String[] vars, List<SeaFacilityCommon> typicalFans, List<SeaFacilityCommon> nonTypicalFans) {
        int pid = 0;
        List<OutlineNav> navs = new ArrayList<>();

        /* 第1章 */
        navs.add(new OutlineNav(startNavId++, "工程概况", 0, outlineId));

        /* 第2章 */
        navs.add(new OutlineNav(startNavId++, "监测布置概况", 0, outlineId));
        pid = startNavId - 1;
        navs.add(new OutlineNav(startNavId++, "海上升压站监测", pid, outlineId));
        navs.add(new OutlineNav(startNavId++, "风机基础及上部塔架监测", pid, outlineId));
        navs.add(new OutlineNav(startNavId++, "安全监测自动化系统", pid, outlineId));
        navs.add(new OutlineNav(startNavId++, "测点布置详情", pid, outlineId));

        /* 第3章 */
        navs.add(new OutlineNav(startNavId++, "监测系统运行及维护情况", 0, outlineId));

        /* 第4章 */
        navs.add(new OutlineNav(startNavId++, "监测数据分析", 0, outlineId));
        pid = startNavId - 1;
        for (SeaFacilityCommon typicalFan : typicalFans) {
            navs.add(new OutlineNav(startNavId++, typicalFan.getName() + "典型机位监测数据分析", pid, outlineId));
            int sPid = startNavId - 1;
            for (String s : vars) {
                navs.add(new OutlineNav(startNavId++, typicalFan.getName() + s, sPid, outlineId));
            }
        }
        for (SeaFacilityCommon nonTypicalFan : nonTypicalFans) {
            navs.add(new OutlineNav(startNavId++, nonTypicalFan.getName() + "非典型机位监测数据分析", pid, outlineId));
            int sPid = startNavId - 1;
            navs.add(new OutlineNav(startNavId++, nonTypicalFan.getName() + "机位倾斜", sPid, outlineId));
            navs.add(new OutlineNav(startNavId++, nonTypicalFan.getName() + "机位振动", sPid, outlineId));
        }
        navs.add(new OutlineNav(startNavId++, "海上升压站监测数据分析", pid, outlineId));
        pid = startNavId - 1;
        navs.add(new OutlineNav(startNavId++, "海上升压站不均匀沉降", pid, outlineId));
        navs.add(new OutlineNav(startNavId++, "海上升压站倾斜", pid, outlineId));
        navs.add(new OutlineNav(startNavId++, "海上升压站振动", pid, outlineId));
        navs.add(new OutlineNav(startNavId++, "海上升压站应力应变", pid, outlineId));
        navs.add(new OutlineNav(startNavId++, "海上升压站防腐蚀", pid, outlineId));

        /* 第5章 */
        navs.add(new OutlineNav(startNavId++, "结论", 0, outlineId));
        pid = startNavId - 1;
        navs.add(new OutlineNav(startNavId++, "风机基础及其上部塔架监测", pid, outlineId));
        navs.add(new OutlineNav(startNavId++, "海上升压站监测", pid, outlineId));

        /* 第6章 */
        navs.add(new OutlineNav(startNavId++, "附图及附表", 0, outlineId));
        pid = startNavId - 1;
        navs.add(new OutlineNav(startNavId++, "风机及升压站监测布置图", pid, outlineId));
        int sPid = startNavId - 1;
        navs.add(new OutlineNav(startNavId++, "风机监测布置图", sPid, outlineId));
        navs.add(new OutlineNav(startNavId++, "海上升压站监测布置图", sPid, outlineId));


        navs.add(new OutlineNav(startNavId++, "典型机位", pid, outlineId));
        sPid = startNavId - 1;
        navs.add(new OutlineNav(startNavId++, "风机倾斜监测成果统计表", sPid, outlineId));
        navs.add(new OutlineNav(startNavId++, "风机振动监测成果统计表", sPid, outlineId));
        navs.add(new OutlineNav(startNavId++, "风机应力应变监测成果统计表", sPid, outlineId));

        navs.add(new OutlineNav(startNavId++, "非典型机位", pid, outlineId));
        sPid = startNavId - 1;
        navs.add(new OutlineNav(startNavId++, "风机倾斜角度过程线", sPid, outlineId));
        navs.add(new OutlineNav(startNavId++, "风机倾斜监测成果统计表", sPid, outlineId));
        navs.add(new OutlineNav(startNavId++, "风机振动加速度有效值过程线", sPid, outlineId));
        navs.add(new OutlineNav(startNavId++, "风机振动频谱图", sPid, outlineId));
        navs.add(new OutlineNav(startNavId++, "风机振动监测成果统计表", sPid, outlineId));

        navs.add(new OutlineNav(startNavId++, "海上升压站", pid, outlineId));
        sPid = startNavId - 1;
        navs.add(new OutlineNav(startNavId++, "不均匀沉降监测成果统计表", sPid, outlineId));
        navs.add(new OutlineNav(startNavId++, "倾斜监测成果统计表", sPid, outlineId));
        navs.add(new OutlineNav(startNavId++, "振动监测成果统计表", sPid, outlineId));
        navs.add(new OutlineNav(startNavId++, "应力应变监测成果统计表", sPid, outlineId));

        /* 设置排序 */
        for (int i = 0; i < navs.size(); i++) {
            navs.get(i).setOrder(i + 1);
        }
        return navs;
    }

    /**
     * 获取预制大纲导航树
     */
    private List<OutlineNav> getPreOutlineNavs3(Integer outlineId, int startNavId, String[] vars, List<SeaFacilityCommon> seaFacilities, List<SeaFacilityCommon> typicalFans, List<SeaFacilityCommon> nonTypicalFans) {
        int pid = 0;
        List<OutlineNav> navs = new ArrayList<>();

        /* 第1章 */
        navs.add(new OutlineNav(startNavId++, "工程概况", 0, outlineId));

        /* 第2章 */
        navs.add(new OutlineNav(startNavId++, "监测概况", 0, outlineId));
        Set<String> basicForms = seaFacilities.stream().filter(e -> !ObjectUtils.isEmpty(e.getBasicForm())).map(SeaFacilityCommon::getBasicForm).collect(Collectors.toSet());
        Set<String> typicalBasicForms = typicalFans.stream().filter(e -> !ObjectUtils.isEmpty(e.getBasicForm())).map(SeaFacilityCommon::getBasicForm).collect(Collectors.toSet());
        Set<String> nonTypicalBasicForms = nonTypicalFans.stream().filter(e -> !ObjectUtils.isEmpty(e.getBasicForm())).map(SeaFacilityCommon::getBasicForm).collect(Collectors.toSet());
        pid = startNavId - 1;
        for (String basicForm : basicForms) {
            navs.add(new OutlineNav(startNavId++, basicForm + "风机监测布置", pid, outlineId));
        }
        navs.add(new OutlineNav(startNavId++, "海上升压站监测布置", pid, outlineId));

        /* 第3章 */
        navs.add(new OutlineNav(startNavId++, "监测系统运行及维护情况", 0, outlineId));

        /* 第4章 */
        navs.add(new OutlineNav(startNavId++, "监测数据分析", 0, outlineId));
        pid = startNavId - 1;
        for (SeaFacilityCommon typicalFan : typicalFans) {
            navs.add(new OutlineNav(startNavId++, typicalFan.getName() + ffff(typicalFan.getBasicForm()) + "风机监测数据分析", pid, outlineId));
            int sPid = startNavId - 1;
            for (String s : vars) {
                navs.add(new OutlineNav(startNavId++, typicalFan.getName() + s, sPid, outlineId));
            }
        }
        for (String nonTypicalBasicForm : nonTypicalBasicForms) {
            navs.add(new OutlineNav(startNavId++, "非典型" + nonTypicalBasicForm + "风机监测数据分析", pid, outlineId));
            int sPid = startNavId - 1;
            navs.add(new OutlineNav(startNavId++, "非典型" + nonTypicalBasicForm + "风机倾斜", sPid, outlineId));
            navs.add(new OutlineNav(startNavId++, "非典型" + nonTypicalBasicForm + "风机振动", sPid, outlineId));
        }

//        for (SeaFacilityCommon nonTypicalFan : nonTypicalFans) {
//            navs.add(new OutlineNav(startNavId++, "非典型" + nonTypicalFan.getName() + "风机监测数据分析", pid, outlineId));
//            int sPid = startNavId - 1;
//            for (String s : vars) {
//                navs.add(new OutlineNav(startNavId++, nonTypicalFan.getName() + s, sPid, outlineId));
//            }
//        }

        navs.add(new OutlineNav(startNavId++, "海上升压站监测数据分析", pid, outlineId));
        pid = startNavId - 1;
        navs.add(new OutlineNav(startNavId++, "海上升压站不均匀沉降", pid, outlineId));
        navs.add(new OutlineNav(startNavId++, "海上升压站倾斜", pid, outlineId));
        navs.add(new OutlineNav(startNavId++, "海上升压站振动", pid, outlineId));
        navs.add(new OutlineNav(startNavId++, "海上升压站应力应变", pid, outlineId));
        navs.add(new OutlineNav(startNavId++, "海上升压站防腐蚀", pid, outlineId));

        /* 第5章 */
        navs.add(new OutlineNav(startNavId++, "结论", 0, outlineId));
        pid = startNavId - 1;
        for (String basicForm : basicForms) {
            navs.add(new OutlineNav(startNavId++, basicForm + "风机", pid, outlineId));
            int sPid = startNavId - 1;
            for (String s : vars) {
                navs.add(new OutlineNav(startNavId++, s, sPid, outlineId));
            }
        }
        navs.add(new OutlineNav(startNavId++, "海上升压站", pid, outlineId));

        /* 第6章 */
        navs.add(new OutlineNav(startNavId++, "附图及附表", 0, outlineId));
        pid = startNavId - 1;
        navs.add(new OutlineNav(startNavId++, "风机及升压站监测布置图", pid, outlineId));
        int sPid = startNavId - 1;
        navs.add(new OutlineNav(startNavId++, "风机监测布置图", sPid, outlineId));
        navs.add(new OutlineNav(startNavId++, "海上升压站监测布置图", sPid, outlineId));

        for (String basicForm : typicalBasicForms) {
            navs.add(new OutlineNav(startNavId++, "典型" + basicForm + "风机", pid, outlineId));
            sPid = startNavId - 1;
            navs.add(new OutlineNav(startNavId++, "风机倾斜监测成果统计表", sPid, outlineId));
            navs.add(new OutlineNav(startNavId++, "风机振动监测成果统计表", sPid, outlineId));
            navs.add(new OutlineNav(startNavId++, "风机应力应变监测成果统计表", sPid, outlineId));
            navs.add(new OutlineNav(startNavId++, "风机防腐蚀监测成果统计表", sPid, outlineId));
        }
        for (String basicForm : nonTypicalBasicForms) {
            navs.add(new OutlineNav(startNavId++, "非典型" + basicForm + "风机", pid, outlineId));
            sPid = startNavId - 1;
            navs.add(new OutlineNav(startNavId++, "风机倾斜角度过程线", sPid, outlineId));
            navs.add(new OutlineNav(startNavId++, "风机倾斜监测成果统计表", sPid, outlineId));
            navs.add(new OutlineNav(startNavId++, "风机振动加速度有效值过程线", sPid, outlineId));
            navs.add(new OutlineNav(startNavId++, "风机振动监测成果统计表", sPid, outlineId));
        }

        /* 设置排序 */
        for (int i = 0; i < navs.size(); i++) {
            navs.get(i).setOrder(i + 1);
        }
        return navs;
    }

    private String ffff(String basicForm) {
        if (ObjectUtils.isEmpty(basicForm)) {
            return "未配置基础型式";
        }
        return basicForm;
    }

    /**
     * 风场下所有大纲
     */
    public List<Outline> listByField(String fieldNum) {
        return outlineMapper.listByField(fieldNum);
    }

    /**
     * 导入大纲导航
     */
    public R importOutlineNav(String fieldNum, Integer curOutlineId, Integer selectedOutlineId) {
        List<OutlineNav> navs = outlineMapper.navList(selectedOutlineId);
        if (ObjectUtils.isEmpty(navs)) {
            return R.error("目标大纲下没有目录");
        }

        int startNavId = outlineMapper.getMaxNavId() + 1;
        int diff = startNavId - navs.get(0).getId();

        for (OutlineNav nav : navs) {
            nav.setOutlineId(curOutlineId);
            nav.setId(nav.getId() + diff);
            if (nav.getPid() != 0) {
                nav.setPid(nav.getPid() + diff);
            }
        }
        outlineMapper.deleteNavByOutline(curOutlineId);
        outlineMapper.addNavs(fieldNum, navs);
        return R.success("导入成功");
    }

    /**
     * 获取某大纲导航树告警情况
     */
    public Set<Integer> navWarn(String fieldNum, Integer outlineId) {
        /* 获取该大纲下所有导航和模板 */
        Map<Integer, List<Mould>> navMouldContentsMap = getNavMouldContentsByOutline(outlineId);

        /* 获取该大纲下海上设施绑定关系 */
        Map<Integer, List<MouldVal>> navMouldsMap = navMouldMapper.list(outlineId).stream().collect(Collectors.groupingBy(MouldVal::getNavId));

        Set<Integer> warnNavIds = new HashSet<>();
        navMouldContentsMap.forEach((navId, moulds) -> {
            List<MouldVal> mouldValues = navMouldsMap.get(navId);
            if (!ObjectUtils.isEmpty(mouldValues)) {
                Map<Integer, String> mouldsMap = moulds.stream().collect(Collectors.toMap(Mould::getId, Mould::getContent, (key1, key2) -> key1));
                for (MouldVal mouldValue : mouldValues) {
                    String content = mouldsMap.get(mouldValue.getMouldId());
                    if (isExistMultiVar(fieldNum, content) && ObjectUtils.isEmpty(mouldValue.getFanVal())) {
                        warnNavIds.add(navId);
                    }
                }
            }
        });
        return warnNavIds;
    }

    /**
     * 获取大纲下所有模板
     */
    private Map<Integer, List<Mould>> getNavMouldContentsByOutline(Integer outlineId) {
        List<Mould> moulds = outlineMapper.getNavMouldContentsByOutline(outlineId);
        return moulds.stream().collect(Collectors.groupingBy(Mould::getNavId));
    }

    /**
     * 复制大纲
     */
    @Transactional(rollbackFor = Exception.class)
    public void copy(String fieldNum, Outline outline, String sessionId) {
        Integer id = outline.getId();
        if (id == null || StringUtils.isEmpty(fieldNum) || StringUtils.isEmpty(sessionId)) {
            throw new RuntimeException("复制大纲失败，id为空");
        }
        /* 查询大纲信息 */
        Outline old = outlineMapper.getById(id);
        if (old == null) {
            throw new RuntimeException("复制大纲失败，大纲不存在");
        }

        /*  复制大纲  */
        Outline newOutline = new Outline();
        newOutline.setName(outline.getName());
        newOutline.setType(old.getType());

        newOutline.setFieldNum(fieldNum);
        /* 设置创建人信息 */
        ResourceCommon resource = (ResourceCommon) redisTemplate.opsForValue().get(sessionId);
        if (resource != null) {
            newOutline.setUserId(resource.getUserId());
            newOutline.setUsername(resource.getUsername());
        }else {
            throw new RuntimeException("复制大纲失败，用户信息不存在");
        }
        outlineMapper.add(newOutline);

        /*  复制大纲标题  */
        List<OutlineNav> navs = outlineMapper.navList(id);
        Map<Integer, Integer> navIdMap = new HashMap<>();
        navIdMap.put(0, 0);
        List<Integer> pids = new ArrayList<>();
        pids.add(0);
        while (!pids.isEmpty()) {
            List<Integer> nextPid = new ArrayList<>();
            for (Integer pid : pids) {
                List<OutlineNav> navList = navs.stream().filter(item -> item != null
                                && item.getPid() != null && Objects.equals(item.getPid(), pid))
                        .collect(Collectors.toList());
                for (OutlineNav nav : navList) {
                    nextPid.add(nav.getId());
                    Integer newPid = navIdMap.get(nav.getPid());
                    if (newPid == null) {
                        continue;
                    }
                    OutlineNav newNav = new OutlineNav();
                    newNav.setName(nav.getName());
                    newNav.setPid(newPid);
                    newNav.setOutlineId(newOutline.getId());
                    newNav.setOrder(nav.getOrder());
                    newNav.setFieldNum(fieldNum);
                    outlineMapper.addNav(fieldNum, newNav);
                    navIdMap.put(nav.getId(), newNav.getId());
                }
            }
            pids = nextPid;
        }

        /*  复制标题模版  */
        List<MouldVal> mouldList = navMouldMapper.list(id);
        mouldList.forEach(item -> {
            Integer newId = navIdMap.get(item.getNavId());
            if (newId != null) {
                item.setNavId(newId);
                if (item.getRefVal() != null) {
                    List<ParamVo> params = JSON.parseArray(item.getRefVal(), ParamVo.class);
                    List<ParamVo> newParams = new ArrayList<>();
                    for (ParamVo param : params) {
                        String refId = param.getRefId();
                        Matcher matcher = Pattern.compile("^(\\d+)_u").matcher(refId);
                        if (matcher.find()) {
                            Integer paramRefId = Integer.parseInt(matcher.group(1));
                            Integer newRefId = navIdMap.get(paramRefId);
                            if (newRefId != null) {
                                refId = matcher.replaceFirst(newRefId + "_u");
                                param.setRefId(refId);
                                newParams.add(param);
                            }
                        }
                    }
                    item.setRefVal(JSON.toJSONString(newParams));
                }
                navMouldMapper.addNavMould(item);
            }
        });
    }
}

//    private void setWarning2(Integer outlineId, List<OutlineNav> navList) {
//        Map<Integer, List<MouldVal>> navMouldsMap = navMouldMapper.list(outlineId).stream().collect(Collectors.groupingBy(MouldVal::getNavId));
//        for (OutlineNav nav : navList) {
//            List<MouldVal> navMoulds = navMouldsMap.get(nav.getId());
//            if (!ObjectUtils.isEmpty(navMoulds)) {
//                continue;
//            }
//            for (MouldVal mould : navMoulds) {
//                if ("".equals(navMould.getFanVal())) {
//                    nav.setIsWarning(true);
//                    continue;
//                }
//
//                boolean b = isExistMultiVar(mould.getContent());
//                if (b && ObjectUtils.isEmpty(navMouldValue.getFanVal())) {
//                    mould.setIsWarning(true);
//                }
//            }
//        }
//    }














