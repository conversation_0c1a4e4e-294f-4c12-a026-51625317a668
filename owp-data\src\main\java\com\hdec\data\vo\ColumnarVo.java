package com.hdec.data.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * @date 2023/8/2
 */
@Data
@ToString
@NoArgsConstructor
public class ColumnarVo {

    private String seaName;

    private Float a;

    private Float b;

    public ColumnarVo(String seaName, Float a, Float b) {
        this.seaName = seaName;
        this.a = a;
        this.b = b;
    }

    public ColumnarVo(String seaName) {
        this.seaName = seaName;
    }

    public Float getA() {
        if (a == null) {
            return 0f;
        }
        return a;
    }

    public Float getB() {
        if (b == null) {
            return 0f;
        }
        return b;
    }
}
