package com.hdec.data.service.impl;

import com.hdec.common.constant.Constant;
import com.hdec.common.domain.AttrCommon;
import com.hdec.common.domain.PointCommon;
import com.hdec.common.util.TimeUtil;
import com.hdec.common.vo.Direct;
import com.hdec.common.vo.InstDirectAttr;
import com.hdec.data.cache.LocalCacheManager;
import com.hdec.data.domain.taos.Record;
import com.hdec.data.feign.MonitorService;
import com.hdec.data.mapper.TaosMapper;
import com.hdec.data.service.SimpleService;
import com.hdec.data.service.TaosService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


@Service
public class SimpleServiceImpl implements SimpleService {

    private static final Logger logger = LoggerFactory.getLogger(SimpleServiceImpl.class);

    @Resource
    private LocalCacheManager localCacheManager;

    @Resource
    private TaosMapper taosMapper;

    @Resource
    private TaosService taosService;

    @Resource
    private MonitorService monitorService;

    @Override
    public void doHighSample(List<Integer> pointIds, Date startTime, Date endTime) {
        long start = System.currentTimeMillis();
        List<PointCommon> points = pointIds.stream().filter(Objects::nonNull)
                .map(e -> localCacheManager.getPointCache(e))
                .filter(Objects::nonNull).filter(e -> e.getInstId() != null)
                .collect(Collectors.toList());
        Map<Integer, List<PointCommon>> instPointMap = points.stream()
                .collect(Collectors.groupingBy(PointCommon::getInstId));
        instPointMap.forEach((instId, instPoints) -> {
            if (instPoints == null || instPoints.isEmpty()) {
                return;
            }
            Integer pointId = instPoints.get(0).getId();
            /*  查询分量信息  */
            InstDirectAttr instDirectAttr = monitorService.getInstAttrsByRate(pointId, Constant.RATE_HIGH);
            List<AttrCommon> rateAttrs = instDirectAttr.getAttrs();
            Integer[] instDirects = instDirectAttr.getDirects().stream()
                    .map(Direct::getDirectId)
                    .toArray(Integer[]::new);
            /*  创建抽稀表  */
            taosService.initializeHighSampleSTable(instId, rateAttrs, instDirects);
            /*  插入抽稀记录  */
            instPoints.forEach(point -> {
                try {
                    insertSampleRecord(instId, point.getId(), rateAttrs, instDirects, startTime, endTime);
                } catch (Exception e) {
                    logger.error("插入抽稀记录失败,仪器:{},测点:{}", instId, point.getId(), e);
                }
            });
        });
        logger.info("抽稀完成,耗时:{}ms", System.currentTimeMillis() - start);
    }

    @Override
    public void insertSampleRecord(Integer instId, Integer pointId, List<AttrCommon> attrs, Integer[] directs,
                                   Date startTime, Date endTime) {
        /*  查询数据  */
        List<String> columns = new ArrayList<>();
        for (AttrCommon attr : attrs) {
            directs = attr.getIsPolar() ? new Integer[]{0} : directs;
            for (Integer direct : directs) {
                columns.add(taosService.enColName(attr.getId(), direct));
            }
        }
        String hdb = taosService.getRateDatabase(Constant.RATE_HIGH);
        String htn = taosService.getTableName(Constant.RATE_HIGH, instId, false);
        List<Record> records = taosMapper.selectSampleData(hdb, htn, pointId, columns, startTime, endTime, "1m");
        if (records == null || records.isEmpty()) {
            return;
        }

        /*  写入数据  */
        String stn = taosService.getTableName(Constant.RATE_SAMPLE, instId, true);
        StringBuilder insert = new StringBuilder();
        insert.append("insert into ").append(stn).append("_").append(pointId);
        insert.append("(ts,");
        for (String column : columns) {
            insert.append(column).append(", ");
        }
        insert.deleteCharAt(insert.length() - 2);
        insert.append(") using ").append(stn).append(" tags(").append(pointId).append(") values ");
        records.forEach(map -> {
            Date ts = map.getTs();
            insert.append("('").append(TimeUtil.format2Second(ts)).append("', ");
            for (String column : columns) {
                insert.append(map.get(column)).append(", ");
            }
            insert.deleteCharAt(insert.length() - 2);
            insert.append("), ");
        });
        taosMapper.insertSql(insert.toString());
    }
}
