package com.hdec.data.service;

import com.alibaba.fastjson.JSON;
import com.hdec.data.domain.BatchImportCfg;
import com.hdec.data.domain.ImportCfgParam;
import com.hdec.data.mapper.BatchImportMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.Iterator;
import java.util.List;

/**
 * 批量导入业务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class BatchImportService {

    @Autowired
    private BatchImportMapper importMapper;

    /**
     * 查找配置
     */
    public BatchImportCfg selectCfg(Integer type, String fieldNum) {
        BatchImportCfg cfg = importMapper.selectCfg(type, fieldNum);
        if (cfg != null && !ObjectUtils.isEmpty(cfg.getParam())) {
            cfg.setParams(JSON.parseArray(cfg.getParam(), ImportCfgParam.class));
            cfg.setParam(null);
        }
        return cfg;
    }

    /**
     * 保存配置
     */
    public void saveCfg(BatchImportCfg cfg) {
        /* 去除空参数 */
        Iterator<ImportCfgParam> it = cfg.getParams().iterator();
        while (it.hasNext()) {
            ImportCfgParam param = it.next();
            if (param.getInstId() == null || (param.getAttrId() == null && ObjectUtils.isEmpty(param.getAttrIds()))) {
                it.remove();
            }
        }
        cfg.setParam(JSON.toJSONString(cfg.getParams()));
        importMapper.saveCfg(cfg);
    }

    /**
     * 删除配置
     */
    public void deleteCfg(BatchImportCfg cfg) {
        importMapper.deleteCfg(cfg);
    }

    /**
     * 移除含有某分量的配置参数
     */
    public void removeParamByAttr(String fieldNum, Integer attrId) {
        List<BatchImportCfg> configs = importMapper.fieldCfg(fieldNum);
        if (ObjectUtils.isEmpty(configs)) {
            return;
        }

        /* 修改配置参数 */
        for (BatchImportCfg config : configs) {
            List<ImportCfgParam> params = JSON.parseArray(config.getParam(), ImportCfgParam.class);
            if (ObjectUtils.isEmpty(params)) {
                continue;
            }
            Iterator<ImportCfgParam> it = params.iterator();
            while (it.hasNext()) {
                ImportCfgParam param = it.next();
                if (param.getAttrId() != null && param.getAttrId().equals(attrId)) {
                    it.remove();
                }
                if (param.getAttrIds() != null) {
                    Iterator<Integer> iterator = param.getAttrIds().iterator();
                    while (iterator.hasNext()) {
                        if (iterator.next().equals(attrId)) {
                            iterator.remove();
                        }
                    }
                    if (ObjectUtils.isEmpty(param.getAttrIds())) {
                        it.remove();
                    }
                }
            }
            config.setParam(JSON.toJSONString(params));
        }

        /* 保存配置参数 */
        importMapper.updateList(configs);
    }

}
