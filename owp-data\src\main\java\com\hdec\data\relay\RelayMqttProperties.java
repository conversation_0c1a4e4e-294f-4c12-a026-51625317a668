package com.hdec.data.relay;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

@Data
@ConfigurationProperties(prefix = "relay.mqtt", ignoreUnknownFields = true)
public class RelayMqttProperties {

    private boolean enable = false;

    /**
     * MQTT broker
     */
    private String broker;

    /**
     * 用户名
     */
    private String username;

    /**
     * 客户端ID
     */
    private String clientId;

    /**
     * 密码
     */
    private String password;

    /**
     * 清除会话
     */
    private boolean cleanSession = true;

    /**
     * 连接超时时间
     */
    private int connectionTimeout = 10;

    /**
     * 保活
     */
    private int keepAliveInterval = 10;

    /**
     * 自动重连
     */
    private boolean automaticReconnect = true;
}
