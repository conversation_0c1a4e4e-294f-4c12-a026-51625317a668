package com.hdec.data.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hdec.common.util.MonitorUtil;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class DataLog {

    /** 自增主键 */
    private Integer id;

    private String rate;

    /** 测点ID */
    private Integer pointId;

    /** 测点编号 */
    private String pointNo;

    /** 方向编号 */
    private Integer direction;

    /** 方向名称 */
    private String directionName;

    /** 分量ID */
    private Integer attrId;

    /** 分量名称 */
    private String attrName;

    /** 操作时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date operateTime;

    /** 时间 */
    private String time;

    /** 修改前值 */
    private Float beforeVal;

    /** 修改后值 */
    private Float afterVal;

    /** 用户ID */
    private Integer userId;

    /** 用户名 */
    private String username;

    /** 类型（1：整编日志  2：删除日志） */
    private Integer type;

    /** 删除原因 */
    private String delReason;

    private String delDataJson;
    private String delData;

    /** 风场编码 */
    private String fieldNum;

    public String getDirectionName() {
        if (direction != null) {
            return MonitorUtil.transDirection(direction);
        }
        return null;
    }

    public DataLog(Integer pointId, Integer direction, Integer attrId, String time, Integer type, String delReason, String fieldNum) {
        this.pointId = pointId;
        this.direction = direction;
        this.attrId = attrId;
        this.time = time;
        this.type = type;
        this.delReason = delReason;
        this.fieldNum = fieldNum;
    }

    public DataLog(Integer pointId, Integer direction, Integer attrId, String time, Float beforeVal, Float afterVal, Integer type, String fieldNum) {
        this.pointId = pointId;
        this.direction = direction;
        this.attrId = attrId;
        this.time = time;
        this.beforeVal = beforeVal;
        this.afterVal = afterVal;
        this.type = type;
        this.fieldNum = fieldNum;
    }
}
