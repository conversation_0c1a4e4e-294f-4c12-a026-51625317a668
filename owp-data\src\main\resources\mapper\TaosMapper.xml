<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hdec.data.mapper.TaosMapper">
    <insert id="insertSql" parameterType="string">
        ${sql}
    </insert>
    <select id="selectSql" parameterType="string" resultType="com.hdec.data.domain.taos.Record">
        ${sql}
    </select>
    <update id="createDatabase">
        create database if not exists ${database}
    </update>

    <select id="showStables" resultType="string">
        show ${database}.stables
        <if test="table != null || table != ''">
            like '#{table}'
        </if>
    </select>

    <select id="tablesDescribe" resultType="com.hdec.data.domain.taos.TableColumn">
        <bind name="table_name" value=" database + '.' + table"/>
        describe ${table_name}
    </select>

    <update id="createStable">
        <bind name="table_name" value=" database + '.' + table"/>
        create stable if not exists ${table_name}
            <foreach collection="columns" item="column" open="(" close=")" separator=",">
                ${column.field} ${column.type}
            </foreach>
            tags
                <foreach collection="tags" item="tag"  open="(" close=")" separator=",">
                    ${tag.field} ${tag.type}
                </foreach>
    </update>

    <update id="addColumn">
        <bind name="table_name" value=" database + '.' + table"/>
        alter stable ${table_name} add column ${column} ${type}
    </update>

    <update id="deleteColumn">
        <bind name="table_name" value=" database + '.' + table"/>
        alter stable ${table_name} drop column ${column}
    </update>

    <update id="createDailyCountJobStable">
        <bind name="table_name" value=" database + '.daily_count'"/>
        create stable if not exists ${table_name}
        (
            ts         timestamp,
            data_count int
        ) TAGS ( point int)
    </update>

    <update id="createRedoJobStable">
        <bind name="table_name" value=" database + '.redo_job'"/>
        create
        stable if not exists ${table_name} (
           ts timestamp,
           status int,
           create_time timestamp
        ) TAGS (
            point int,
            type int
        )
    </update>

    <insert id="commitRedoJob">
        <bind name="table_name" value=" database + '.redo_job'"/>
        insert into
        <foreach collection="jobs" item="job" separator=" ">
            ${table_name}_${job.point}_${type}(ts, status, create_time) using ${table_name} tags (#{job.point},#{type})
            values (#{job.ts}, #{job.status}, now)
        </foreach>
    </insert>

    <select id="selectUndoTask" resultType="com.hdec.data.domain.RedoJob">
        <bind name="table_name" value=" database + '.redo_job'"/>
        select *
        from ${table_name}
        where type = #{type}
          and status = 0
        <if test="createInterval > 0">
            and timediff(now(), create_time, 1s) > #{createInterval}
        </if>
    </select>

    <insert id="completeRedoJob">
        <bind name="table_name" value=" database + '.redo_job'"/>
        insert into
        <foreach collection="jobs" item="job" separator=" ">
            ${table_name}_${job.point}_${type}(ts, status) using ${table_name} tags (#{job.point},#{type})
            values (#{job.ts}, #{job.status})
        </foreach>
    </insert>

    <select id="selectDailyCount" resultType="long">
        <bind name="table_name" value=" database+'.high_' + inst"/>
        select count(1) as data_count from ${table_name}
        where point = #{point} and ts >= #{startTime} and #{endTime} >ts
    </select>

    <insert id="insectDailyCount">
        <bind name="table_name" value=" database + '.daily_count'"/>
        insert into
        <foreach collection="dailyCounts" item="dailyCount" separator=" ">
            ${table_name}_${dailyCount.point} (ts, data_count) using ${table_name} tags (#{dailyCount.point})
            values (#{dailyCount.ts}, #{dailyCount.dataCount})
        </foreach>
    </insert>

    <select id="selectDataCount" resultType="long">
        <bind name="table_name" value=" database + '.daily_count'"/>
        select sum(data_count) data_count from ${table_name}
        where point in
        <foreach collection="points" item="point" open="(" close=")" separator=",">
            #{point}
        </foreach>
        and ts >= #{startTime} and #{endTime} > ts
    </select>

    <select id="selectSampleData" resultType="com.hdec.data.domain.taos.Record">
        <bind name="table_name" value=" database + '.' + table"/>
        select _wstart as ts,
        <foreach collection="columns" item="column" separator=",">
            first(${column}) as ${column}
        </foreach>
        from ${table_name} where point = #{point} and ts >= #{startTime} and #{endTime} > ts interval(${interval})
    </select>

    <select id="selectAttrVal" resultType="com.hdec.common.formula.AttrVal">
        select ts, ${column} as val
        from #{table}
        where point = #{point}
          and  ts >= #{startTime} and #{endTime} > ts
    </select>

    <select id="selectAttrValAtSameTime" resultType="com.hdec.common.formula.AttrVal">
        select ts, ${column} as val
        from #{table}
        where point = #{point}
          and ts = #{ts}
        limit 1
    </select>

    <select id="selectAttrValAtScopeTime" resultType="com.hdec.common.formula.AttrVal">
        select ts, ${column} as val, ABS((CAST(ts as BIGINT) - #{ts})) as diff
        from #{table}
        where point = #{point}
          and ts between #{startTime} and #{endTime}
        order by diff
        limit 1
    </select>
</mapper>