package com.hdec.data.mapper;

import com.hdec.data.domain.ReportBaseInfo;
import com.hdec.data.domain.ReportPerson;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 报告基本信息Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface ReportBaseInfoMapper {

    /**
     * 获取某项目报告基本信息
     */
    ReportBaseInfo getBaseInfo(@Param("fieldNum") String fieldNum);

    /**
     * 更新某项目报告基本信息
     */
    void updateBaseInfo(@Param("baseInfo") ReportBaseInfo baseInfo);

    /**
     * 保存某项目报告基本信息
     */
    void saveBaseInfo(@Param("baseInfo") ReportBaseInfo baseInfo);

    /**
     * 获取某项目人员信息
     */
    ReportPerson getPersonConf(@Param("fieldNum") String fieldNum);

    /**
     * 修改某项目人员信息
     */
    void updatePersonConf(@Param("person") ReportPerson reportPerson);

    /**
     * 保存某项目人员信息
     */
    void savePersonConf(@Param("person") ReportPerson reportPerson);
}
