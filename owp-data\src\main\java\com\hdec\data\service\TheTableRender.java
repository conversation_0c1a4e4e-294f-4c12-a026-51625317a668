package com.hdec.data.service;

import com.hdec.data.util.ElementRendererUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.TableRowAlign;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableCell;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.apache.xmlbeans.XmlCursor;
import org.ddr.poi.html.HtmlRenderContext;
import org.ddr.poi.html.tag.TableRenderer;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class TheTableRender extends TableRenderer {

    private static final Logger logger = LoggerFactory.getLogger(TheTableRender.class);

    public TheTableRender() {
        super();
    }

    @Override
    public boolean renderStart(Element element, HtmlRenderContext context) {
        autoFixTableImage(element);
        return super.renderStart(element, context);
    }

    /**
     * 改写渲染结束逻辑
     * 表格斜线
     */
    @Override
    public void renderEnd(Element element, HtmlRenderContext context) {
//        handleNextTabTitle(element, context);


        // 设置第一行作为标题行
        final XWPFTable table = context.getClosestTable();
        CTTblPr tblPr = table.getCTTbl().getTblPr();
        if (tblPr == null) {
            tblPr = table.getCTTbl().addNewTblPr();
        }
        CTTblLayoutType tblLayout = tblPr.addNewTblLayout();
        tblLayout.setType(STTblLayoutType.FIXED);

        XWPFTableRow headerRow = table.getRow(0);
        CTTrPr ctTrPr = headerRow.getCtRow().addNewTrPr();
        CTOnOff ctOnOff = ctTrPr.addNewTblHeader();
        ctOnOff.setVal(STOnOff.TRUE);


        handleBorder(element, context);
        super.renderEnd(element, context);
    }

    /**
     * 处理分页表头
     */
    private void handleNextTabTitle(Element element, HtmlRenderContext context) {
        final XWPFTable closestTable = context.getClosestTable();

        final CTRow ctRow = closestTable.getRow(0).getCtRow();
        final CTTrPr ctTrPr = ctRow.isSetTrPr() ? ctRow.getTrPr() : ctRow.addNewTrPr();
        ctTrPr.addNewTblHeader();

        int rowspan = 0;

        /* 只检查 第一行 tbody -> 第一列tr -> 最大rowspan值 */
        final Elements tbodys = element.getElementsByTag("tbody");
        if (!tbodys.isEmpty()) {
            final Element tbody = tbodys.first();
            final Elements trs;
            if (tbody != null) {
                trs = tbody.getElementsByTag("tr");
                if (!trs.isEmpty()) {
                    final Element tr = trs.first();
                    final Elements tds;
                    if (tr != null) {
                        tds = tr.getElementsByTag("td");
                        for (Element td : tds) {
                            // 找 td有没有rowspan属性
                            if (td.hasAttr("rowspan")) {
                                final String irowspan = td.attr("rowspan");
                                final int i = Integer.parseInt(irowspan);
                                if (i > rowspan) {
                                    rowspan = i;
                                }
                            }
                        }
                    }
                }
            }
        }

        for (int i = 0; i < rowspan; i++) {
            if (closestTable.getRow(i + 1) != null) {
                final CTRow ctRow2 = closestTable.getRow(i + 1).getCtRow();
                final CTTrPr ctTrPr2 = ctRow2.isSetTrPr() ? ctRow2.getTrPr() : ctRow2.addNewTrPr();
                ctTrPr2.addNewTblHeader();
            }
        }

        /* 处理 margin 属性未正确识别为对齐方式导致的 table 设置了对齐方式不生效 */
        String style = element.attributes().get("style");
        TableRowAlign tableRowAlign = ElementRendererUtil.getTableAlignmentFromStyle(style);
        if (tableRowAlign != null) {
            context.getClosestTable().setTableAlignment(tableRowAlign);
        }
    }

    /**
     * 处理边框虚线实线问题
     */
    private void handleBorder(Element element, HtmlRenderContext context) {
        final Elements body = element.getElementsByTag("tbody");
        if (body.isEmpty()) {
            return;
        }

        for (Element tbody : body) {
            Elements trs = tbody.getElementsByTag("tr");
            for (Element tr : trs) {
                Elements tds = tr.getElementsByTag("td");
                for (Element td : tds) {
                    String style = td.attributes().get("style");
                    Integer borderWidth = getBorderWidth(style);
                    String borderColor = getBorderColorHex(style);

                    handleTh(element, context, borderWidth, borderColor);
                }
            }
        }
    }

    private void handleTh(Element element, HtmlRenderContext context, Integer borderWidth, String borderColor) {
        if (borderWidth == null || borderColor == null) {
            return;
        }

        if (borderWidth == null) {
            borderWidth = 1;
        }
        if (borderColor == null) {
            borderColor = "000000";
        }

        CTTblBorders borders = context.getClosestTable().getCTTbl().getTblPr().addNewTblBorders();
        CTBorder hBorder = borders.addNewInsideH();
        hBorder.setVal(STBorder.Enum.forString("single"));  // 线条类型
        hBorder.setSz(new BigInteger(String.valueOf(borderWidth))); // 线条大小
        hBorder.setColor(borderColor); // 设置颜色

        CTBorder vBorder = borders.addNewInsideV();
        vBorder.setVal(STBorder.Enum.forString("single"));
        vBorder.setSz(new BigInteger(String.valueOf(borderWidth)));
        vBorder.setColor(borderColor);

        CTBorder lBorder = borders.addNewLeft();
        lBorder.setVal(STBorder.Enum.forString("single"));
        lBorder.setSz(new BigInteger(String.valueOf(borderWidth)));
        lBorder.setColor(borderColor);

        CTBorder rBorder = borders.addNewRight();
        rBorder.setVal(STBorder.Enum.forString("single"));
        rBorder.setSz(new BigInteger(String.valueOf(borderWidth)));
        rBorder.setColor(borderColor);

        CTBorder tBorder = borders.addNewTop();
        tBorder.setVal(STBorder.Enum.forString("single"));
        tBorder.setSz(new BigInteger(String.valueOf(borderWidth)));
        tBorder.setColor(borderColor);

        CTBorder bBorder = borders.addNewBottom();
        bBorder.setVal(STBorder.Enum.forString("single"));
        bBorder.setSz(new BigInteger(String.valueOf(borderWidth)));
        bBorder.setColor(borderColor);
    }

    public static Integer getBorderWidth(String input) {
        if (input == null) {
            return null;
        }
        // 定义正则表达式来匹配 border-width: 后面跟随的数字和单位
        String regex = "border-width:\\s*(\\d+)px;";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);

        // 检查是否找到匹配
        if (matcher.find()) {
            // 将匹配到的字符串转换成整数并返回
            return Integer.parseInt(matcher.group(1));
        } else {
            return null;
        }
    }

    public static String getBorderColorHex(String input) {
        if (input == null) {
            return null;
        }

        // 正则表达式匹配 border-color: rgb(...)
        String regex = "border-color:\\s*rgb\\(\\s*(\\d+),\\s*(\\d+),\\s*(\\d+)\\)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);

        if (matcher.find()) {
            // 提取 RGB 值
            int r = Integer.parseInt(matcher.group(1));
            int g = Integer.parseInt(matcher.group(2));
            int b = Integer.parseInt(matcher.group(3));

            // 转换为十六进制形式
            return String.format("%02X%02X%02X", r, g, b).toUpperCase();
        } else {
            return null;
        }
    }

    /**
     * 处理表格中图片被横向压缩问题
     *
     * @param element table元素
     */
    public void autoFixTableImage(Element element) {

        /* 捕获异常，不影响正常渲染 */
        try {
            /*
            表格中的图片会因为col的width无法横向填充
            并且，因为wangEditor编辑器宽度比word更宽&图片的宽高是依照页面的相对px
            height固定但width的实际宽度变短导致图片横向被挤压
            这里无法获取word实际px宽度，目前宽高统一乘0.9保持宽度足够
            */
            Elements image = element.getElementsByTag("img");
            if (!image.isEmpty()) {
                /* 删除table col 的 width 让图片宽度自适应 */
                Elements cols = element.getElementsByTag("col");
                cols.forEach(col -> col.removeAttr("style"));

                /* 对图片从新设置宽高 */
                image.forEach(img -> {
                    String width = img.attr("width").replace("px", "").trim();
                    String height = img.attr("height").replace("px", "").trim();

                /* 这里必须使用style属性，
                直接设置width、height属性不生效 */
                    String style = "";
                    if (!width.isEmpty()) {
                        width = (Double.parseDouble(width) * 0.9) + "";
                        style = style + "width: " + width + "px;";
                    }
                    if (!height.isEmpty()) {
                        height = (Double.parseDouble(height) * 0.9) + "";
                        style = style + " height: " + height + "px;";
                    }
                    if (!style.trim().isEmpty()) {
                        img.attr("style", style);
                    }
                });
            }
        } catch (Exception e) {
            logger.error("处理表格中图片横向压缩时发生错误:{}", e.getMessage());
        }

    }
}