package com.hdec.data.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.hdec.common.constant.Constant;
import com.hdec.common.constant.RedisKey;
import com.hdec.common.domain.AttrCommon;
import com.hdec.common.domain.PointCommon;
import com.hdec.common.formula.AttrVal;
import com.hdec.common.util.*;
import com.hdec.common.vo.Direct;
import com.hdec.common.vo.InstDirectAttr;
import com.hdec.data.config.LocalCache;
import com.hdec.data.domain.*;
import com.hdec.data.feign.MonitorService;
import com.hdec.data.mapper.DataStatMapper;
import com.hdec.data.qo.LatestDataQo;
import com.hdec.data.qo.SloshQo;
import com.hdec.data.qo.VerifyQo;
import com.hdec.data.util.RateLimitedQueue;
import com.hdec.data.util.TdUtil;
import com.hdec.data.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * <p>
 * TDEngine操作类
 */
@Slf4j
@Service
public class TdService {

    @Autowired
    private MonitorService monitorService;

    @Autowired
    private DataStatMapper statMapper;

    @Autowired
    private DataLogService dataLogService;

    @Autowired
    private TdBaseService tdBaseService;

    @Lazy
    @Autowired
    private ExcelImportService excelImportService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Lazy
    @Autowired
    private FrequencyService frequencyService;

    @Autowired
    private LocalCache localCache;

    @Resource
    private DynamicGraphService dynamicGraphService;

    @Resource
    private DailyCountService dailyCountService;

    /**
     * 判断表是否存在
     *
     * @param tableType (1:超级表  2:普通表)
     */
    public boolean isTableExist(String tableName, Integer tableType, String rate) {
        Map<String, Object> record = tdBaseService.selectOne("show " + (tableType == 1 ? "stables" : "tables") + " like '" + tableName + "'", rate);
        if (ObjectUtils.isEmpty(record)) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 确保数据超级表存在
     */
    public void createOrModifySTable(String fieldNum, Integer instId, String instDirect, List<AttrCommon> attrs) {
        log.info("fieldNum:{}, instId:{}, instDirect:{}", fieldNum, instId, instDirect);
        attrs.forEach(System.out::println);
        if (instId == null || ObjectUtils.isEmpty(attrs)) {
            return;
        }

        Integer[] directs = TdUtil.parseDirect2Arr(instDirect);
        Map<String, List<AttrCommon>> rateAttrMap = attrs.stream().collect(Collectors.groupingBy(AttrCommon::getRate));
        rateAttrMap.forEach((rate, rateAttrs) -> {
            if (Constant.RATE_HIGH.equals(rate)) {
                createOrModifyHighTables(fieldNum, instId, rateAttrs, directs);
            } else {
                createOrModifyTables(rate, instId, rateAttrs, directs);
            }
        });
    }

    /**
     * 创建或修改数据表
     */
    private void createOrModifyTables(String rate, Integer instId, List<AttrCommon> rateAttrs, Integer[] instDirects) {
        String sTableName = tdBaseService.getSTableNameByRateInst(rate, instId);
        Map<String, Object> record = tdBaseService.selectOne("show stables like '" + sTableName + "'", rate);
        if (ObjectUtils.isEmpty(record)) {
            /* 创建表 */
            StringBuilder sql = new StringBuilder("create stable " + sTableName + "(ts timestamp, ");
            for (AttrCommon attr : rateAttrs) {
                Integer attrId = attr.getId();
                Integer[] directs = tdBaseService.getFinalDirects(attr.getIsPolar(), instDirects);
                for (Integer direct : directs) {
                    sql.append(enColName(attrId, direct) + " float," + enOldColName(attrId, direct) + " float," + enD1ColName(attrId, direct) + " int," + enD2ColName(attrId, direct) + " int,");
                }
            }
            sql.append("status_0 int, status_1 int, status_2 int, status_3 int, del bool) TAGS (point int)");
            tdBaseService.executeSql(sql.toString(), rate);
        } else {
            /* 修改表 */
            System.out.println("describe " + sTableName);
            List<Map<String, Object>> records = tdBaseService.selectMulti("describe " + sTableName, rate);
            List<Object> columns = records.stream().map(m -> m.get("field")).collect(Collectors.toList());
            System.out.println("--columns--------s----");
            for (Object column : columns) {
                System.out.println(new String((byte[]) column));
            }
            System.out.println("----columns------e--------");

            for (AttrCommon attr : rateAttrs) {
                Integer attrId = attr.getId();
                Integer[] directs = tdBaseService.getFinalDirects(attr.getIsPolar(), instDirects);
                for (Integer direct : directs) {
                    if (!isColumnExist(enColName(attrId, direct), columns)) {
                        tdBaseService.executeSql("alter stable " + sTableName + " add column " + enColName(attrId, direct) + " float", rate);
                    }
                    if (!isColumnExist(enOldColName(attrId, direct), columns)) {
                        tdBaseService.executeSql("alter stable " + sTableName + " add column " + enOldColName(attrId, direct) + " float", rate);
                    }
                    /* 小时级需要判断d1和d2 */
                    if (Constant.RATE_HOUR.equals(rate)) {
                        if (!isColumnExist(enD1ColName(attrId, direct), columns)) {
                            tdBaseService.executeSql("alter stable " + sTableName + " add column " + enD1ColName(attrId, direct) + " int", rate);
                        }
                        if (!isColumnExist(enD2ColName(attrId, direct), columns)) {
                            tdBaseService.executeSql("alter stable " + sTableName + " add column " + enD2ColName(attrId, direct) + " int", rate);
                        }
                    }
                }
            }
        }
    }

    /**
     * 修正超级表（删除以前的d1和d2字段然后新建）
     */
    public void modifySTables(Integer instId, String instDirect, List<AttrCommon> attrs) {
        if (instId == null || ObjectUtils.isEmpty(attrs)) {
            return;
        }

        Integer[] directs = TdUtil.parseDirect2Arr(instDirect);
        Map<String, List<AttrCommon>> rateAttrMap = attrs.stream().collect(Collectors.groupingBy(AttrCommon::getRate));
        rateAttrMap.forEach((rate, rateAttrs) -> {
            handleModifySTables(rate, instId, rateAttrs, directs);
        });
    }

    /**
     * 处理修正超级表
     */
    private void handleModifySTables(String rate, Integer instId, List<AttrCommon> rateAttrs, Integer[] instDirects) {
        String sTableName = DataServiceUtil.buildSTableName(instId, rate);
        List<Map<String, Object>> records = tdBaseService.selectMulti("describe " + sTableName, rate);
        List<Object> columns = records.stream().map(m -> m.get("field")).collect(Collectors.toList());
        for (AttrCommon attr : rateAttrs) {
            Integer attrId = attr.getId();
            Integer[] directs = tdBaseService.getFinalDirects(attr.getIsPolar(), instDirects);
            for (Integer direct : directs) {
                if (isD1ColumnExist(attrId, direct, columns)) {
                    tdBaseService.executeSql("alter stable " + sTableName + " DROP COLUMN " + enD1ColName(attrId, direct), rate);
                }
                if (isD2ColumnExist(attrId, direct, columns)) {
                    tdBaseService.executeSql("alter stable " + sTableName + " DROP COLUMN " + enD2ColName(attrId, direct), rate);
                }
                if (isDColumnExist(attrId, direct, columns)) {
                    tdBaseService.executeSql("alter stable " + sTableName + " DROP COLUMN " + ("d_" + attrId + "_" + direct), rate);
                }

                tdBaseService.executeSql("alter stable " + sTableName + " add column " + enD1ColName(attrId, direct) + " int", rate);
                tdBaseService.executeSql("alter stable " + sTableName + " add column " + enD2ColName(attrId, direct) + " int", rate);
            }
        }
    }

    /**
     * 创建或修改高频数据表
     */
    public void createOrModifyHighTables(String fieldNum, Integer instId, List<AttrCommon> rateAttrs, Integer[] instDirects) {
        /* 创建或修改高频基础数据表 */
        createOrModifyHighBaseTable(instId, rateAttrs, instDirects);

        /* 创建或修改高频采样表 */
        createOrModifySampleTab(instId, rateAttrs, instDirects);

        /* 创建高频数量统计表 */
        statMapper.createHighStatTableIfNotExist(fieldNum);
    }

    /**
     * 创建或修改高频基础数据表
     */
    private void createOrModifyHighBaseTable(Integer instId, List<AttrCommon> rateAttrs, Integer[] instDirects) {
        String sTableName = tdBaseService.getSTableNameByRateInst(Constant.RATE_HIGH, instId);
        Map<String, Object> record = tdBaseService.selectOne("show stables like '" + sTableName + "'", Constant.RATE_HIGH);
        if (ObjectUtils.isEmpty(record)) {
            /* 创建表 */
            StringBuilder sql = new StringBuilder("create stable " + sTableName + "(ts timestamp, ");
            for (AttrCommon attr : rateAttrs) {
                Integer[] directs = tdBaseService.getFinalDirects(attr.getIsPolar(), instDirects);
                for (Integer direct : directs) {
                    sql.append(enColName(attr.getId(), direct) + " float,");
                }
            }
            sql.append("del bool) TAGS (point int)");
            tdBaseService.executeSql(sql.toString(), Constant.RATE_HIGH);
        } else {
            /* 修改表 */
            List<Map<String, Object>> records = tdBaseService.selectMulti("describe " + sTableName, Constant.RATE_HIGH);
            List<Object> columns = records.stream().map(m -> m.get("field")).collect(Collectors.toList());
            for (AttrCommon attr : rateAttrs) {
                Integer[] directs = tdBaseService.getFinalDirects(attr.getIsPolar(), instDirects);
                for (Integer direct : directs) {
                    if (!isColumnExist(attr.getId(), direct, columns)) {
                        tdBaseService.executeSql("alter stable " + sTableName + " add column " + enColName(attr.getId(), direct) + " float", Constant.RATE_HIGH);
                    }
                }
            }
        }
    }

    /**
     * 创建或修改高频采样表
     */
    private void createOrModifySampleTab(Integer instId, List<AttrCommon> rateAttrs, Integer[] instDirects) {
        String sTableName = tdBaseService.getSTableNameByRateInst(Constant.RATE_SAMPLE, instId);
        Map<String, Object> record = tdBaseService.selectOne("show stables like '" + sTableName + "'", Constant.RATE_HIGH);
        if (ObjectUtils.isEmpty(record)) {
            /* 创建表 */
            StringBuilder sql = new StringBuilder("create stable " + sTableName + "(ts timestamp, ");
            for (AttrCommon attr : rateAttrs) {
                Integer[] directs = tdBaseService.getFinalDirects(attr.getIsPolar(), instDirects);
                for (Integer direct : directs) {
                    sql.append(enColName(attr.getId(), direct) + " float,");
                }
            }
            sql.deleteCharAt(sql.length() - 1);
            sql.append(") TAGS (point int)");
            tdBaseService.executeSql(sql.toString(), Constant.RATE_HIGH);
        } else {
            /* 修改表 */
            List<Map<String, Object>> records = tdBaseService.selectMulti("describe " + sTableName, Constant.RATE_HIGH);
            List<Object> columns = records.stream().map(m -> m.get("field")).collect(Collectors.toList());
            for (AttrCommon attr : rateAttrs) {
                Integer[] directs = tdBaseService.getFinalDirects(attr.getIsPolar(), instDirects);
                for (Integer direct : directs) {
                    if (!isColumnExist(attr.getId(), direct, columns)) {
                        tdBaseService.executeSql("alter stable " + sTableName + " add column " + enColName(attr.getId(), direct) + " float", Constant.RATE_HIGH);
                    }
                }
            }
        }
    }

    /**
     * 判断列是否存在
     */
    private boolean isColumnExist(String columnName, List<Object> columns) {
        for (Object column : columns) {
            if (columnName.equals(new String((byte[]) column))) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断列是否存在
     */
    public boolean isColumnExist(Integer attrId, Integer direct, List<Object> columns) {
        for (Object column : columns) {
            byte[] buf = (byte[]) column;
            String columnName = enColName(attrId, direct);
            if (columnName.equals(new String(buf))) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断列是否存在
     */
    private boolean isD1ColumnExist(Integer attrId, Integer direct, List<Object> columns) {
        for (Object column : columns) {
            byte[] buf = (byte[]) column;
            String columnName = enD1ColName(attrId, direct);
            if (columnName.equals(new String(buf))) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断列是否存在
     */
    private boolean isDColumnExist(Integer attrId, Integer direct, List<Object> columns) {
        for (Object column : columns) {
            byte[] buf = (byte[]) column;
            String columnName = "d_" + attrId + "_" + direct;
            if (columnName.equals(new String(buf))) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断列是否存在
     */
    private boolean isD2ColumnExist(Integer attrId, Integer direct, List<Object> columns) {
        for (Object column : columns) {
            byte[] buf = (byte[]) column;
            String columnName = enD2ColName(attrId, direct);
            if (columnName.equals(new String(buf))) {
                return true;
            }
        }
        return false;
    }

    public String convertRate(String rate) {
        if (ObjectUtils.isEmpty(rate)) {
            return "";
        }
        if (rate.contains("高频")) {
            return "high";
        }
        if (rate.contains("分钟")) {
            return "min";
        }
        if (rate.contains("小时")) {
            return "hour";
        }
        return "";
    }

    /**
     * 查询数据
     */
    public DataTableVo selectData(String fieldNum, Integer instId, List<Direct> instDirects, String startTime, String endTime, Integer[] pointIds, String[] pointNos,
                                  Integer pageNum, Integer pageSize, String orderBy, String orderRule, Integer[] approval,
                                  List<AttrCommon> attrs, String rate, boolean needLimit, Integer direct, String type) {
        DataTableVo vo = new DataTableVo();

        /* 补全开始结束时间到秒 */
        if (startTime.length() == 10) {
            startTime = TimeUtil.completeStart(startTime);
            endTime = TimeUtil.completeEnd(endTime);
        }

        Integer highOffset = null;

        String sTableName = DataServiceUtil.buildSTableName(instId, rate);
        List<Map<String, Object>> records;
        if ("高频".equals(rate)) {
            if (!"export".equals(type)) {
//                Integer num = statMapper.getHighStatCount(fieldNum, pointIds, startTime, endTime);
                Date et = TimeUtil.addDays(TimeUtil.parse2Day(endTime), 1);
                Long num = dailyCountService.selectDataCount(Arrays.asList(pointIds), TimeUtil.parse2Day(startTime), et);
                if (num == null || num == 0) {
                    return vo;
                }
                vo.setTotal(num.intValue());

                int totalPages;
                if (num % pageSize == 0) {
                    totalPages = num.intValue() / pageSize;
                } else {
                    totalPages = num.intValue() / pageSize + 1;
                }
                if (totalPages > 50 && pageNum > totalPages / 2) {
                    if ("asc".equals(orderRule)) {
                        orderRule = "desc";
                    } else if ("desc".equals(orderRule)) {
                        orderRule = "asc";
                    }

                    highOffset = (totalPages - pageNum) * pageSize;
                }
            }
        } else {
            /* 查询总条数 */
            // 时间范围
            StringBuilder countSql = new StringBuilder("select count(*) AS num from " + sTableName + " where ts between '" + startTime + "' and '" + endTime + "'");
            // 测点范围
            countSql.append(" and point in " + getStrPointIds(pointIds));
            countSql.append(" and del = false");
            /*  非空条件  */
            countSql.append(getNotNullWhereCase(attrs, instDirects.stream().map(Direct::getDirectId).collect(Collectors.toList())));
            // 审核状态
            if (direct != null && approval.length != 3) {
                countSql.append(" and status_" + direct + " in " + getStrPointIds(approval));
            }
            log.info("count Sql:{}", countSql.toString());
            long s = System.currentTimeMillis();
            records = tdBaseService.selectMulti(countSql.toString(), rate);
            if (records.size() == 0) {
                return vo;
            }
            vo.setTotal(Integer.parseInt(String.valueOf(records.get(0).get("num"))));
            long e = System.currentTimeMillis();
            log.info("耗时：{}ms, count Sql:{}", (e - s), countSql.toString());
        }

        /* 查询数据 */
        // 时间范围
        StringBuilder dataSql = new StringBuilder("select * from " + sTableName + " where ts between '" + startTime + "' and '" + endTime + "'");
        // 测点范围
        dataSql.append(" and point in " + getStrPointIds(pointIds));
        dataSql.append(" and del = false");
        /*  非空条件  */
        dataSql.append(getNotNullWhereCase(attrs,instDirects.stream().map(Direct::getDirectId).collect(Collectors.toList())));
        // 审核状态
        if (direct != null && approval.length != 3 && !"高频".equals(rate)) {
            dataSql.append(" and status_" + direct + " in " + getStrPointIds(approval));
        }
        // 排序
        if (!ObjectUtils.isEmpty(orderBy)) {
            dataSql.append(" order by ");
            if ("point".equals(orderBy)) {
                dataSql.append("point");
            } else if (orderBy != null && orderBy.startsWith("attr")) {
                String[] arr = orderBy.split("_");
                dataSql.append(enColName(Integer.parseInt(arr[1]), Integer.parseInt(arr[2])));
            } else {
                dataSql.append("ts");
            }
            dataSql.append(" " + orderRule + " NULLS LAST");
        }
        // 分页
        if (needLimit) {
            int offset = highOffset == null ? (pageNum - 1) * pageSize : highOffset;
            dataSql.append(" limit " + offset + "," + pageSize);
        }

        long s = System.currentTimeMillis();
        records = tdBaseService.selectMulti(dataSql.toString(), rate);
        long e = System.currentTimeMillis();
        log.info("耗时：{}ms, data Sql:{}", (e - s), dataSql.toString());

        /* 封装表头 */
        List<TableHead> tableHeads = new ArrayList<>();
        tableHeads.add(new TableHead("point", "测点编号"));
        tableHeads.add(new TableHead("ts", "时间"));
        Set<Integer> allDirects = new HashSet<>();
        for (int i = 0; i < attrs.size(); i++) {
            AttrCommon attr = attrs.get(i);
            List<Direct> directs = tdBaseService.getFinalDirects(attr.getIsPolar(), instDirects);
            for (int j = 0; j < directs.size(); j++) {
                String directName;
                if (attr.getIsPolar() || directs.size() == 1) {
                    directName = "";
                } else {
                    directName = directs.get(j).getDirectName() + "方向";
                }
                tableHeads.add(new TableHead("attr_" + attr.getId() + "_" + directs.get(j).getDirectId(),
                        directName + attr.getName() + (ObjectUtils.isEmpty(attr.getUnit()) ? "" : "(" + attr.getUnit() + ")"), attr.getType()));
                allDirects.add(directs.get(j).getDirectId());
            }
        }
        vo.setHead(tableHeads.toArray(new TableHead[tableHeads.size()]));

        /* 封装表体 */
        List<List<CellVal>> tableBody = new ArrayList<>();
        for (int i = 0; i < records.size(); i++) {
            Map<String, Object> record = records.get(i);
            List<CellVal> cellValues = new ArrayList<>();

            Integer pointId = (Integer) record.get("point");
            cellValues.add(new CellVal(pointId + "", getPointNoById(pointId, pointIds, pointNos), null));

            Date ts = (Date) record.get("ts");
            String formatTs;
            if (rate.contains("高频")) {
                formatTs = TimeUtil.format2Ms(ts);
            } else {
                formatTs = TimeUtil.format2Second(ts);
            }
            cellValues.add(new CellVal(formatTs, formatTs, null));

            // 封装属性
            for (AttrCommon attr : attrs) {
                L1:
                for (int j = 0; j < instDirects.size(); j++) {
                    int directId = attr.getIsPolar() ? 0 : instDirects.get(j).getDirectId();
                    Object val = record.get(enColName(attr.getId(), directId));
                    Object oldVal = null;
                    if (!"高频".equals(rate)) {
                        oldVal = record.get(enOldColName(attr.getId(), directId));
                    }
                    cellValues.add(new CellVal(attr.getId() + "-" + directId, val, oldVal));
                    if (attr.getIsPolar()) {
                        break L1;
                    }
                }
            }

            for (Integer d : allDirects) {
                Integer status = 0;
                if (!"高频".equals(rate)) {
                    status = (Integer) record.get("status_" + d);
                }
                cellValues.add(new CellVal("s_" + d, String.valueOf(status), null));
            }
            tableBody.add(cellValues);
        }
        vo.setBody(tableBody);
        vo.setDirects(allDirects);
        return vo;
    }

    /**
     * 获取非空列where条件
     *
     * @param attrs   attrs
     * @param directs directs
     * @return {@link String }
     */
    private static String getNotNullWhereCase(List<AttrCommon> attrs, List<Integer> directs) {
        if (ObjectUtils.isEmpty(attrs) || ObjectUtils.isEmpty(directs)) {
            return "";
        }
        List<String> whereCase = attrs.stream().map(attr -> {
            Boolean isPolar = attr.getIsPolar();
            List<String> attrCol = isPolar != null && isPolar ?
                    Collections.singletonList("a_" + attr.getId() + "_0 is not null") :
                    directs.stream()
                            .map(direct -> "a_" + attr.getId() + "_" + direct + " is not null")
                            .collect(Collectors.toList());
            return String.join(" or ", attrCol);
        }).collect(Collectors.toList());
        return " and (" + String.join(" or ", whereCase) + ")";
    }

    /**
     * 查询数据
     */
    public DataTableVo selectDataForExport(String fieldNum, Integer instId, List<Direct> instDirects, String startTime, String endTime, Integer[] pointIds, String[] pointNos,
                                  Integer pageNum, Integer pageSize, String orderBy, String orderRule,
                                  List<AttrCommon> attrs, String rate, boolean needLimit, String type) {
        DataTableVo vo = new DataTableVo();

        /* 补全开始结束时间到秒 */
        if (startTime.length() == 10) {
            startTime = TimeUtil.completeStart(startTime);
            endTime = TimeUtil.completeEnd(endTime);
        }

        Integer highOffset = null;

        String sTableName = DataServiceUtil.buildSTableName(instId, rate);
        List<Map<String, Object>> records;
        if ("高频".equals(rate)) {
            if (!"export".equals(type)) {
                Integer num = statMapper.getHighStatCount(fieldNum, pointIds, startTime, endTime);
                if (num == null || num == 0) {
                    return vo;
                }
                vo.setTotal(num);

                int totalPages;
                if (num % pageSize == 0) {
                    totalPages = num / pageSize;
                } else {
                    totalPages = num / pageSize + 1;
                }
                if (totalPages > 50 && pageNum > totalPages / 2) {
                    if ("asc".equals(orderRule)) {
                        orderRule = "desc";
                    } else if ("desc".equals(orderRule)) {
                        orderRule = "asc";
                    }

                    highOffset = (totalPages - pageNum) * pageSize;
                }
            }
        } else {
            /* 查询总条数 */
            // 时间范围
            StringBuilder countSql = new StringBuilder("select count(*) AS num from " + sTableName + " where ts between '" + startTime + "' and '" + endTime + "'");
            // 测点范围
            countSql.append(" and point in " + getStrPointIds(pointIds));
            countSql.append(" and del = false");
            log.info("count Sql:{}", countSql.toString());
            long s = System.currentTimeMillis();
            records = tdBaseService.selectMulti(countSql.toString(), rate);
            if (records.size() == 0) {
                return vo;
            }
            vo.setTotal(Integer.parseInt(String.valueOf(records.get(0).get("num"))));
            long e = System.currentTimeMillis();
            log.info("耗时：{}ms, count Sql:{}", (e - s), countSql.toString());
        }

        /* 查询数据 */
        // 时间范围
        StringBuilder dataSql = new StringBuilder("select * from " + sTableName + " where ts between '" + startTime + "' and '" + endTime + "'");
        // 测点范围
        dataSql.append(" and point in " + getStrPointIds(pointIds));
        dataSql.append(" and del = false");
        // 排序
        if (!ObjectUtils.isEmpty(orderBy)) {
            dataSql.append(" order by ");
            if ("point".equals(orderBy)) {
                dataSql.append("point");
            } else if (orderBy != null && orderBy.startsWith("attr")) {
                String[] arr = orderBy.split("_");
                dataSql.append(enColName(Integer.parseInt(arr[1]), Integer.parseInt(arr[2])));
            } else {
                dataSql.append("ts");
            }
            dataSql.append(" " + orderRule + " NULLS LAST");
        }
        // 分页
        if (needLimit) {
            int offset = highOffset == null ? (pageNum - 1) * pageSize : highOffset;
            dataSql.append(" limit " + offset + "," + pageSize);
        }

        long s = System.currentTimeMillis();
        records = tdBaseService.selectMulti(dataSql.toString(), rate);
        long e = System.currentTimeMillis();
        log.info("耗时：{}ms, data Sql:{}", (e - s), dataSql.toString());

        /* 封装表头 */
        List<TableHead> tableHeads = new ArrayList<>();
        tableHeads.add(new TableHead("point", "测点编号"));
        tableHeads.add(new TableHead("ts", "时间"));
        Set<Integer> allDirects = new HashSet<>();
        for (int i = 0; i < attrs.size(); i++) {
            AttrCommon attr = attrs.get(i);
            List<Direct> directs = tdBaseService.getFinalDirects(attr.getIsPolar(), instDirects);
            for (int j = 0; j < directs.size(); j++) {
                String directName;
                if (attr.getIsPolar() || directs.size() == 1) {
                    directName = "";
                } else {
                    directName = directs.get(j).getDirectName() + "方向";
                }
                tableHeads.add(new TableHead("attr_" + attr.getId() + "_" + directs.get(j).getDirectId(),
                        directName + attr.getName() + (ObjectUtils.isEmpty(attr.getUnit()) ? "" : "(" + attr.getUnit() + ")"), attr.getType()));
                allDirects.add(directs.get(j).getDirectId());
            }
        }
        vo.setHead(tableHeads.toArray(new TableHead[tableHeads.size()]));

        /* 封装表体 */
        List<List<CellVal>> tableBody = new ArrayList<>();
        for (int i = 0; i < records.size(); i++) {
            Map<String, Object> record = records.get(i);
//            System.out.println("---"+record);
            List<CellVal> cellValues = new ArrayList<>();

            Integer pointId = (Integer) record.get("point");
            cellValues.add(new CellVal(pointId + "", getPointNoById(pointId, pointIds, pointNos), null));

            Date ts = (Date) record.get("ts");
            String formatTs;
            if (rate.contains("高频")) {
                formatTs = TimeUtil.format2Ms(ts);
            } else {
                formatTs = TimeUtil.format2Second(ts);
            }
            cellValues.add(new CellVal(formatTs, formatTs, null));

            // 封装属性
            for (AttrCommon attr : attrs) {
                L1:
                for (int j = 0; j < instDirects.size(); j++) {
                    int directId = attr.getIsPolar() ? 0 : instDirects.get(j).getDirectId();
                    Object val = record.get(enColName(attr.getId(), directId));
                    Object oldVal = null;
                    Integer status = null;
                    if (!"高频".equals(rate)) {
                        oldVal = record.get(enOldColName(attr.getId(), directId));
                        status = (Integer) record.get(enStatusColName(directId));
                    }
                    cellValues.add(new CellVal(attr.getId() + "-" + directId, val, oldVal, status));
                    if (attr.getIsPolar()) {
                        break L1;
                    }
                }
            }
            tableBody.add(cellValues);
        }
        vo.setBody(tableBody);
        vo.setDirects(allDirects);
        return vo;
    }

    /**
     * 查询数据
     */
    public DataTableVo selectMinData(Integer instId, String rate, String startTime, String endTime, Integer pointId, String pointNo,
                                     Integer pageNum, Integer pageSize, Integer[] approval, Integer attrId, String attrName, Integer direct,
                                     Float lt, String connector, Float gt, Float min, Float max) {
        attrName = Utils.getDirectNameById(direct) + attrName;
        DataTableVo vo = new DataTableVo();
        String sTableName = convertRate(rate) + "_" + instId;

        /* 查询总条数 */
        // 时间范围
        StringBuilder sql = new StringBuilder("select count(*) AS num from " + sTableName + " where ts between '" + startTime + "' and '" + endTime + "'");
        // 测点范围
        sql.append(" and point = " + pointId);
        sql.append(" and del = false");
        // 值范围
        if (min != null && max != null) {
            sql.append(" and " + enColName(attrId, direct) + " between " + min + " and " + max);
        }
        // 审核状态
        if (direct != null && approval.length != 3) {
            sql.append(" and status_" + direct + " in " + getStrPointIds(approval));
        }
        // 测值范围
        if (gt != null && lt != null) {
            sql.append(" and (" + enColName(attrId, direct) + " >= " + gt + " " + connector + " " + enColName(attrId, direct) + " <= " + lt + ")");
        } else if (gt != null) {
            sql.append(" and " + enColName(attrId, direct) + " >= " + gt);
        } else if (lt != null) {
            sql.append(" and " + enColName(attrId, direct) + " <= " + lt);
        }
        long s = System.currentTimeMillis();
        System.out.println("查询小表总数：" + sql.toString());
        List<Map<String, Object>> records = tdBaseService.selectMulti(sql.toString(), rate);
        if (records.size() == 0) {
            return vo;
        }
        vo.setTotal(Integer.parseInt(String.valueOf(records.get(0).get("num"))));
        long e = System.currentTimeMillis();
        log.info("查询小表总数耗时:" + (e - s) + "ms");

        /* 查询数据 */
        // 时间范围
        sql = new StringBuilder("select * from " + sTableName + " where ts between '" + startTime + "' and '" + endTime + "'");
        // 测点范围
        sql.append(" and point = " + pointId);
        sql.append(" and del = false");
        // 值范围
        if (min != null && max != null) {
            sql.append(" and " + enColName(attrId, direct) + " between " + min + " and " + max);
        }
        // 审核状态
        if (direct != null && approval.length != 3) {
            sql.append(" and status_" + direct + " in " + getStrPointIds(approval));
        }
        // 测值范围
        if (gt != null && lt != null) {
            sql.append(" and (" + enColName(attrId, direct) + " >= " + gt + " " + connector + " " + enColName(attrId, direct) + " <= " + lt + ")");
        } else if (gt != null) {
            sql.append(" and " + enColName(attrId, direct) + " >= " + gt);
        } else if (lt != null) {
            sql.append(" and " + enColName(attrId, direct) + " <= " + lt);
        }
        // 排序
        sql.append(" order by ts");
        // 分页
        int offset = (pageNum - 1) * pageSize;
        sql.append(" limit " + offset + "," + pageSize);

        System.out.println("查询小表数据：" + sql.toString());
        records = tdBaseService.selectMulti(sql.toString(), rate);

        /* 封装表头 */
        TableHead[] tableHeads = new TableHead[1 + 3];
        tableHeads[0] = new TableHead("point", "测点编号");
        tableHeads[1] = new TableHead("direct", "方向");
        tableHeads[2] = new TableHead("ts", "时间");
        tableHeads[3] = new TableHead("attr_" + attrId, attrName, "");
        vo.setHead(tableHeads);

        /* 封装表体 */
        List<List<CellVal>> tableBody = new ArrayList<>();
        for (int i = 0; i < records.size(); i++) {
            Map<String, Object> record = records.get(i);
            List<CellVal> cellValues = new ArrayList<>();

            cellValues.add(new CellVal(pointId + "", pointNo, null));
            cellValues.add(new CellVal(direct + "", MonitorUtil.transDirection(direct), null));

            Date ts = (Date) record.get("ts");
            String formatTs = TimeUtil.format2Ms(ts);
            cellValues.add(new CellVal(formatTs, formatTs, null));

            // 封装属性
            Object val = record.get(enColName(attrId, direct));
            Object oldVal = record.get(enOldColName(attrId, direct));
            cellValues.add(new CellVal(attrId + "", val, oldVal));

            Integer approvalFlag = (Integer) record.get("status_" + direct);
            cellValues.add(new CellVal("s_" + direct, String.valueOf(approvalFlag), null));
            tableBody.add(cellValues);
        }
        vo.setBody(tableBody);
        return vo;
    }

    /**
     * 数据查询(图)
     */
    public List<DataChartVo> queryChart(Integer instId, Integer[] pointIds, String[] pointNos,
                                        String[] attrIds, String[] attrNames, String startTime, String endTime, Integer[] approval, Float min, Float max, String rate) throws Exception {
        long s, e;
        s = System.currentTimeMillis();
        endTime = endTime.replace("00:00:00", "23:59:59");

        if (ObjectUtils.isEmpty(approval)) {
            approval = new Integer[]{0, 1, 2};
        }

        int secondsBetween = TimeUtil.secondsBetween(startTime, endTime);
        long seconds = TimeUtil.secondsBetween(startTime, endTime);
        int targetSize = 10000;
        int interval = (int) (seconds / targetSize);

        List<DataChartVo> vos = new ArrayList<>();
        int pointIdx = 0;
        for (Integer pointId : pointIds) {
            String tabName = DataServiceUtil.buildTableName(instId, rate, pointId);
            for (int i = 0; i < attrIds.length; i++) {
                String[] ids = attrIds[i].split("-");
                int id = Integer.parseInt(ids[0]);
                int directId = Integer.parseInt(ids[1]);
                String colName = enColName(id, directId);

                List<Map<String, Object>> records;
                boolean isSampling;
                if ("高频".equals(rate) && (secondsBetween > 60 * 10)) {
                    String tableName = "sample_" + instId + "_" + pointId;
//                    long s1 = System.currentTimeMillis();
//                    String tableName = "sample_" + instId + "_" + pointId;
//
//                    targetSize = 300;
//                    long s2 = System.currentTimeMillis();
//                    long totalSize = getRecordsSize(startTime, endTime, approval, interval, tabName, attrIds, min, max, directId, rate);
//                    long e2 = System.currentTimeMillis();
//                    log.error("数量耗时：{} ms", (e2 - s2));
//                    if (totalSize <= targetSize) {
//                        // 不用采样
//                        isSampling = false;
//                        String sql = "select ts, " + colName + " from " + tableName + " where ts between '" + startTime + "' and '" + endTime + "'";
//                        records = tdBaseService.selectMulti(sql, rate);
//                        log.error("不采样sql:"+sql);
//                    } else {
                    // 采样
                    isSampling = true;
                    String sql = "select ts, sample(" + colName + ", " + 1000 + ") AS " + colName + " from " + tableName + " where ts between '" + startTime + "' and '" + endTime + "' order by ts";
                    log.info("采样sql:" + sql);
                    records = tdBaseService.selectMulti(sql, rate);
//                    }
//                    long e1 = System.currentTimeMillis();
//                    log.error("采样耗时：{} ms", (e1 - s1));
                } else {
                    long totalSize = getRecordsSize(startTime, endTime, approval, interval, tabName, attrIds, min, max, directId, rate);
                    if (totalSize <= targetSize) {
                        // 不用抽稀
                        records = getRecordsByInterval(startTime, endTime, approval, null, tabName, attrIds, min, max, directId, "", rate, colName);
                    } else {
                        records = getRecordsByInterval(startTime, endTime, approval, interval, tabName, attrIds, min, max, directId, "", rate, colName);
                    }
                    isSampling = records.size() < totalSize;
                }

                List<Object[]> dataList = new ArrayList<>();
                DataChartVo vo = new DataChartVo(pointId, directId, id, pointNos[pointIdx] + "-" + attrNames[i], dataList);
                vo.setIsSampling(isSampling);

                for (Map<String, Object> record : records) {
                    Long ts = getTs(record);
                    Object val = getVal(record, id, directId);
                    if (val != null) {
                        Object[] objArr = new Object[]{ts, val};
                        dataList.add(objArr);
                    }
                }
                if (!ObjectUtils.isEmpty(dataList)) {
                    vos.add(vo);
                }
            }
            pointIdx++;
        }
        e = System.currentTimeMillis();
        log.info("过程线总耗时：{} ms", (e - s));
        return vos;
    }

    private List<Map<String, Object>> getRecordsByInterval(String startTime, String endTime, Integer[] approval, Integer interval,
                                                           String tabName, String[] attrIds, Float min, Float max, Integer directId, String unit, String rate, String colName) {
        String s = ObjectUtils.isEmpty(unit) ? "s" : unit;
        if (interval == null) {
            StringBuilder sql = new StringBuilder("select ts, " + colName + " AS " + colName + " from " + tabName + " where ts between '" + startTime + "' and '" + endTime + "'");
            if (!Constant.RATE_HIGH.equals(rate)) {
                sql.append(" and status_" + directId + " in " + getStrPointIds(approval));
            }
            sql.append(" and del = false");
            return tdBaseService.selectMulti(sql.toString(), rate);
        }
        if (interval <= 0) {
            interval = 1;
        }
        StringBuilder sql = new StringBuilder("select ts, FIRST(" + colName + ") AS " + colName + " from " + tabName + " where ts between '" + startTime + "' and '" + endTime + "'");
        if (!Constant.RATE_HIGH.equals(rate)) {
            sql.append(" and status_" + directId + " in " + getStrPointIds(approval));
        }
        sql.append(" and del = false");
        sql.append(" INTERVAL(" + interval + s + ")");
        log.info(sql.toString());
        return tdBaseService.selectMulti(sql.toString(), rate);
    }

    private long getRecordsSize(String startTime, String endTime, Integer[] approval, int interval,
                                String tabName, String[] attrIds, Float min, Float max, Integer directId, String rate) {
        StringBuilder sql = new StringBuilder("select count(*) AS size from " + tabName + " where ts between '" + startTime + "' and '" + endTime + "'");
        if (!Constant.RATE_HIGH.equals(rate)) {
            sql.append(" and status_" + directId + " in " + getStrPointIds(approval));
        }
        sql.append(" and del = false");
        Map<String, Object> record = tdBaseService.selectOne(sql.toString(), rate);
        if (ObjectUtils.isEmpty(record)) {
            return 0;
        }
        if (record.get("size") != null) {
            return (long) record.get("size");
        }
        return 0;
    }

    /**
     * 获取ID和名称的映射关系
     */
    private Map<String, String> createMapping(String[] ids, String[] names) {
        Map<String, String> map = new HashMap<>(ids.length);
        for (int i = 0; i < ids.length; i++) {
            map.put(ids[i], names[i]);
        }
        return map;
    }

    /**
     * 获取ID和名称的映射关系
     */
    private Map<Integer, String> createMappingInt(Integer[] ids, String[] names) {
        Map<Integer, String> map = new HashMap<>(ids.length);
        for (int i = 0; i < ids.length; i++) {
            map.put(ids[i], names[i]);
        }
        return map;
    }

    private Object getVal(Map<String, Object> record, int attrId, int direct) {
        for (Map.Entry<String, Object> entry : record.entrySet()) {
            String key = entry.getKey();
            if (key.contains(enColName(attrId, direct))) {
                return entry.getValue();
            }
        }
        return null;
    }

    private Long getTs(Map<String, Object> record) {
        for (Map.Entry<String, Object> entry : record.entrySet()) {
            if (entry.getKey().contains("ts")) {
                Date ts = (Date) entry.getValue();
                return ts.getTime();
            }
        }
        return null;
    }

    private String getPointNoById(Integer pointId, Integer[] pointIds, String[] pointNos) {
        for (int i = 0; i < pointIds.length; i++) {
            if (pointId.equals(pointIds[i])) {
                return pointNos[i];
            }
        }
        return null;
    }

    private String getStrPointIds(Integer[] pointIds) {
        StringBuilder sb = new StringBuilder("(");
        for (Integer pointId : pointIds) {
            sb.append(pointId);
            sb.append(",");
        }
        sb.deleteCharAt(sb.length() - 1);
        sb.append(")");
        return sb.toString();
    }

    private String getStrTimes(List<VerifyQo> qos) {
        StringBuilder sb = new StringBuilder("(");
        for (VerifyQo qo : qos) {
            sb.append("'" + qo.getTime() + "'");
            sb.append(",");
        }
        sb.deleteCharAt(sb.length() - 1);
        sb.append(")");
        return sb.toString();
    }

    /**
     * 审核数据
     */
    public void verify(Integer instId, VerifyQo qo, String rate) {
        /* 查询目标数据并更新 */
        String tableName = convertRate(rate) + "_" + instId + "_" + qo.getPointId();
        Map<String, Object> record = tdBaseService.selectOne("select * from " + tableName + " where ts = '" + qo.getTime() + "'", rate);
        record.put("status_" + qo.getDirect(), qo.getApproval());

        /* 插入数据 */
        insertRecord(tableName, record, qo.getRate());
    }

//    /**
//     * 删除数据
//     */
//    public void del(String fieldNum, Integer instId, VerifyQo qo, Integer userId, String username) {
//        /* 查询目标数据并更新 */
//        String tableName = convertRate(qo.getRate()) + "_" + instId + "-" + qo.getPointId();
//        Map<String, Object> record = selectBaseOne("select * from " + tableName + " where ts = '" + qo.getTime() + "'");
//        record.put("status", 3);
//
//        /* 插入数据 */
//        insertRecord(tableName, record);
//
//        /* 记录日志 */
//        DataLog dataLog = new DataLog(qo.getPointId(), qo.getDirect(), qo.getAttrId(), qo.getTime(), 2, qo.getDelReason(), fieldNum);
//        dataLog.setUserId(userId);
//        dataLog.setUsername(username);
//        dataLogService.save(dataLog);
//    }

    /**
     * 批量删除数据
     */
    public void delBatch(String fieldNum, Integer instId, List<AttrCommon> attrs, Integer direct, List<VerifyQo> qos, Integer userId, String username) {
        String rate = qos.get(0).getRate();
        Map<Integer, AttrCommon> attrsMap = attrs.stream().collect(Collectors.toMap(AttrCommon::getId, Function.identity(), (key1, key2) -> key2));

        /* 查询目标数据并更新 */
        List<Map<String, Object>> records = new ArrayList<>();
        Map<Integer, List<VerifyQo>> pointTimeMap = qos.stream().collect(Collectors.groupingBy(e -> e.getPointId()));
        pointTimeMap.forEach((pointId, timeQos) -> {
            String tableName = DataServiceUtil.buildTableName(instId, rate, pointId);
            String sql = "select point, * from " + tableName + " where ts IN " + getStrTimes(timeQos);
            if (!Constant.RATE_HIGH.equals(rate)) {
                sql = "select point, * from " + tableName + " where TIMETRUNCATE(ts, 1s) IN " + getStrTimes(timeQos);
            }
            List<Map<String, Object>> pointRecords = tdBaseService.selectMulti(sql, rate);
            if (!ObjectUtils.isEmpty(pointRecords)) {
                records.addAll(pointRecords);
            }
        });

        Map<String, List<Map<String, Object>>> map = records.stream().collect(Collectors.groupingBy(e -> TimeUtil.format((Date) e.get("ts"), "yyyy-MM-dd HH:mm:ss") + "-" + e.get("point")));

        // 删除数据
        List<Map<String, Object>> newRecords = new ArrayList<>(records.size());
        for (Map<String, Object> record : records) {
            Map<String, Object> newRecord = new HashMap<>(record.size());
            record.forEach((k, v) -> {
                newRecord.put(k, v);
            });
            newRecords.add(newRecord);
        }
        for (Map<String, Object> record : newRecords) {
            Iterator<String> it = record.keySet().iterator();
            while (it.hasNext()) {
                String k = it.next();
                if ("ts".equals(k) || "point".equals(k) || "direct".equals(k) || "approval".equals(k)) {
                    continue;
                }
                if (direct == -1 || k.endsWith("_" + direct)) {
                    record.put(k, null);
                }
            }
            if (direct == -1 || allValNull(record)) {
                // 删除整行
                record.put("del", true);
            }
        }

        for (Map<String, Object> newRecord : newRecords) {
            log.info(newRecord.toString());
        }
        for (List<Map<String, Object>> list : Lists.partition(newRecords, 50)) {
            insertRecords(instId, rate, list);
        }

        if ("高频".equals(qos.get(0).getRate())) {
            List<AttrCommon> fieldAttrs = monitorService.allAttrByField(fieldNum);
            Map<Integer, AttrCommon> fieldAttrMap = fieldAttrs.stream().collect(Collectors.toMap(AttrCommon::getId, Function.identity(), (key1, key2) -> key2));

            List<InsertRecord> records1 = toRecords(records);
            for (InsertRecord record : records1) {
                record.setInstId(instId);
            }
            excelImportService.handleHighSample(fieldNum, records1, fieldAttrMap);
        }

        /* 记录日志 */
        if (!"高频".equals(qos.get(0).getRate())) {
            new Thread(() -> {
                try {
                    for (VerifyQo qo : qos) {
                        DataLog dataLog = new DataLog(qo.getPointId(), direct, qo.getAttrId(), qo.getTime(), 2, qo.getDelReason(), fieldNum);
                        dataLog.setRate(rate);
                        List<Map<String, Object>> record = map.get(qo.getTime() + "-" + qo.getPointId());
                        StringBuilder sb = new StringBuilder();
                        Map<String, Object> values = new HashMap<>();
                        if (record != null && record.size() > 0) {
                            record.get(0).forEach((k, v) -> {
                                if (k.startsWith("a_") && (direct == -1 || k.endsWith("_" + direct))) {
                                    String directName = Utils.getDirectNameById(Integer.parseInt(k.split("_")[2]));
                                    AttrCommon attrCommon = attrsMap.get(Integer.parseInt(k.split("_")[1]));
                                    if (attrCommon != null) {
                                        sb.append(directName + attrCommon.getName() + "：" + (v == null ? "空值" : v));
                                        sb.append("，");
                                        values.put(k, v);
                                    }
                                }
                                if ("ts".equals(k)) {
                                    values.put(k, v);
                                }
                            });

                            if (sb.length() > 1) {
                                sb.deleteCharAt(sb.length() - 1);
                            }
                            dataLog.setDelDataJson(JSON.toJSONString(values));
                        }
                        dataLog.setDelData(sb.toString());
                        dataLog.setUserId(userId);
                        dataLog.setUsername(username);
                        dataLogService.save(dataLog);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }).start();
        }
    }

    /**
     * 查询结果集转对象
     */
    private List<InsertRecord> toRecords(List<Map<String, Object>> records) {
        List<InsertRecord> list = new ArrayList<>(records.size());
        for (Map<String, Object> record : records) {
            String time = TimeUtil.format2Second((Date) record.get("ts"));
            for (String colName : record.keySet()) {
                if (colName.startsWith("a_")) {
                    String[] arr = colName.split("_");
                    AttrIdVal value = new AttrIdVal(Integer.parseInt(arr[1]), (Float) record.get(colName));

                    Set<AttrIdVal> values = new HashSet<>(1);
                    values.add(value);
                    list.add(new InsertRecord(time, (Integer) record.get("point"), Integer.parseInt(arr[2]), values, value));
                }
            }
        }
        return list;
    }

    private boolean allValNull(Map<String, Object> record) {
        Iterator<String> it = record.keySet().iterator();
        while (it.hasNext()) {
            String k = it.next();
            if (k.startsWith("a_") && !ObjectUtils.isEmpty(record.get(k))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 过滤数据
     */
    private void filterData(List<Map<String, Object>> records, List<VerifyQo> qos) {
        if (ObjectUtils.isEmpty(records) || ObjectUtils.isEmpty(qos)) {
            return;
        }

        Set<String> set = qos.stream().map(e -> e.getTime() + "-" + e.getPointId()).collect(Collectors.toSet());

        Iterator<Map<String, Object>> it = records.iterator();
        while (it.hasNext()) {
            Map<String, Object> next = it.next();
            String ts = TimeUtil.format((Date) next.get("ts"), "yyyy-MM-dd HH:mm:ss");
            Integer point = (Integer) next.get("point");
            if (!set.contains(ts + "-" + point)) {
                it.remove();
            }
        }
    }

    /**
     * 更新数据
     */
    public void update(String fieldNum, Integer instId, VerifyQo qo, Integer userId, String username) throws Exception {
        String[] arr = qo.getAttrAndDirect().split("-");
        if (arr.length != 2) {
            throw new Exception();
        }
        qo.setAttrId(Integer.parseInt(arr[0]));
        qo.setDirect(Integer.parseInt(arr[1]));

        /* 查询目标数据并更新 */
        String tableName = convertRate(qo.getRate()) + "_" + instId + "_" + qo.getPointId();
        Map<String, Object> record = tdBaseService.selectOne("select * from " + tableName + " where ts = '" + qo.getTime() + "'", qo.getRate());
        Object curVal = record.get(enColName(qo.getAttrId(), qo.getDirect()));
        System.out.println("be - record:" + record);

        record.put(enOldColName(qo.getAttrId(), qo.getDirect()), curVal);
        record.put(enColName(qo.getAttrId(), qo.getDirect()), qo.getNewVal());

        System.out.println("af - record:" + record);

        /* 插入数据 */
        insertRecord(tableName, record, qo.getRate());

        /* 记录日志 */
        DataLog dataLog = new DataLog(qo.getPointId(), qo.getDirect(), qo.getAttrId(), qo.getTime(), (Float) curVal, qo.getNewVal(), 1, fieldNum);
        dataLog.setUserId(userId);
        dataLog.setUsername(username);
        dataLogService.save(dataLog);
    }

    /**
     * 插入一条数据
     */
    public void insertRecord(String tableName, Map<String, Object> record, String rate) {
        StringBuilder sb = new StringBuilder("insert into " + tableName + " (");
        record.forEach((colName, colVal) -> {
            sb.append(colName + ",");
        });
        sb.deleteCharAt(sb.length() - 1);
        sb.append(") values (");
        record.forEach((colName, colVal) -> {
            sb.append("'" + colVal + "',");
        });
        sb.deleteCharAt(sb.length() - 1);
        sb.append(")");

        /* 执行插入sql */
        tdBaseService.executeSql(sb.toString(), rate);
    }

    /**
     * 插入一条数据
     */
    public void insertRecord2(String tableName, Map<String, Object> record, String rate) {
        StringBuilder sb = new StringBuilder("insert into " + tableName + " (");
        record.forEach((colName, colVal) -> {
            sb.append(colName + ",");
        });
        sb.deleteCharAt(sb.length() - 1);
        sb.append(") values (");
        record.forEach((colName, colVal) -> {
            if ("ts".equals(colName)) {
                sb.append(colVal + ",");
            } else {
                sb.append("'" + colVal + "',");
            }
        });
        sb.deleteCharAt(sb.length() - 1);
        sb.append(")");

        /* 执行插入sql */
        tdBaseService.executeSql(sb.toString(), rate);
    }

    /**
     * 还原数据
     */
    public void returnBack(Integer instId, VerifyQo qo, Integer userId, String username, String fieldNum) throws Exception {
        String[] arr = qo.getAttrAndDirect().split("-");
        if (arr.length != 2) {
            throw new Exception();
        }
        qo.setAttrId(Integer.parseInt(arr[0]));
        qo.setDirect(Integer.parseInt(arr[1]));

        /* 查询目标数据并更新 */
        String tableName = convertRate(qo.getRate()) + "_" + instId + "_" + qo.getPointId();
        Map<String, Object> record = tdBaseService.selectOne("select * from " + tableName + " where ts = '" + qo.getTime() + "'", qo.getRate());
        Object newVal = record.get(enColName(qo.getAttrId(), qo.getDirect()));
        Object oldVal = record.get(enOldColName(qo.getAttrId(), qo.getDirect()));
        record.put(enColName(qo.getAttrId(), qo.getDirect()), oldVal);
        record.put(enOldColName(qo.getAttrId(), qo.getDirect()), "null");

        /* 插入数据 */
        insertRecord(tableName, record, qo.getRate());

        /* 记录日志 */
        DataLog dataLog = new DataLog(qo.getPointId(), qo.getDirect(), qo.getAttrId(), qo.getTime(), (Float) newVal, (Float) oldVal, 1, fieldNum);
        dataLog.setUserId(userId);
        dataLog.setUsername(username);
        dataLogService.save(dataLog);
    }

    /**
     * 保存数据到表
     */
    public void save(String rate, String time, Integer pointId, Integer directId, Set<AttrIdVal> values) {
        removeNull(values);
        /* 表名 */
        InstDirectAttr instInfo = monitorService.getInstDirectAttr(pointId);
        if (instInfo.getInstId() == null || ObjectUtils.isEmpty(instInfo.getAttrs())) {
            return;
        }

        String sTableName = DataServiceUtil.buildSTableName(instInfo.getInstId(), rate);
        String tableName = DataServiceUtil.buildTableName(instInfo.getInstId(), rate, pointId);

        /* 封装字段 */
        StringBuilder sb = new StringBuilder("insert into " + tableName + "(ts,");
        for (AttrIdVal value : values) {
            sb.append(enColName(value.getAttrId(), directId) + ", ");
        }
        sb.append("status_" + directId + ",del");

        /* 封装值 */
        sb.append(") using " + sTableName + " TAGS (" + pointId + ") VALUES ('" + time + "',");
        for (AttrIdVal value : values) {
            sb.append(value.getAttrVal() + ", ");
        }
        sb.append("0,false)");

        System.out.println(sb.toString());

        /* 执行插入sql */
        tdBaseService.executeSql(sb.toString(), rate);
    }

    public void batchSave(List<AttrVal> values) {
        /*  先根据频率分组  */
        Map<String, List<AttrVal>> rateMap = values.stream().filter(e -> e.getRate() != null)
                .collect(Collectors.groupingBy(AttrVal::getRate));
        rateMap.forEach((rate, rateValues) -> {
            AtomicInteger weight = new AtomicInteger(0);
            List<Pair<String, Integer>> sqlWeightList = new ArrayList<>();
            Map<String, List<AttrVal>> instMap = rateValues.stream().collect(Collectors.groupingBy(AttrVal::getInst));
            instMap.forEach((inst, instValues) -> {
                Map<String, List<AttrVal>> pointMap = instValues.stream().collect(Collectors.groupingBy(AttrVal::getPoint));
                pointMap.forEach((point, pointValues) -> {
                    Pair<String, Integer> sw = buildInsertSql(rate, inst, point, pointValues);
                    sqlWeightList.add(sw);
                    /*  判断是否需要执行写入  */
                    int w = weight.addAndGet(sw.getRight());
                    if (w >= 10000) {
                        /*  执行sql  */
                        List<String> ss = sqlWeightList.stream().map(Pair::getLeft).collect(Collectors.toList());
                        sqlWeightList.clear();
                        weight.set(0);
                        String sql = "INSERT INTO " + String.join(" ", ss);
                        tdBaseService.executeSql(sql, rate);
                    }
                });
            });
            /*  写入暂存区剩余数据  */
            if (!sqlWeightList.isEmpty()) {
                List<String> ss = sqlWeightList.stream().map(Pair::getLeft).collect(Collectors.toList());
                String sql = "INSERT INTO " + String.join(" ", ss);
                tdBaseService.executeSql(sql, rate);
            }
        });
    }

    public Pair<String, Integer> buildInsertSql(String rate, String inst, String point, List<AttrVal> values) {
        boolean isHighRate = Constant.RATE_HIGH.equals(rate);
        AtomicInteger index = new AtomicInteger(0);
        StringBuilder sb = new StringBuilder();
        String tablePrefix = DataServiceUtil.convertRate(rate);
        Map<String, List<AttrVal>> colMap = values.stream().collect(Collectors.groupingBy(e -> "a_" + e.getAttr() + "_" + e.getDirect()));
        colMap.forEach((colName, attrValues) -> {
            sb.append(tablePrefix).append("_").append(inst).append("_").append(point)
                    .append(" (ts, ").append(colName);
            /*  非高频数据插入扩展字段  */
            if (!isHighRate) {
                sb.append(", ").append(colName.replace("a_", "o_"))
                        .append(", status_0,status_1,status_2,status_3");
            }
            sb.append(", del)")
                    .append(" using ").append(tablePrefix).append("_").append(inst)
                    .append(" TAGS (").append(point).append(") VALUES ");

            attrValues.forEach(e -> {
                sb.append("( '").append(TimeUtil.format2Ms(e.getTs())).append("', ").append(e.getVal());
                /*  非高频数据插入扩展字段  */
                if (!isHighRate) {
                    sb.append(", null, 0, 0, 0, 0");
                }
                sb.append(", false), ");
                index.incrementAndGet();
            });
            sb.deleteCharAt(sb.length() - 2);
        });
        return Pair.of(sb.toString(), index.get());
    }

    /**
     * 保存数据到表
     */
    public void batchSave(List<InsertRecord> insertRecords, Map<String, List<Map<String, Object>>> historyData, Boolean isOverride) {
//        log.info("----------");
//        for (InsertRecord insertRecord : insertRecords) {
//            System.out.println(insertRecord.toString());
//        }
//        log.info("----------");
        if (ObjectUtils.isEmpty(insertRecords)) {
            return;
        }

        /* 按仪器、频率分组 */
        Map<String, List<InsertRecord>> instRateMap = insertRecords.stream().collect(Collectors.groupingBy(e -> e.getInstId() + "-" + e.getRate()));
        instRateMap.forEach((instAndRate, records) -> {
            String rate = instAndRate.split("-")[1];

            /* 构造插入语句并执行 */
            String sql = buildInsertSql(rate, records, historyData, isOverride);
            if (sql.getBytes().length < 1024 * 1024) {
                tdBaseService.executeSql(sql, rate);
            } else {
                List<List<InsertRecord>> parts = partRecords(rate, records, historyData, isOverride);
                for (List<InsertRecord> part : parts) {
                    sql = buildInsertSql(rate, part, historyData, isOverride);
                    tdBaseService.executeSql(sql, rate);
                }
            }
        });
    }

    /**
     * 保存高频数据到表
     */
    public void batchHighSave(List<InsertRecord> insertRecords) {
        if (ObjectUtils.isEmpty(insertRecords)) {
            return;
        }

        /* 按仪器、频率分组 */
        Map<String, List<InsertRecord>> instRateMap = insertRecords.stream().collect(Collectors.groupingBy(e -> e.getInstId() + "-" + e.getRate()));
        instRateMap.forEach((instAndRate, records) -> {
            String rate = instAndRate.split("-")[1];

            /* 构造插入语句并执行 */
            String sql = buildHighInsertSql(rate, records);
            if (sql.getBytes().length < 1024 * 1024) {
                tdBaseService.executeSql(sql, rate);
            } else {
                List<List<InsertRecord>> parts = partHighRecords(rate, records);
                for (List<InsertRecord> part : parts) {
                    sql = buildHighInsertSql(rate, part);
                    tdBaseService.executeSql(sql, rate);
                }
            }
        });
    }

    /**
     * 拆分待插入集合数据
     */
    private List<List<InsertRecord>> partRecords(String rate, List<InsertRecord> records, Map<String, List<Map<String, Object>>> historyData, Boolean isOverride) {
        int partSize = 2;
        for (int i = 0; i < 100; i++) {
            boolean allIsOk = true;
            List<List<InsertRecord>> parts = Lists.partition(records, records.size() / partSize);
            L1:
            for (List<InsertRecord> part : parts) {
                String sql = buildInsertSql(rate, part, historyData, isOverride);
                if (sql.getBytes().length >= 1024 * 1024) {
                    partSize *= 2;
                    allIsOk = false;
                    break L1;
                }
            }
            if (allIsOk) {
                return parts;
            }
        }
        return Lists.partition(records, 1);
    }

    /**
     * 拆分待插入集合数据
     */
    private List<List<InsertRecord>> partHighRecords(String rate, List<InsertRecord> records) {
        int partSize = 2;
        for (int i = 0; i < 100; i++) {
            boolean allIsOk = true;
            List<List<InsertRecord>> parts = Lists.partition(records, records.size() / partSize);
            L1:
            for (List<InsertRecord> part : parts) {
                String sql = buildHighInsertSql(rate, part);
                if (sql.getBytes().length >= 1024 * 1024) {
                    partSize *= 2;
                    allIsOk = false;
                    break L1;
                }
            }
            if (allIsOk) {
                return parts;
            }
        }
        return Lists.partition(records, 1);
    }

    /**
     * 构造插入语句
     */
    private String buildInsertSql(String rate, List<InsertRecord> records, Map<String, List<Map<String, Object>>> historyData, Boolean isOverride) {
        StringBuilder sb = new StringBuilder("insert into ");
        for (InsertRecord record : records) {
            Integer instId = record.getInstId();
            String time = record.getTime();
            Integer pointId = record.getPointId();
            Integer directId = record.getDirectId();
            Set<AttrIdVal> values = record.getValues();

            if (!isOverride) {
                replaceVal(historyData, time, pointId, directId, values);
            }

            /* 表名 */
            String sTableName = DataServiceUtil.buildSTableName(instId, rate);
            String tableName = DataServiceUtil.buildTableName(instId, rate, pointId);

            /* 封装字段 */
            sb.append(tableName + "(ts,");
            for (AttrIdVal value : values) {
                sb.append(enColName(value.getAttrId(), directId) + ",");
                if (!"高频".equals(rate)) {
                    sb.append(enOldColName(value.getAttrId(), directId) + ",");
                }
            }
            if ("高频".equals(rate)) {
                sb.append("del");
            } else {
                sb.append("status_0,status_1,status_2,status_3,del");
            }

            /* 封装值 */
            sb.append(") using " + sTableName + " TAGS (" + pointId + ") VALUES ('" + time + "',");
            for (AttrIdVal value : values) {
                if ("高频".equals(rate)) {
                    sb.append(value.getAttrVal() + ",");
                } else {
                    sb.append(value.getAttrVal() + ",NULL,");
                }
            }
            if ("高频".equals(rate)) {
                sb.append("false)");
            } else {
                sb.append("0,0,0,0,false)");
            }
        }
        return sb.toString();
    }

    /**
     * 构造高频插入语句
     */
    private String buildHighInsertSql(String rate, List<InsertRecord> records) {
        StringBuilder sb = new StringBuilder("insert into ");
        for (InsertRecord record : records) {
            Integer instId = record.getInstId();
            String time = record.getTime();
            Integer pointId = record.getPointId();
            Integer directId = record.getDirectId();
            Set<AttrIdVal> values = record.getValues();

            /* 表名 */
            String sTableName = DataServiceUtil.buildSTableName(instId, rate);
            String tableName = DataServiceUtil.buildTableName(instId, rate, pointId);

            /* 封装字段 */
            sb.append(tableName + "(ts,");
            for (AttrIdVal value : values) {
                sb.append(enColName(value.getAttrId(), directId) + ",");
            }
            sb.append("del");

            /* 封装值 */
            sb.append(") using " + sTableName + " TAGS (" + pointId + ") VALUES ('" + time + "',");
            for (AttrIdVal value : values) {
                sb.append(value.getAttrVal() + ",");
            }
            sb.append("false)");
        }
        return sb.toString();
    }

    /**
     * 构造插入语句
     */
    private String buildInsertSqlSpe(String rate, List<InsertRecord> records, Map<String, List<Map<String, Object>>> historyData, Boolean isOverride) {
        StringBuilder sb = new StringBuilder("insert into ");
        for (InsertRecord record : records) {
            Integer instId = record.getInstId();
            String time = record.getTime();
            Integer pointId = record.getPointId();
            Integer directId = record.getDirectId();
            Set<AttrIdVal> values = record.getValues();

            if (!isOverride) {
                replaceVal(historyData, time, pointId, directId, values);
            }

            /* 表名 */
            String sTableName = DataServiceUtil.buildSTableName(instId, rate);
            String tableName = DataServiceUtil.buildTableName(instId, rate, pointId);

            /* 封装字段 */
            sb.append(tableName + "(ts,");
            for (AttrIdVal value : values) {
                sb.append(enColName(value.getAttrId(), directId) + ",");
            }
            sb.append("del");

            /* 封装值 */
            sb.append(") using " + sTableName + " TAGS (" + pointId + ") VALUES ('" + time + "',");
            for (AttrIdVal value : values) {
                sb.append(value.getAttrVal() + ",");
            }
            sb.append("false)");
        }
        return sb.toString();
    }

    public void insertRecords(Integer instId, String rate, List<Map<String, Object>> records) {
        StringBuilder sb = new StringBuilder("insert into ");
        for (Map<String, Object> record : records) {
            Integer pointId = (Integer) record.get("point");

            String stableName = convertRate(rate) + "_" + instId;
            String tableName = convertRate(rate) + "_" + instId + "_" + pointId;
            sb.append(tableName + "(");
            record.forEach((colName, colVal) -> {
                if ("direct".equals(colName) || "point".equals(colName)) {
                    return;
                }
                sb.append(colName + ",");
            });
            sb.deleteCharAt(sb.length() - 1);
            sb.append(") using " + stableName + " TAGS (" + pointId + ") values (");
            record.forEach((colName, colVal) -> {
                if ("direct".equals(colName) || "point".equals(colName)) {
                    return;
                }
                sb.append("'" + colVal + "',");
            });
            sb.deleteCharAt(sb.length() - 1);
            sb.append(") ");
        }

        /* 执行插入sql */
        tdBaseService.executeSql(sb.toString(), rate);
    }

    /**
     * 不覆盖的话使用旧值替换新值
     */
    private void replaceVal(Map<String, List<Map<String, Object>>> groupMap, String time, Integer pointId, Integer directId, Set<AttrIdVal> values) {
        time = CommonUtil.completeMill(time);
        List<Map<String, Object>> list = groupMap.get(time + "-" + pointId);
        if (list != null && list.size() == 1) {
            Map<String, Object> record = list.get(0);
            for (AttrIdVal value : values) {
                Object val = record.get(enColName(value.getAttrId(), directId));
                if (val != null) {
                    value.setAttrVal((Float) val);
                }
            }
        }
    }

    private void removeNull(Set<AttrIdVal> values) {
        Iterator<AttrIdVal> it = values.iterator();
        while (it.hasNext()) {
            AttrIdVal next = it.next();
            if (next.getAttrVal() == null) {
                it.remove();
            }
        }
    }

    /**
     * 获取列名
     */
    public String enColName(Integer attrId, Integer directId) {
        return "a_" + attrId + "_" + directId;
    }

    /**
     * 获取列名
     */
    public String enDColName(Integer attrId, Integer directId) {
        return "d_" + attrId + "_" + directId;
    }

    /**
     * 获取列名
     */
    public String enD1ColName(Integer attrId, Integer directId) {
        return "d1_" + attrId + "_" + directId;
    }

    /**
     * 获取列名
     */
    public String enD2ColName(Integer attrId, Integer directId) {
        return "d2_" + attrId + "_" + directId;
    }

    /**
     * 获取old列名
     */
    public String enOldColName(Integer attrId, Integer directId) {
        return "o_" + attrId + "_" + directId;
    }

    /**
     * 获取old列名
     */
    public String enStatusColName(Integer directId) {
        return "status_" + directId;
    }

    /**
     * 获取列名
     */
    private String enColName(Integer attrId) {
        return "a_" + attrId + "_" + 0;
    }

    /**
     * 获取old列名
     */
    private String enOldColName(Integer attrId) {
        return "o_" + attrId + "_" + 0;
    }

    /**
     * 数据重新统计
     */
    public void dataReStat(String fieldNum, PointCommon point) {
        // 获取该测点所属监测项目
        InstDirectAttr info = monitorService.getInstDirectAttr(point.getId());
        if (info.getInstId() == null || ObjectUtils.isEmpty(info.getDirects()) || ObjectUtils.isEmpty(info.getAttrs())) {
            return;
        }
        Integer monitorId = monitorService.getMonitorIdByInst(info.getInstId());

        String[] rates = {"高频", "小时级", "分钟级"};
        for (String rate : rates) {
            String tableName = DataServiceUtil.buildTableName(info.getInstId(), rate, point.getId());
            /* 获取待统计属性ID */
            List<AttrCommon> attrs = info.getAttrs().stream().filter(e -> rate.equals(e.getRate())).collect(Collectors.toList());
            if (ObjectUtils.isEmpty(attrs)) {
                continue;
            }
            /* 创建统计表 */
            for (AttrCommon attr : attrs) {
                statMapper.createStatTableIfNotExist(fieldNum, monitorId, attr.getId());
            }

            String sql = "select * from " + tableName + " where del = false";
            List<Map<String, Object>> records = tdBaseService.selectMulti(sql, rate);
            if (ObjectUtils.isEmpty(records)) {
                continue;
            }
            log.info("{}-{}，{}条", point.getNo(), rate, records.size());

            // 数据按天分组
            Map<String, List<Map<String, Object>>> dayList = records.stream().collect(Collectors.groupingBy(r -> TimeUtil.format2Day((Date) r.get("ts"))));

            /* 统计 */
            List<Stat> statList = new ArrayList<>();
            for (Map.Entry<String, List<Map<String, Object>>> entry : dayList.entrySet()) {
                String day = entry.getKey();
                records = entry.getValue();
                for (AttrCommon attr : attrs) {
                    L1:
                    for (Direct direct : info.getDirects()) {
                        int directId = attr.getIsPolar() ? 0 : direct.getDirectId();
                        Stat s = statRecords(records, attr.getId(), directId);
                        s.setDay(day);
                        s.setPointId(point.getId());
                        s.setDirect(directId);
                        s.setAttrId(attr.getId());
                        if (s.getMeasureNum() != null && s.getMeasureNum() != 0) {
                            statList.add(s);
                        }
                        if (attr.getIsPolar()) {
                            break L1;
                        }
                    }
                }
            }
            if (!ObjectUtils.isEmpty(statList)) {
                // 数据分量分组
                Map<Integer, List<Stat>> attrMaps = statList.stream().collect(Collectors.groupingBy(Stat::getAttrId));
                attrMaps.forEach((attrId, stats) -> {
                    try {
                        statMapper.saveBatch(fieldNum, monitorId, attrId, stats);
                    } catch (Exception e) {
                        e.printStackTrace();
//                        for (Stat stat : statList) {
//                            try {
//                                statMapper.save(fieldNum, stat);
//                            } catch (Exception e1) {
//                            }
//                        }
                    }
                });
            }

        }
    }

//    /**
//     * 统计
//     */
//    public void stat(String fieldNum, Date day, Integer pointId, Integer direct) {
//        InstDirectAttr info = monitorService.getInstDirectAttr(pointId);
//        if (info.getInstId() == null) {
//            return;
//        }
//
//        /* 判断表是否存在 */
//        String tableName = "inst_" + info.getInstId() + "_" + pointId + "_" + direct;
//        if (!isTableExist(tableName, 2)) {
//            return;
//        }
//
//        /* 查询数据 */
//        String dayStr = TimeUtil.format2Day(day);
//        String startTime = dayStr + " 00:00:00";
//        String endTime = dayStr + " 23:59:59";
//
//        String sql = "select * from " + tableName + " where ts between '" + startTime + "' and '" + endTime + "' and approval in (0, 1)";
//        List<Map<String, Object>> records = selectBaseMulti(sql);
//        if (ObjectUtils.isEmpty(records)) {
//            statMapper.deleteByPointDirect(fieldNum, TimeUtil.format2Day(day), pointId);
//            return;
//        }
//
//        /* 获取里面所有属性ID */
//        Set<Integer> attrIds = getAllAttrIds(records);
//
//        /* 统计 */
//        statMapper.createStatTableIfNotExist(fieldNum);
//        List<Stat> statList = new ArrayList<>();
//        for (Integer attrId : attrIds) {
//            Stat s = statRecords(records, attrId);
//            s.setDay(TimeUtil.format2Day(day));
//            s.setPointId(pointId);
//            s.setDirect(direct);
//            s.setAttrId(attrId);
//            statMapper.del(fieldNum, s.getDay(), s.getPointId(), s.getDirect(), s.getAttrId());
//            if (s.getMeasureNum() != null && s.getMeasureNum() != 0) {
//                statList.add(s);
//            }
//        }
//        if (!ObjectUtils.isEmpty(statList)) {
//            try {
//                statMapper.saveBatch(fieldNum, statList);
//            } catch (Exception e) {
//                for (Stat stat : statList) {
//                    try {
//                        statMapper.save(fieldNum, stat);
//                    } catch (Exception e1) {
//                    }
//                }
//            }
//        }
//    }

    private Set<Integer> getAllAttrIds(List<Map<String, Object>> records) {
        Set<Integer> attrIds = new HashSet<>();
        records.forEach(record -> {
            record.forEach((colName, v) -> {
                if (colName != null && colName.startsWith("c_") && !colName.endsWith("_old")) {
                    attrIds.add(Integer.parseInt(colName.split("_")[1]));
                }
            });
        });
        return attrIds;
    }

    private Stat statRecords(List<Map<String, Object>> records, Integer attrId, int directId) {
        float max = Integer.MIN_VALUE;
        float min = Integer.MAX_VALUE;
        Date maxTime = null, minTime = null;
        float total = 0f;
        int count = 0;

        for (Map<String, Object> record : records) {
            Float val = (Float) record.get(enColName(attrId, directId));
            if (val == null) {
                continue;
            }

            Integer status = (Integer) record.get("status_" + directId);
            /* 审批不通过的不参与统计，同时记录审批不通过的数量 */
            if (status == 2) {
                continue;
            }

            if (val > max) {
                max = val;
                maxTime = (Date) record.get("ts");
            }
            if (val < min) {
                min = val;
                minTime = (Date) record.get("ts");
            }
            total += val;
            count++;
        }
        Stat statRes = new Stat();
        if (count != 0) {
            statRes.setMinValue(min);
            statRes.setMinValueTime(minTime);
            statRes.setMaxValue(max);
            statRes.setMaxValueTime(maxTime);
            statRes.setAvgValue(total / count);
            statRes.setMeasureNum(count);
        }
        return statRes;
    }

    public List<Datum> alarmProcessLine(Integer instId, Integer pointId, Integer direct, Integer attrId, String attrName, String time, String startTime, String endTime, Integer groupNum, String type) {
        AttrCommon attr = monitorService.getProAttrById(attrId);
        /* 查询目标数据并更新 */

        String tableName = convertRate(attr.getRate()) + "_" + instId + "_" + pointId;
        String colName = enColName(attrId, direct);

        List<Map<String, Object>> records = selectRecords(colName, tableName, attr.getRate(), time, startTime, endTime, groupNum, type);
        if (ObjectUtils.isEmpty(records)) {
            return Collections.emptyList();
        }

        Date startDate = TimeUtil.parse2Second(startTime);
        Date endDate = TimeUtil.parse2Second(endTime);
        List<Datum> list = new ArrayList<>();
        for (Map<String, Object> record : records) {
            Date ts = (Date) record.get("ts");
            if (ts.after(endDate) || ts.before(startDate)) {
                continue;
            }
            Datum datum = new Datum();
            datum.setTime(ts);
            datum.setPointId(pointId);
            datum.setDirection(direct);
            datum.setAttrId(attrId);
            datum.setAttrName(attrName);
            datum.setAttrVal((Float) record.get(colName));
            list.add(datum);
        }
        return list;
    }

    private List<Map<String, Object>> selectRecords(String colName, String tableName, String rate, String time, String startTime, String endTime, Integer groupNum, String type) {
        if (!ObjectUtils.isEmpty(time)) {
            if (groupNum != null && !ObjectUtils.isEmpty(type)) {
                String sign = "A".equals(type) ? "<" : ">";
                String order = "A".equals(type) ? "desc" : "asc";
                String sql = "select ts, " + colName + " from " + tableName + " where ts " + sign + "= '" + time + "' and del = false order by ts " + order + " limit " + groupNum;
                return tdBaseService.selectMulti(sql, rate);
            } else {
                String sql1 = "select ts, " + colName + " from " + tableName + " where ts <= '" + time + "' and del = false order by ts desc limit 30";
                String sql2 = "select ts, " + colName + " from " + tableName + " where ts > '" + time + "' and del = false order by ts asc limit 30";
                List<Map<String, Object>> records1 = tdBaseService.selectMulti(sql1, rate);
                List<Map<String, Object>> records2 = tdBaseService.selectMulti(sql2, rate);
                List<Map<String, Object>> records = new ArrayList<>();
                if (!ObjectUtils.isEmpty(records1)) {
                    records.addAll(records1);
                }
                if (!ObjectUtils.isEmpty(records2)) {
                    records.addAll(records2);
                }
                return records;
            }
        } else {
            String sql = "select ts, " + colName + " from " + tableName + " where ts between '" + startTime + "' and '" + endTime + "' and del = false limit 10000";
            return tdBaseService.selectMulti(sql, rate);
        }
    }

    /**
     * 获取晃点图数据
     */
    public List<Datum> querySlosh(SloshQo qo, String attrName) throws ParseException {
        InstDirectAttr info = monitorService.getInstDirectAttr(qo.getPointID());
        if (info.getInstId() == null || ObjectUtils.isEmpty(info.getAttrs())) {
            return Collections.emptyList();
        }

        /* 寻找目标属性ID */
        AttrCommon targetAttr = null;
        List<AttrCommon> attrs = info.getAttrs();
        for (AttrCommon attr : attrs) {
            /* 分量名称由 attr.getName().contains(attrName) 改为绝对匹配出现问题后再处理 */
            if (Objects.equals(attr.getName(), attrName)) {
                targetAttr = attr;
                break;
            }
        }

//        String attrId = null;
//        List<Map<String, Object>> records = this.selectBaseMulti("describe " + tabName);
//        List<Object> columns = records.stream().map(m -> m.get("field")).collect(Collectors.toList());
//        for (Object column : columns) {
//            String colName = new String((byte[]) column);
//            log.info("column:" + colName);
//            if (colName.startsWith("c_")) {
//                String[] arr = colName.split("_");
//                String attrNameById = monitorService.getProAttrById(Integer.parseInt(arr[1])).getName();
//                if (!ObjectUtils.isEmpty(attrName) && attrName.equals(attrNameById)) {
//                    attrId = arr[1];
//                    break;
//                }
//            }
//        }

        if (targetAttr == null) {
            return Collections.emptyList();
        }

//        long seconds = TimeUtil.secondsBetween(qo.getStartTime(), qo.getEndTime());
//        int size = 1000;
//        int interval;
//        if (seconds <= size) {
//            interval = 60;
//        } else {
//            interval = (int) (seconds / size);
//        }

//        String colName = "c_" + attrId;

//        List<Map<String, Object>> records1 = getData(colName, tabName, qo, interval, 1);
//        List<Map<String, Object>> records2 = getData(colName, tabName, qo, interval, 2);
//        if (records1.size() < 90 && interval > 60) {
//            // 需重新计算interval
//            int times = records1.size() / 3;
//            interval = 60 * (times + 1);
//            records1 = getData(colName, tabName, qo, interval, 1);
//        }
//        if (records2.size() < 90 && interval > 60) {
//            // 需重新计算interval
//            int times = records2.size() / 3;
//            interval = 60 * (times + 1);
//            records2 = getData(colName, tabName, qo, interval, 2);
//        }
        String tableName = convertRate(targetAttr.getRate()) + "_" + info.getInstId();
        String colName1 = enColName(targetAttr.getId(), 1);
        String colName2 = enColName(targetAttr.getId(), 2);

        List<Map<String, Object>> records1 = getData(colName1, tableName, qo, null, 1, targetAttr.getRate());
        List<Map<String, Object>> records2 = getData(colName2, tableName, qo, null, 2, targetAttr.getRate());

        List<Datum> vos = new ArrayList<>();
        for (int i = 0; i < records1.size(); i++) {
            Map<String, Object> record = records1.get(i);
            Datum datum = new Datum();
            datum.setTime((Date) record.get("ts"));
            datum.setAttrVal((Float) record.get(colName1));
            datum.setDirection(1);
            vos.add(datum);
        }
        for (int i = 0; i < records2.size(); i++) {
            Map<String, Object> record = records2.get(i);
            Datum datum = new Datum();
            datum.setTime((Date) record.get("ts"));
            datum.setAttrVal((Float) record.get(colName2));
            datum.setDirection(2);
            vos.add(datum);
        }
        return vos;
    }

    private List<Map<String, Object>> getData(String colName, String tabName, SloshQo qo, Integer interval, int direct, String rate) {
//        String sql1 = "select FIRST(ts) AS ts, FIRST(" + colName + ") AS " + colName + " from " + tabName + " where ts between '" + qo.getStartTime() + "' and '" + qo.getEndTime() + "' and point = " + qo.getPointID() + " and direct = " + direct + " and approval != 3 INTERVAL(" + interval + "s) limit 1000";
        String sql1 = "select ts, " + colName + " from " + tabName + " where ts between '" + qo.getStartTime() + "' and '" + qo.getEndTime() + "' and point = " + qo.getPointID() + " and del = false";
        System.out.println("sql1:" + sql1);
        return tdBaseService.selectMulti(sql1, rate);
    }

    /**
     * 获取测点最新成果量数据
     */
    public List<LastDataVo> pointLastAchieveData3(Integer instId, List<Direct> directs, List<AttrCommon> attrs, Integer pointId) {
        List<LastDataVo> vos = new ArrayList<>();
        String tableName = DataServiceUtil.buildTableName(instId, "小时级", pointId);

        /* 查询最新时间 */
        String sql = "select last(ts) AS last_ts from " + tableName + " where point = " + pointId + " and del = false";

        Map<String, Object> resOne = tdBaseService.selectOne(sql, "小时级");
        Date lastTs = null;
        if (!ObjectUtils.isEmpty(resOne)) {
            lastTs = (Date) resOne.get("last_ts");
        }
        if (lastTs == null) {
            return Collections.emptyList();
        }

        sql = "select * from " + tableName + " where point = " + pointId + " and ts = '" + lastTs + "' and del = false";
        System.out.println(sql);
        Map<String, Object> record = tdBaseService.selectOne(sql, "小时级");
        if (ObjectUtils.isEmpty(record)) {
            return Collections.emptyList();
        }

        for (Direct direct : directs) {
            for (AttrCommon attr : attrs) {
                LastDataVo vo = new LastDataVo(lastTs, direct.getDirectName(), attr.getName());
                Object attrVal = record.get(enColName(attr.getId(), direct.getDirectId()));
                vo.setVal(attrVal == null ? null : (Float) attrVal);
                vos.add(vo);
            }
        }
        return vos;
    }

    public Float selectByTime(String rate, String time, Integer pointId, Integer directId, Integer attrId, boolean isPolar, Integer type, Integer millis) throws ParseException {
        directId = isPolar ? 0 : directId;
        InstDirectAttr instInfo = monitorService.getInstDirectAttr(pointId);
        if (instInfo.getInstId() == null || ObjectUtils.isEmpty(instInfo.getAttrs())) {
            return null;
        }

        /* 查询目标数据并更新 */
        String tableName = convertRate(rate) + "_" + instInfo.getInstId() + "_" + pointId;

        String sql = null;
        if (type == null || type == 1) {
            sql = "select " + enColName(attrId, directId) + " from " + tableName + " where ts = '" + time + "' and del = false";
        } else if (type == 2) {
            long timeMillis = TimeUtil.parse2Second(time).getTime();
            Date sDate = new Date();
            sDate.setTime(timeMillis - millis);
            String sTime = TimeUtil.format2Second(sDate);

            Date dDate = new Date();
            dDate.setTime(timeMillis + millis);
            String dTime = TimeUtil.format2Second(dDate);
            sql = "select LAST(" + enColName(attrId, directId) + ") AS " + enColName(attrId, directId) + " from " + tableName + " where ts between '" + sTime + "' and '" + dTime + "' and del = false";
        } else if (type == 3) {
            sql = "select LAST(" + enColName(attrId, directId) + ") AS " + enColName(attrId, directId) + " from " + tableName + " where ts <= '" + time + "' and del = false";
        }
        Map<String, Object> record = tdBaseService.selectOne(sql, rate);
        if (record.size() != 0) {
            return (Float) record.get(enColName(attrId, directId));
        }
        return null;
    }

    /**
     * 查询历史数据
     */
    public Map<String, List<Map<String, Object>>> getHistoryData(List<InsertRecord> toSaveRecords, String rate, Boolean isOverride) {
        if (isOverride) {
            return Collections.emptyMap();
        }
        Integer instId = toSaveRecords.get(0).getInstId();
        List<String> times = toSaveRecords.stream().map(InsertRecord::getTime).collect(Collectors.toList());
        String timesStr = linkByComma(times);
        StringBuilder sql = new StringBuilder();
        String sTableName = convertRate(rate) + "_" + instId;
        sql.append("select * from " + sTableName + " where ts in (" + timesStr + ")");
        List<Map<String, Object>> records = tdBaseService.selectMulti(sql.toString(), rate);
        if (ObjectUtils.isEmpty(records)) {
            return Collections.emptyMap();
        }
        return records.stream().collect(Collectors.groupingBy(e -> CommonUtil.completeMill(e.get("ts").toString()) + "-" + e.get("point")));
    }

    private String linkByComma(List<String> times) {
        StringBuilder sb = new StringBuilder();
        for (String time : times) {
            sb.append("'");
            sb.append(time);
            sb.append("',");
        }
        sb.deleteCharAt(sb.length() - 1);
        return sb.toString();
    }

    /**
     * 查询聚合数据
     */
    public List<Map<String, Object>> getTogetherData(Integer instId, String rate, List<PointCommon> pointIds, Integer attrId, String direct, String startTime, String endTime, String statItem) {
        List<Map<String, Object>> list = new ArrayList<>();
        String tableName = DataServiceUtil.buildSTableName(instId, rate);

        for (String d : direct.split(",")) {
            int directId = Integer.parseInt(d);
            String colName = enColName(attrId, directId);
            /* 构造查询sql */
            StringBuilder sb = new StringBuilder("select ");
            if (!ObjectUtils.isEmpty(statItem) && (statItem.contains("各测点") || statItem.contains("各海上设施"))) {
                // 按测点分组
                sb.append(" point,");
            }
            sb.append("min(" + colName + ") AS min, max(" + colName + ") AS max, avg(" + colName + ") AS avg from " + tableName + " where ");
            // 时间
            sb.append("ts between '" + startTime + "' and '" + endTime + "' ");
            // 测点
            sb.append("and point in " + getStrPointIds(mapPointIds(pointIds)) + " ");
            // 有效数据
            sb.append("and status_" + directId + " != 2 and del = false");
            if (!ObjectUtils.isEmpty(statItem) && (statItem.contains("各测点") || statItem.contains("各海上设施"))) {
                // 按测点分组
                sb.append(" group by point");
            }
            log.info(sb.toString());
            List<Map<String, Object>> list1 = tdBaseService.selectMulti(sb.toString(), rate);
            list.addAll(list1);
        }
        /* 替换TD返回的数据中统计为null串的数据 */
        list.forEach(this::replaceNullStr);
        return list;
    }

    /**
     * 替换 map中的null串
     *
     * @param map map
     */
    private void replaceNullStr(Map<String, Object> map) {
        Set<String> keys = map.keySet();
        for (String key : keys) {
            if (map.get(key) != null
                    && map.get(key) instanceof String
                    && Objects.equals("null", ((String) map.get(key)).toLowerCase())) {
                map.put(key, null);
            }
        }
    }

    private Integer[] mapPointIds(List<PointCommon> pointIds) {
        Integer[] ids = new Integer[pointIds.size()];
        for (int i = 0; i < pointIds.size(); i++) {
            ids[i] = pointIds.get(i).getId();
        }
        return ids;
    }

//    /**
//     * 复制数据
//     */
//    public void copyData(Integer instId, List<AttrCommon> attrs, List<PointCommon> points) {
//        if (ObjectUtils.isEmpty(points)) {
//            return;
//        }
//
//        int x = 0;
//        for (PointCommon point : points) {
//            /* 查询历史超级表中数据 */
//            String stableName = "inst_" + instId;
//            String sql = "select * from " + stableName + " where point = " + point.getId() + " and approval != 3";
//            List<Map<String, Object>> records = tdBaseService.selectBaseMulti(sql);
//            log.error(point.getNo() + " : (" + x++ + "/" + points.size() + ")， " + records.size());
//            if (ObjectUtils.isEmpty(records)) {
//                continue;
//            }
//
//            List<List<Map<String, Object>>> partRecords = Lists.partition(records, 200);
//            for (List<Map<String, Object>> partRecord : partRecords) {
//                CommonUtil.sleepNMillSeconds(20);
//                Map<String, List<AttrCommon>> rateAttrs = attrs.stream().collect(Collectors.groupingBy(AttrCommon::getRate));
//                rateAttrs.forEach((rate, rateAttrs1) -> {
//                    StringBuilder sb = new StringBuilder("insert into ");
//                    for (Map<String, Object> record : partRecord) {
//                        for (AttrCommon attr : rateAttrs1) {
//                            if (34 == attr.getId() || 35 == attr.getId()) {
//                                continue;
//                            }
//                            Float val = (Float) record.get("c_" + attr.getId());
//                            Float oldVal = (Float) record.get("c_" + attr.getId() + "_old");
//                            if (val == null && oldVal == null) {
//                                continue;
//                            }
//
//                            Date ts = (Date) record.get("ts");
//                            Integer pointId = (Integer) record.get("point");
//                            Integer directId = (Integer) record.get("direct");
//                            Integer approval = (Integer) record.get("approval");
//
//                            if (attr.getIsPolar()) {
//                                directId = 0;
//                            }
//
//                            /* 表名 */
//                            String sTableName = DataServiceUtil.buildSTableName(instId, rate);
//                            String tableName = DataServiceUtil.buildTableName(instId, rate, pointId);
//
//                            /* 封装字段 */
//                            sb.append(tableName + "(ts,");
//                            sb.append(enColName(attr.getId(), directId) + ",");
//                            sb.append(enOldColName(attr.getId(), directId) + ",");
//                            sb.append("status_" + directId);
//                            sb.append(",del");
//
//                            /* 封装值 */
//                            sb.append(") using " + sTableName + " TAGS (" + pointId + ") VALUES ('" + ts + "',");
//                            sb.append(val + "," + oldVal + ",");
//                            sb.append(approval + ",false) ");
//                        }
//                    }
//                    if (!"insert into ".equals(sb.toString())) {
////                        executeSql2(sb.toString());
//                    }
//                });
//            }
//        }
//    }

    /**
     * 拼接基础待查询属性列
     */
    public String makeSelectAttrCols(List<AttrCommon> attrs, List<Direct> instDirects, String rate) {
        StringBuilder sb = new StringBuilder();
        if (Constant.RATE_HIGH.equals(rate)) {
            sb.append("ts,point,");
        } else {
            sb.append("ts,point,status_0,status_1,status_2,status_3,");
        }

        for (AttrCommon attr : attrs) {
            List<Direct> directs = tdBaseService.getFinalDirects(attr.getIsPolar(), instDirects);
            for (Direct direct : directs) {
                sb.append(enColName(attr.getId(), direct.getDirectId()) + ",");
            }
        }
        sb.deleteCharAt(sb.length() - 1);
        return sb.toString();
    }

    /**
     * 拼接基础待查询属性列
     */
    public List<String> makeSelectAttrColsWithHigh(List<AttrCommon> attrs, List<Direct> instDirects, String sufSql) {
        List<String> list = new ArrayList<>();

        for (AttrCommon attr : attrs) {
            List<Direct> directs = tdBaseService.getFinalDirects(attr.getIsPolar(), instDirects);
            for (Direct direct : directs) {
                StringBuilder sb = new StringBuilder();
                sb.append("select 1 AS type, ts AS ts, min(" + enColName(attr.getId(), direct.getDirectId()) + ") AS " + enColName(attr.getId(), direct.getDirectId()) + sufSql);
                sb.append(" union ");
                sb.append("select 2 AS type, ts AS ts, max(" + enColName(attr.getId(), direct.getDirectId()) + ") AS " + enColName(attr.getId(), direct.getDirectId()) + sufSql);
                sb.append(" union ");
                sb.append("select 3 AS type, null AS ts, avg(" + enColName(attr.getId(), direct.getDirectId()) + ") AS " + enColName(attr.getId(), direct.getDirectId()) + sufSql);
                sb.append(" union ");
                sb.append("select 4 AS type, null AS ts, count(" + enColName(attr.getId(), direct.getDirectId()) + ") AS " + enColName(attr.getId(), direct.getDirectId()) + sufSql);
                list.add(sb.toString());
            }
        }
        return list;
    }

    /**
     * 获取采样时间间隔
     */
    public int getSampleInterval(String startTime, String endTime, int size) {
        int seconds = TimeUtil.secondsBetween(startTime, endTime);
        if (seconds <= size) {
            return 60;
        }
        return (int) (seconds / size);
    }

    private AttrCommon getAttrFromCache(Integer attrId) {
        String key = RedisKey.ATTR_COMMON_INFO + attrId;
        Object o = redisTemplate.opsForValue().get(key);
        if (o == null) {
            AttrCommon attrInfo = monitorService.getProAttrById(attrId);
            redisTemplate.opsForValue().set(key, JSON.toJSONString(attrInfo), 5, TimeUnit.MINUTES);
            return attrInfo;
        }
        return JSON.parseObject(o.toString(), AttrCommon.class);
    }

    private InstDirectAttr getInstDirectAttrFromCache(Integer pointId) {
        String key = RedisKey.INST_DIRECT_ATTR + pointId;
        Object o = redisTemplate.opsForValue().get(key);
        if (o == null) {
            InstDirectAttr info = monitorService.getInstDirectAttr(pointId);
            redisTemplate.opsForValue().set(key, JSON.toJSONString(info), 5, TimeUnit.MINUTES);
            return info;
        }
        return JSON.parseObject(o.toString(), InstDirectAttr.class);
    }

    /**
     * 查询自动化设备最新数据
     */
    public List<LatestDataVo> queryAutoLatestData(LatestDataQo qo, String userId) {
        long s = 0, e = 0;
        long nowTs = System.currentTimeMillis();
        if (qo.getInterval() > 500) {
            // 长时段
            s = nowTs - 25 * 60 * 1000;
            e = nowTs - 5 * 1000;
        } else {
            // 短时段
            s = nowTs - 35 * 1000;
            e = nowTs - 10 * 1000;
        }
        List<LatestDataVo> vos = new ArrayList<>();
        for (String id : qo.getIds()) {
            /* 获取测点和方向 */
            String[] arr = id.split("-");
            Integer pointId = Integer.parseInt(arr[0]);
            Integer attrId = Integer.parseInt(arr[1]);
            Integer directId = Integer.parseInt(arr[2]);

            /* 由测点获取仪器信息 */
            InstDirectAttr instInfo = getInstDirectAttrFromCache(pointId);
            AttrCommon attr = getAttrFromCache(attrId);
            if (attr == null) {
                continue;
            }

            /* 结果集 */
            LatestDataVo vo = new LatestDataVo(id);

            /* 时域图 */
            getTimeData(vo, pointId, attr, directId, qo.getInterval(), s, e);

            /* 频域图 */
            if (qo.getFftFlag()) {
                String tableName = DataServiceUtil.buildTableName(instInfo.getInstId(), attr.getRate(), pointId);
                String colName = enColName(attrId, directId);
                getFftData(vo, tableName, colName, attr, id);
            }

            /*  晃点图  */
            if (qo.getSloshFlag()) {
                getSloshData(pointId, vo);
            }

            if (!ObjectUtils.isEmpty(vo.getVs())) {
                vos.add(vo);
            }
        }
        return vos;
    }

    private void getSloshData(Integer pointId, LatestDataVo vo) {
        if (ObjectUtils.isEmpty(vo.getVs())) {
            return;
        }
        long now = new Date().getTime();
        String key = RedisKey.SLOSH + pointId;
        Long preTime = localCache.get(key);
        if (preTime == null) {
            List<SloshVo> list = new ArrayList<>(60);
            /* 加载 60 个点 */
            long s = System.currentTimeMillis();
            for (int i = 0; i < 60; i++) {
                Date endTime = new Date((now / 1000 / 60) * 1000 * 60 - i * 60 * 1000);
                Date startTime = TimeUtil.addSeconds(endTime, -60);
                SloshVo slosh = dynamicGraphService.calcSlosh(pointId, startTime, endTime);
                if (slosh != null) {
                    list.add(slosh);
                }
            }
            log.info("测点[{}]加载晃点图数据:{}-耗时:{}", pointId, list, System.currentTimeMillis() - s);
            if (!ObjectUtils.isEmpty(list)) {
                vo.setSloshList(list);
            }
            localCache.set(key, now, 60, TimeUnit.MINUTES);
        } else if (now - preTime >= 60 * 1000) {
            /* 超过 60 秒，重新加载一个点 */
            Date endTime = new Date((now / 1000 / 60) * 1000 * 60);
            Date startTime = TimeUtil.addSeconds(endTime, -60);
            long s = System.currentTimeMillis();
            SloshVo slosh = dynamicGraphService.calcSlosh(pointId, startTime, endTime);
            log.info("测点[{}]加载晃点图数据:{}-耗时:{}", pointId, slosh, System.currentTimeMillis() - s);
            if (slosh != null) {
                vo.setSloshList(Collections.singletonList(slosh));
                localCache.set(key, now, 60, TimeUnit.MINUTES);
            }
        }

    }

    /**
     * 获取时域图数据
     */
    private void getTimeData(LatestDataVo vo, Integer pointId, AttrCommon attr, Integer directId, int interval, long s, long e) {
        String key = RedisKey.LAST_AUTO_DATA + CommonUtil.join("-", pointId, directId, attr.getId());
        if (interval >= 1000) {
            key += "-long";
        }

        LinkedList<Val> queue = localCache.get(key);
        vo.setVs(handle(queue, key, s, e));

//        List<Val> vs = Arrays.asList(new Val(new Date(), 0.12f), new Val(new Date(), 0.15f));
//        vo.setVs(vs);
    }

    private List<Val> handle(RateLimitedQueue<Val> queue, String key, long sT, long eT) {
        if (ObjectUtils.isEmpty(queue)) {
            return Collections.emptyList();
        }

        return queue.getAsList();
    }

    private List<Val> handle(LinkedList<Val> queue, String key, long sT, long eT) {
        if (ObjectUtils.isEmpty(queue)) {
            return Collections.emptyList();
        }

        if (key.endsWith("-long")) {
            return queue;
        }
        try {
            // v1
            List<Val> vals = queue.stream().filter(e -> (e.getT().getTime() > sT) && (e.getT().getTime() < eT)).collect(Collectors.toList());
            return vals;

//            return v2(queue);
        } catch (Exception e) {
        }
        return Collections.emptyList();
    }

    /**
     * 这个方法不要动，不然怎么调过程线的效果都不好
     */
    private List<Val> v2(List<Val> vals) {
        int size = vals.size();
        long lastTs = vals.get(size - 1).getT().getTime();
        List<Val> res = new ArrayList<>(size);
        for (int i = vals.size(); i >= 0; i--) {
            res.set(i, new Val(new Date(lastTs), vals.get(i).getV()));
            lastTs -= 50;
        }
        return res;
    }

//    private List<Val> newValues(List<Val> vals) {
//        long s = vals.get(0).getT().getTime();
//        List<Val> list = new ArrayList<>(vals.size());
//        for (int i = 0; i < vals.size(); i++) {
//            list.add(new Val(new Date(s), vals.get(i).getV()));
//            s += 60;
//        }
//        return list;
//    }

    /**
     * 获取频域图数据
     */
    private void getFftData(LatestDataVo vo, String tableName, String colName, AttrCommon attr, String id) {
        /* 查看缓存数据是否存在 */
        String key = RedisKey.FFT + id;
        Boolean isExist = localCache.get(key);
        if (isExist != null) {
            return;
        }

        /* 获取开始时间和结束时间 */
        Date endTime = TimeUtil.only2Hour();
        Date startTime = TimeUtil.addSecondsForEven(endTime, -60 * 5);

        /* 查询数据 */
        String sql = "select ts, " + colName + " from " + tableName + " where ts >= '" + TimeUtil.format2Second(startTime) + "' and ts <= '" + TimeUtil.format2Second(endTime) + "' and " + colName + " is not null";
        long s = System.currentTimeMillis();
        List<Map<String, Object>> records = tdBaseService.selectMulti(sql, attr.getRate());
        if (ObjectUtils.isEmpty(records)) {
            return;
        }

        List<AccSmallVo> vos = new ArrayList<>(records.size());
        for (Map<String, Object> m : records) {
            vos.add(new AccSmallVo((Float) m.get(colName)));
        }

        FrequencyVo frequencyVo = frequencyService.analysisFrequency(vos);
        vo.setFrequencyVo(frequencyVo);
        localCache.set(key, true, seconds2nextHour(), TimeUnit.SECONDS);
        long e = System.currentTimeMillis();
        log.info("FFT查询耗时：{}ms", e - s);
    }

    /**
     * 获取当前时间到下一个小时的间隔秒数
     */
    private static long seconds2nextHour() {
        // 获取当前时间
        LocalTime now = LocalTime.now();

        // 目标时间为下一个小时
        LocalTime target = now.withMinute(0).withSecond(0).withNano(0).plusHours(1);
        return ChronoUnit.SECONDS.between(now, target);
    }

    /**
     * 数据减去均值
     */
    private void valueSubAvg(List<AccSmallVo> vos) {
        /* 求均值 */
        BigDecimal sum = new BigDecimal(0d);
        int count = 0;
        for (AccSmallVo accVo : vos) {
            if (accVo.getVal() != null) {
                sum = sum.add(new BigDecimal(accVo.getVal()));
                count++;
            }
        }
        float avg = sum.floatValue() / count;

        for (int i = 0; i < vos.size(); i++) {
            vos.get(i).setVal(vos.get(i).getVal() - avg);
        }
    }

    /**
     * 获取频谱X轴数据
     */
    private List<Double> getXArr(int size) {
        List<Double> xArr = new ArrayList<>();
        double sRate = 50;
        BigDecimal bigStep = new BigDecimal(sRate).divide(new BigDecimal(size), 8, RoundingMode.HALF_UP);

        BigDecimal i = new BigDecimal(0d);
        while (i.doubleValue() <= sRate) {
            xArr.add(i.doubleValue());
            i = i.add(bigStep);
        }
        xArr = xArr.subList(0, xArr.size() / 2);
        xArr.add(sRate / 2);
        return xArr;
    }

    public List<Map<String, Object>> getByTimeBetween(Integer instId, Integer[] pointIds, String startTime, String endTime, String rate) {
        String sTableName = DataServiceUtil.buildSTableName(instId, rate);
        String sql = "select point, ts from " + sTableName + " where ts between '" + startTime + "' and '" + endTime + "' and del = false and point in " + getStrPointIds(pointIds) + " ";
        System.out.println(sql);
        log.info("查询待删除所有数据的sql：{}", sql);
        return tdBaseService.selectMulti(sql, rate);
    }

    public void del(Integer instId, Integer[] pointIds, String startTime, String endTime, String rate) {
        String sTableName = DataServiceUtil.buildSTableName(instId, rate);
        String sql = "delete from " + sTableName + " where ts between '" + startTime + "' and '" + endTime + "' and point in " + getStrPointIds(pointIds) + " ";
        log.info("删除数据的sql：{}", sql);
        tdBaseService.executeSql(sql, rate);
    }

}


















