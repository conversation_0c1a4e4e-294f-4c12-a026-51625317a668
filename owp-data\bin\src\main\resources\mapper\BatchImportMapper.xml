<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hdec.data.mapper.BatchImportMapper">

    <select id="selectCfg" resultType="com.hdec.data.domain.BatchImportCfg">
        select * from
            owp_batch_import_cfg
        where
            field_num = #{fieldNum} and `type` = #{type}
    </select>

    <select id="fieldCfg" resultType="com.hdec.data.domain.BatchImportCfg">
        select * from
            owp_batch_import_cfg
        where
            field_num = #{fieldNum}
    </select>

    <insert id="saveCfg" parameterType="com.hdec.data.domain.BatchImportCfg">
        insert into owp_batch_import_cfg (`type`, `is_override`, `param`, `field_num`)
        values (#{type}, #{isOverride}, #{param}, #{fieldNum})
    </insert>

    <select id="deleteCfg">
        delete from
            owp_batch_import_cfg
        where
            field_num = #{fieldNum} and `type` = #{type}
    </select>

    <update id="updateList" parameterType="com.hdec.data.domain.BatchImportCfg">
        <foreach collection="configs" item="config" separator=";">
            UPDATE owp_batch_import_cfg
            <trim prefix="set" suffixOverrides=",">
                <if test="config.param != null">`param` = #{config.param},</if>
            </trim>
            WHERE
                id = #{config.id}
        </foreach>
    </update>

</mapper>