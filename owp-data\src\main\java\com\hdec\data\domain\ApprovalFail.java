package com.hdec.data.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * 审批不通过统计实体类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class ApprovalFail {

    /** 自增主键 */
    private Integer id;

    /** 测点ID */
    private Integer pointId;

    /** 天 */
    private Date day;

    /** 审核不通过数量 */
    private Integer noPassNum;

    /** 风场编码 */
    private String fieldNum;

}
