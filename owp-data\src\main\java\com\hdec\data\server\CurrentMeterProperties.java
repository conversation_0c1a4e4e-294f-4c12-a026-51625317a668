package com.hdec.data.server;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

@Data
@ConfigurationProperties(prefix = "server.current-meter", ignoreUnknownFields = true)
public class CurrentMeterProperties {
    private boolean enable = false;
    private int acceptor = 1;
    private int worker = 1;
    private int port = 9106;
    private int timeout = 10000;
}
