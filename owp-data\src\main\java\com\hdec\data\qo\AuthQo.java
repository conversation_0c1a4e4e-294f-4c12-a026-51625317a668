package com.hdec.data.qo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * 加速度查询类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class AuthQo {

    /**
     * 密钥
     */
    @NotBlank(message = "密钥不允许为空")
    private String secret;

    /**
     * 盐
     */
    @NotBlank(message = "盐不允许为空")
    private String salt;
}
