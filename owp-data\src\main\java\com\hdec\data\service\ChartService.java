package com.hdec.data.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.hdec.common.domain.AttrCommon;
import com.hdec.common.domain.InstCommon;
import com.hdec.common.domain.PointCommon;
import com.hdec.common.util.*;
import com.hdec.data.domain.Chart;
import com.hdec.data.domain.ChartAttr;
import com.hdec.data.domain.Datum;
import com.hdec.data.domain.report.PngInfo;
import com.hdec.data.domain.report.Series;
import com.hdec.data.feign.MonitorService;
import com.hdec.data.mapper.ChartMapper;
import com.hdec.data.qo.ProcessLineJsonQo;
import com.hdec.data.socket.WebSocketServer;
import com.hdec.data.vo.ProcessVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.websocket.Session;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ChartService {

    @Autowired
    private ChartMapper chartMapper;

    @Autowired
    private MonitorService monitorService;

    @Autowired
    private TdService tdService;

    /** 计数器Map */
    private static Map<Integer, Integer> countMap = new HashMap<>();

    private static Map<String, Thread> ts = new HashMap<>();

    @Autowired
    private WebSocketServer webSocketServer;

    @Lazy
    @Autowired
    private VarService varService;

    @Autowired
    private TdBaseService tdBaseService;

    /** 访问映射 */
    @Value("${file.visitMapping}")
    private String visitMapping;

    /** 文件本地路径 - Windows */
    @Value("${file.windowPath}")
    private String windowPath;

    /** 文件本地路径 - Linux */
    @Value("${file.linuxPath}")
    private String linuxPath;

    /**
     * 列表
     */
    public List<Chart> list(String fieldNum) {
        List<PointCommon> fieldPoints = monitorService.getPointsByFieldNum(fieldNum);
        Map<Integer, List<PointCommon>> seaPointMap = toSeaPointMap(fieldPoints);

        List<Chart> charts = chartMapper.list(fieldNum);
        for (Chart chart : charts) {
            String ids = chart.getIds();
            List<String> nos = new ArrayList<>();
            for (String s : ids.split(",")) {
                String[] split = s.split("-");
                int pointId = Integer.parseInt(split[0]);
                List<PointCommon> pointCommons = seaPointMap.get(pointId);
                if (!ObjectUtils.isEmpty(pointCommons)) {
                    nos.add(pointCommons.get(0).getNo() + "-" + split[1]);
                } else {
                    nos.add("未知测点-" + split[1]);
                }
            }
            if (!ObjectUtils.isEmpty(nos)) {
                chart.setIds(nos.stream().collect(Collectors.joining(",")));
            }
        }

        return charts;
    }

    private Map<Integer, List<PointCommon>> toSeaPointMap(List<PointCommon> fieldPoints) {
        if (ObjectUtils.isEmpty(fieldPoints)) {
            return Collections.emptyMap();
        }

        return fieldPoints.stream().collect(groupingBy(PointCommon::getId));
    }

    /**
     * 批量删除
     */
    public void delete(Integer[] ids) {
        chartMapper.delete(ids);
    }

    /**
     * 新增
     */
    public void add(Chart chart) {
        chart.setStatus("生成中(0%)");
        chartMapper.add(chart);
        new Thread(() -> {
            try {
                genChart(chart, null);
            } catch (Exception e) {
                log.error("生成图片发生了异常：", e);
                chartMapper.updateStatus(chart.getId(), "已完成", "未知错误，请联系管理员");
            }
            countMap.remove(chart.getId());
        }).start();
    }

    /**
     * 预览
     */
    public void preview(Chart chart, String userId) {
        chart.setId(-Integer.parseInt(userId));
        Thread t = new Thread(() -> {
            try {
                genChart(chart, userId);
            } catch (Exception e) {
                log.error("生成图片发生了异常：", e);
            } finally {
                /* 通知前端生成完毕 */
                Session session = WebSocketServer.webSocketMap.get(userId);
                if (session != null) {
                    List<ProcessVo> vos = Arrays.asList(new ProcessVo(chart.getId(), "", 3));
                    webSocketServer.sendMessage(session, JSON.toJSONString(vos));
                }
            }
        });
        t.start();
        ts.put(userId, t);
    }


    /**
     * 取消预览
     */
    public void cancelPreview(String userId) {
        Thread thread = ts.get(userId);
        thread.stop();
    }

    /**
     * 生成过程线
     */
    private void genChart(Chart chart, String userId) {
        double[] wH = parseWH(chart.getImgType());
        Set<String> pointDirectSet = parsePointDirects(chart.getIds());
        Map<String, String> pointDirectMap = parsePointDirects2Map(chart.getIds());

        /* 准备数据 */
        Map<Integer, PointCommon> fieldPointMap = getFieldPointMap(chart.getFieldNum());
        Map<String, AttrCommon> fieldAttrMap = getFieldAttrMap(chart.getFieldNum());
        Map<Integer, InstCommon> fieldInstMap = getFieldInstMap(chart.getFieldNum());

        String chartDir = chart.getFieldNum() + File.separator + chart.getId() + "-" + sanitizeFilename(chart.getItemName());
        int totalSize = chart.getChartAttrs().size() * pointDirectSet.size() * 3 / 2;
        if (chart.getIsSingleCoord() || chart.getChartAttrs().size() <= 1) {
            for (ChartAttr chartAttr : chart.getChartAttrs()) {
                List<PngInfo> chartInfos = new ArrayList<>();
                attrProcessLine(fieldPointMap, fieldAttrMap, chart, chartAttr, chartInfos, pointDirectSet, pointDirectMap, totalSize, fieldInstMap);
                sortByNo(chartInfos);
                sortByDirect(chartInfos);
                genChartByInfo(chartInfos, chart.getPointNum(), chart.getId(),
                        TimeUtil.parse2Day(chart.getStartTime()).getTime(), TimeUtil.parse2Second(TimeUtil.completeEnd(chart.getEndTime())).getTime(),
                        wH, chartDir, chart.getIsMark(), chart.getInterval(), chart.getLimitLow(), chart.getLimitHigh(), chartAttr, totalSize, userId);
            }
        } else {
            List<Integer> pointIds = new ArrayList<>();
            List<String> directIds = new ArrayList<>();
            for (String pointDirect : pointDirectSet) {
                String[] pointDirectArr = pointDirect.split("-");
                int pointId = Integer.parseInt(pointDirectArr[0]);
                pointIds.add(pointId);
                directIds.add(pointDirectArr[1]);
            }
            List<PointCommon> fieldPoints = monitorService.getPointsByFieldNum(chart.getFieldNum());
            List<PointCommon> points = varService.getPoints(fieldPoints, pointIds);

            ChartAttr chartAttr1 = chart.getChartAttrs().get(0);
            ChartAttr chartAttr2 = chart.getChartAttrs().get(1);
            ProcessLineJsonQo line = new ProcessLineJsonQo();
            line.setLeftAttrAlias(chartAttr1.getAttrAliasName());
            line.setRightAttrAlias(chartAttr2.getAttrAliasName());

            List<PngInfo> chartInfos = getDoubleChartInfos(points, directIds, line, chartAttr1.getAttrName(), chartAttr2.getAttrName(), chart.getStartTime(), chart.getEndTime(), getFieldInstMap(chart.getFieldNum()), fieldAttrMap);

            sortByNo(chartInfos);
            sortByDirect(chartInfos);

            line.setPointNum(chart.getPointNum());
            line.setLeftIsAuto(chartAttr1.getIsAuto());
            line.setLeftMax(chartAttr1.getMax());
            line.setLeftMin(chartAttr1.getMin());

            line.setRightIsAuto(chartAttr2.getIsAuto());
            line.setRightMax(chartAttr2.getMax());
            line.setRightMin(chartAttr2.getMin());

            varService.genChartByInfo(line, chartInfos, TimeUtil.parse2Day(chart.getStartTime()).getTime(), TimeUtil.parse2Second(TimeUtil.completeEnd(chart.getEndTime())).getTime(),
                    chartDir, line.getIsMark());
        }

        try {
            FileCompressUtil.zipDir("/data/highcharts-export/" + chartDir, linuxPath + "/export", ".7z");
        } catch (Exception e) {
            e.printStackTrace();
            chartMapper.updateStatus(chart.getId(), "已完成", null);
            return;
        }
        chartMapper.updateStatus(chart.getId(), "已完成", visitMapping + "/export/" + chart.getId() + "-" + sanitizeFilename(chart.getItemName()) + ".7z");
    }

    public List<PngInfo> getDoubleChartInfos(List<PointCommon> points, List<String> directIds, ProcessLineJsonQo line, String leftName, String rightName,
                                             String startTime, String endTime, Map<Integer, InstCommon> fieldInstMap, Map<String, AttrCommon> fieldAttrMap) {

        List<PngInfo> chartInfos = new ArrayList<>();
        for (PointCommon point : points) {
            AttrCommon leftAttr = fieldAttrMap.get(point.getInstId() + "-" + leftName);
            AttrCommon rightAttr = fieldAttrMap.get(point.getInstId() + "-" + rightName);

            for (String directId : directIds) {
                if ("0".equals(directId) && !leftAttr.getIsPolar() && !rightAttr.getIsPolar()) {
                    continue;
                }
                if (!"0".equals(directId) && leftAttr.getIsPolar() && rightAttr.getIsPolar()) {
                    continue;
                }

                List<Datum> leftDatumList = null;
                List<Datum> rightDatumList = null;
                String leftAttrName = null;
                String rightAttrName = null;
                String leftAttrNameWithoutUnit = null;
                String rightAttrNameWithoutUnit = null;
                if (leftAttr != null) {
                    leftDatumList = varService.selectPointData(leftAttr.getRate(), point.getInstId(), startTime.substring(0, 10), endTime.substring(0, 10), leftAttr.getId(), point.getId(), Integer.parseInt(directId), 1);
                    leftAttrName = ObjectUtils.isEmpty(line.getLeftAttrAlias()) ? leftAttr.getName() : line.getLeftAttrAlias();
                    leftAttrNameWithoutUnit = leftAttrName;
                    if (!ObjectUtils.isEmpty(leftAttr.getUnit())) {
                        leftAttrName += "(" + leftAttr.getUnit() + ")";
                    }
                }
                if (rightAttr != null) {
                    rightDatumList = varService.selectPointData(leftAttr.getRate(), point.getInstId(), startTime.substring(0, 10), endTime.substring(0, 10), rightAttr.getId(), point.getId(), Integer.parseInt(directId), 1);
                    rightAttrName = ObjectUtils.isEmpty(line.getRightAttrAlias()) ? rightAttr.getName() : line.getRightAttrAlias();
                    rightAttrNameWithoutUnit = rightAttrName;
                    if (!ObjectUtils.isEmpty(rightAttr.getUnit())) {
                        rightAttrName += "(" + rightAttr.getUnit() + ")";
                    }
                }
                boolean isSingleDirect = fieldInstMap.get(point.getInstId()) == null ? false : "单向".equals(fieldInstMap.get(point.getInstId()).getDirect());
                PngInfo pngInfo = new PngInfo(point.getId(), point.getNo(), directId, leftDatumList, rightDatumList, leftAttrName, rightAttrName, isSingleDirect, leftAttrNameWithoutUnit, rightAttrNameWithoutUnit,
                        leftAttrName, rightAttrName);
                chartInfos.add(pngInfo);
            }
        }
        return chartInfos;
    }

    private Map<Integer, InstCommon> getFieldInstMap(String fieldNum) {
        List<InstCommon> insts = monitorService.allInstByField(fieldNum);
        if (ObjectUtils.isEmpty(insts)) {
            return Collections.emptyMap();
        }

        return insts.stream().collect(Collectors.toMap(InstCommon::getId, Function.identity(), (key1, key2) -> key2));
    }

    private void sortByDirect(List<PngInfo> chartInfos) {
        if (ObjectUtils.isEmpty(chartInfos)) {
            return;
        }

        Collections.sort(chartInfos, (p1, p2) -> {
            if (p1.getPointNo() == null || p1.getDirect() == null) {
                return 0;
            }
            if (p1.getPointNo().equals(p2.getPointNo())) {
                return p1.getDirect().compareTo(p2.getDirect());
            }
            return 0;
        });
    }

    /**
     * 按测点编号排序
     */
    public static void sortByNo(List<PngInfo> chartInfos) {
        if (ObjectUtils.isEmpty(chartInfos)) {
            return;
        }

        Collections.sort(chartInfos, (p1, p2) -> {
            if (p1.getPointNo() == null || p2.getPointNo() == null) {
                return 0;
            }

            String[] arr1 = p1.getPointNo().split("-");
            String[] arr2 = p2.getPointNo().split("-");
            if (arr1.length != 2 && arr2.length != 2) {
                return arr1[0].compareTo(arr2[0]);
            } else if (arr1.length == 2 && arr2.length != 2) {
                return -1;
            } else if (arr1.length != 2 && arr2.length == 2) {
                return 1;
            }

            String letter1 = extractLetter(arr1[0]);
            String letter2 = extractLetter(arr2[0]);
            if (letter1 == null && letter2 == null) {
                return 0;
            } else if (letter1 != null && letter2 == null) {
                return -1;
            } else if (letter1 == null && letter2 != null) {
                return 1;
            } else {
                int res = letter1.compareTo(letter2);
                if (res == 0) {
                    for (int i = 0; i < 2; i++) {
                        int num1 = extractNum(arr1[i]);
                        int num2 = extractNum(arr2[i]);
                        if (num1 == num2) {
                            continue;
                        } else {
                            return num1 - num2;
                        }
                    }
                } else {
                    return res;
                }
            }
            return 0;
        });
    }

    /**
     * 提取字母
     */
    private static String extractLetter(String s) {
        if (ObjectUtils.isEmpty(s)) {
            return null;
        }

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i <s.length(); i++) {
            char c = s.charAt(i);
            if (Character.isLetter(c)) {
                sb.append(c);
            }
        }
        if (sb.length() == 0) {
            return null;
        }
        return sb.toString();
    }

    /**
     * 从字符串的最后一位往前提取数字
     */
    private static Integer extractNum(String s) {
        if (ObjectUtils.isEmpty(s)) {
            return Integer.MAX_VALUE;
        }

        StringBuilder sb = new StringBuilder();
        for (int i = s.length() - 1; i >= 0; i--) {
            char c = s.charAt(i);
            if (Character.isDigit(c)) {
                sb.append(c);
            } else {
                continue;
            }
        }
        if (sb.length() == 0) {
            return Integer.MAX_VALUE;
        }
        return Integer.parseInt(sb.reverse().toString());
    }

    /**
     * 更新进度
     */
    private void updateProcess(int chartId, int totalSize) {
        Integer curCount = countMap.get(chartId);
        if (curCount == null) {
            curCount = 0;
        }
        double process = ++curCount * 1.0 / totalSize * 100;
        if (process > 99) {
            process = 99;
        }
        chartMapper.updateStatus(chartId, "生成中(" + String.format("%.1f", process) + "%)", null);
        countMap.put(chartId, curCount);
    }

    /**
     * 根据已有信息生成图片
     */
    private void genChartByInfo(List<PngInfo> chartInfos, Integer pointNum, Integer chartId, long sTime, long eTime,
                                double[] wH, String chartDir, Boolean isMark, Integer interval, Double limitLow, Double limitHigh, ChartAttr chartAttr, int totalSize, String userId) {
        LinkedHashMap<Integer, List<PngInfo>> pointChartInfoMap = chartInfos.stream().collect(groupingBy(PngInfo::getPointId, LinkedHashMap::new, Collectors.toList()));
        int count = 0;
        List<Series> series = new ArrayList<>();
        for (Map.Entry<Integer, List<PngInfo>> entry : pointChartInfoMap.entrySet()) {
            List<PngInfo> infos = entry.getValue();
            for (PngInfo info : infos) {
                String name;
                if (info.getIsSingleDirect() != null && info.getIsSingleDirect()) {
                    name = info.getPointNo();
                } else {
                    String d = info.getDirect();
                    name = ObjectUtils.isEmpty(d) ? info.getPointNo() : info.getPointNo() + "-" + d;
                }
                series.add(new Series(name, info.getMonitorItem(), info.getChartAttrName(), info.getFileAttrName(), info.getData(), info.getType()));
            }
            count++;

            if (count == pointNum) {
                if (series.size() == 0) {
                    continue;
                }
                String pngPath = pngData(series, sTime, eTime, wH, chartDir, isMark, interval, limitLow, limitHigh, chartAttr);
                if (chartId < 0 && !ObjectUtils.isEmpty(pngPath)) {
                    Session session = WebSocketServer.webSocketMap.get(String.valueOf(userId));
                    if (session != null) {
                        List<ProcessVo> vos = Arrays.asList(new ProcessVo(chartId, getPngBase64(pngPath), 2));
                        webSocketServer.sendMessage(session, JSON.toJSONString(vos));
                    }
                }

                updateProcess(chartId, totalSize);
                series.clear();
                count = 0;
            }
        }
        if (series.size() > 0) {
            String pngPath = pngData(series, sTime, eTime, wH, chartDir, isMark, interval, limitLow, limitHigh, chartAttr);
            if (chartId < 0 && !ObjectUtils.isEmpty(pngPath)) {
                Session session = WebSocketServer.webSocketMap.get(String.valueOf(userId));
                if (session != null) {
                    List<ProcessVo> vos = Arrays.asList(new ProcessVo(chartId, getPngBase64(pngPath), 2));
                    webSocketServer.sendMessage(session, JSON.toJSONString(vos));
                }
            }
        }
    }

    private String getPngBase64(String pngPath) {
        try {
            Path path = new File(pngPath).toPath();
            byte[] imageBytes = Files.readAllBytes(path);

            return Base64.getEncoder().encodeToString(imageBytes);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private void attrProcessLine(Map<Integer, PointCommon> fieldPointMap, Map<String, AttrCommon> fieldAttrMap, Chart chart,
                                 ChartAttr chartAttr, List<PngInfo> chartInfos, Set<String> pointDirectSet, Map<String, String> pointDirectMap, int totalSize, Map<Integer, InstCommon> fieldInstMap) {
        /* 遍历每个测点的每个方向 */
        for (String pointDirect : pointDirectSet) {
            updateProcess(chart.getId(), totalSize);
            CommonUtil.sleepRandomMillSeconds();

            String[] pointDirectArr = pointDirect.split("-");
            int pointId = Integer.parseInt(pointDirectArr[0]);
            int directId = Integer.parseInt(pointDirectArr[1]);

            PointCommon point = fieldPointMap.get(pointId);
            AttrCommon attr = fieldAttrMap.get(point.getInstId() + "-" + chartAttr.getAttrName());
            if (point == null || attr == null) {
                continue;
            }
            if (attr.getIsPolar() && directId != 0) {
                continue;
            }
            if (!attr.getIsPolar() && directId == 0) {
                continue;
            }

            List<Datum> datumList = selectPointData(point.getInstId(), chart.getStartTime(), chart.getEndTime(), attr.getId(), attr.getRate(), pointId, directId, chart.getNumPerHour());
            String chartAttrName = getChartAttrName(chartAttr.getAttrAliasName(), attr.getUnit());
            String fileAttrName = getFileAttrName(chartAttr.getAttrAliasName(), attr.getUnit());
            boolean isSingleDirect = fieldInstMap.get(point.getInstId()) == null ? false : "单向".equals(fieldInstMap.get(point.getInstId()).getDirect());
            chartInfos.add(new PngInfo(pointId, point.getNo(), MonitorUtil.transDirection(directId), datumList,
                    chart.getItemName(), chartAttrName, fileAttrName, pointDirectMap.get(pointDirect), isSingleDirect));
        }
    }

    private String getChartAttrName(String attrAliasName, String unit) {
        if (ObjectUtils.isEmpty(unit)) {
            return attrAliasName;
        }
        return attrAliasName + "(" + unit + ")";
    }

    private String getFileAttrName(String attrAliasName, String unit) {
        if (ObjectUtils.isEmpty(unit)) {
            return sanitizeFilename(attrAliasName);
        }
        return sanitizeFilename(attrAliasName + "(" + unit + ")");
    }

    public static String sanitizeFilename(String name) {
        name = name.replaceAll("[£]", "\u00A3");
        return name.replaceAll("[\\\\/:*?\"<>|]", "-");
    }

    /**
     * 查询测点数据
     */
    public List<Datum> selectPointData(Integer instId, String startTime, String endTime, Integer attrId, String attrRate, Integer pointId, Integer direct, Integer numPerHour) {
        String tabName = DataServiceUtil.buildTableName(instId, attrRate, pointId);
        String colName = tdService.enColName(attrId, direct);

        StringBuilder sql = new StringBuilder();
        if (numPerHour == null) {
            /* 所有数据 */
            sql.append("select ts, point, " + colName + " from " + tabName + " where ts between '" + startTime + " 00:00:00' and '" + endTime + " 23:59:59'");
            sql.append(" and point = " + pointId);
            sql.append(" and status_" + direct + " IN (0, 1)");
            sql.append(" and del = false");
        } else {
            /* 采样数据 */
            int interval = 3600 / numPerHour;
            sql.append("select first(ts) as ts, first(point) as point, first(" + colName + ") as " + colName + " from " + tabName + " where ts between '" + startTime + " 00:00:00' and '" + endTime + " 23:59:59'");
            sql.append(" and point = " + pointId);
            sql.append(" and status_" + direct + " IN (0, 1)");
            sql.append(" and del = false");
            sql.append(" INTERVAL(" + interval + "s)");
        }

        List<Map<String, Object>> records = tdBaseService.selectMulti(sql.toString(), attrRate);
        if (records.size() == 0) {
            return Collections.emptyList();
        }

        List<Datum> dataList = new ArrayList<>();
        for (Map<String, Object> record : records) {
            Date ts = (Date) record.get("ts");
            Integer point = (Integer) record.get("point");
            Float attrVal = (Float) record.get(colName);
            if (attrVal != null) {
                dataList.add(new Datum(point, direct, ts, attrId, attrVal));
            }
        }
        return dataList;
    }

    /**
     * 获取某风场下所有测点
     */
    private Map<Integer, PointCommon> getFieldPointMap(String fieldNum) {
        List<PointCommon> fieldPoints = monitorService.getPointsByFieldNum(fieldNum);
        if (ObjectUtils.isEmpty(fieldPoints)) {
            return Collections.emptyMap();
        }

        Map<Integer, PointCommon> map = new HashMap<>(fieldPoints.size());
        for (PointCommon point : fieldPoints) {
            map.put(point.getId(), point);
        }
        return map;
    }

    /**
     * 获取某风场下所有属性
     */
    private Map<String, AttrCommon> getFieldAttrMap(String fieldNum) {
        List<AttrCommon> fieldAttrs = monitorService.allAttrByField(fieldNum);
        if (ObjectUtils.isEmpty(fieldAttrs)) {
            return Collections.emptyMap();
        }

        Map<String, AttrCommon> map = new HashMap<>(fieldAttrs.size());
        for (AttrCommon attr : fieldAttrs) {
            map.put(attr.getInstId() + "-" + attr.getName(), attr);
        }
        return map;
    }

    /**
     * 解析测点ID和方向
     */
    private Set<String> parsePointDirects(String ids) {
        if (ObjectUtils.isEmpty(ids)) {
            return Collections.emptySet();
        }

        Set<String> pointDirectSet = new HashSet<>();
        for (String s : ids.split(",")) {
            String[] arr = s.split("-");
            pointDirectSet.add(arr[0] + "-" + arr[1]);
        }
        return pointDirectSet;
    }

    /**
     * 解析测点ID和方向
     */
    private Map<String, String> parsePointDirects2Map(String ids) {
        if (ObjectUtils.isEmpty(ids)) {
            return Collections.emptyMap();
        }

        Map<String, String> pointDirectMap = new HashMap<>();
        for (String s : ids.split(",")) {
            String[] arr = s.split("-");
            pointDirectMap.put(arr[0] + "-" + arr[1], arr[2]);
        }
        return pointDirectMap;
    }

    /**
     * 解析图片宽高
     */
    public double[] parseWH(String imgType) {
        int ratio = 38;
        double[] arr16 = {24.38 * ratio, 7.2 * ratio};
        double[] arr8 = {22.5 * ratio, 8.4 * ratio};
        if ("8cm".equals(imgType)) {
            return arr8;
        }
        return arr16;
    }

    /**
     * 生成图片
     */
    private String pngData(List<Series> series, long sTime, long eTime, double[] wH, String chartDir, Boolean isMark, Integer interval, Double limitLow, Double limitHigh, ChartAttr chartAttr) {
        if (ObjectUtils.isEmpty(series)) {
            return null;
        }
        String seaType = series.get(0).getSeaType();
        File dir = new File("/data/highcharts-export/" + chartDir + File.separator + seaType);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        String chartAttrName = series.get(0).getChartAttrName();
        String fileAttrName = series.get(0).getFileAttrName();
        String elementStr = serializeSeries(series, isMark);
        String pointNo = series.stream().map(Series::getName).collect(Collectors.joining(","));
        String json = null;
        try {
            json = CommonUtil.readResources("data/echarts-option.json");
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (json == null) {
            return null;
        }
        json = json.replace("customWidth", String.valueOf(wH[0]));
        json = json.replace("customHeight", String.valueOf(wH[1]));
        json = json.replace("millisStart", String.valueOf(sTime));
        json = json.replace("millisEnd", String.valueOf(eTime));
        json = json.replace("intervalParam", String.valueOf(interval));
        json = json.replace("limitLow", String.valueOf(limitLow));
        json = json.replace("limitHigh", String.valueOf(limitHigh));
        json = json.replace("分量名", chartAttrName);
        json = json.replace("element", elementStr);
        if (isMark != null && isMark) {
            json = json.replace("legendFlag", "false");
        } else {
            json = json.replace("legendFlag", "true");
        }
        json = setYAxis(json, chartAttr, series);
        String outPngPath = (dir.getAbsolutePath() + File.separator + fileAttrName + "-" + pointNo + ".png").replace(" ", "");
        json = json.replace("outputPath", outPngPath);

        if (ObjectUtils.isEmpty(limitLow) || ObjectUtils.isEmpty(limitHigh)) {
            JSONObject jsonObject = JSONObject.parseObject(json);
            jsonObject.remove("dotline");
            json = jsonObject.toJSONString();
        }


        String jsonPath = (dir.getAbsolutePath() + File.separator + fileAttrName + "-" + pointNo + ".json").replace(" ", "");
        try {
            FileUtil.writeFile(jsonPath, json);
        } catch (Exception e) {
            e.printStackTrace();
        }
        executeCMD("node /home/<USER>/ec-expoort/index.js --input " + jsonPath);

        /* 删除非png文件 */
        for (File file : dir.listFiles()) {
            if (!file.getName().endsWith(".png")) {
                try {
                    String tarDir = file.getAbsolutePath().substring(33, 38);
                    String targetPath = "/data/history-json/" + tarDir;
                    File tFile = new File(targetPath);
                    if (!tFile.exists()) {
                        tFile.mkdirs();
                    }
                    Files.move(Paths.get(file.getAbsolutePath()), Paths.get(tFile.getAbsolutePath() + File.separator + file.getName()));
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return outPngPath;
    }

    /**
     * 设置Y轴坐标
     */
    private String setYAxis(String jsonStr, ChartAttr chartAttr, List<Series> series) {
        String json = jsonStr;
        if (chartAttr.getIsAuto()) {
            /* 若勾选，看有没有填写上下限，填了就判断是否越限，越限就自动，没有越限就直接使用；没填就自动 */
            if (chartAttr.getMin() != null && chartAttr.getMax() != null) {
                if (isCrossBorder(series, chartAttr.getMin(), chartAttr.getMax())) {
                    // 越限
                    json = json.replace("isAuto", "true");
                    json = json.replace("yMin", "0");
                    json = json.replace("yMax", "0");
                    json = json.replace("yInterval", "0");
                } else {
                    // 没有越限
                    json = json.replace("isAuto", "false");
                    json = json.replace("yMin", String.valueOf(chartAttr.getMin()));
                    json = json.replace("yMax", String.valueOf(chartAttr.getMax()));
                    json = json.replace("yInterval", String.format("%.4f", (chartAttr.getMax() - chartAttr.getMin()) / 4));
                }
            } else {
                json = json.replace("isAuto", "true");
                json = json.replace("yMin", "0");
                json = json.replace("yMax", "0");
                json = json.replace("yInterval", "0");
            }
        } else {
            /* 若没有勾选，看有没有填写上下限，填了就直接使用；没填就自动 */
            if (chartAttr.getMin() != null && chartAttr.getMax() != null) {
                json = json.replace("isAuto", "false");
                json = json.replace("yMin", String.valueOf(chartAttr.getMin()));
                json = json.replace("yMax", String.valueOf(chartAttr.getMax()));
                json = json.replace("yInterval", String.format("%.4f", (chartAttr.getMax() - chartAttr.getMin()) / 4));
            } else {
                json = json.replace("isAuto", "true");
                json = json.replace("yMin", "0");
                json = json.replace("yMax", "0");
                json = json.replace("yInterval", "0");
            }
        }
        return json;
    }

    /**
     * 判断是否越限
     */
    private boolean isCrossBorder(List<Series> series, Double min, Double max) {
        if (ObjectUtils.isEmpty(series)) {
            return false;
        }

        for (Series s : series) {
            if (s.getData() == null) {
                continue;
            }
            for (Datum datum : s.getData()) {
                Float val = datum.getAttrVal();
                if (val == null) {
                    continue;
                }
                if (val < min || val > max) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 获取自定义纵轴
     */
    private String getCustomYAxisStr(ChartAttr chartAttr) {
        if (chartAttr.getMin() != null && chartAttr.getMax() != null) {
            String[] arr = toNSections(chartAttr.getMin(), chartAttr.getMax(), 4);
            return ",\"tickPositions\": [" + StringUtils.join(arr, ",") + "]";
        }
        return "";
    }

    private static String serializeSeries(List<Series> series, Boolean isMark) {
        String[] colors = {"#3070b7", "#c95c2e", "#00ff00", "#9900ff", "#ff00ff", "#0865f7"};
        String[] shapes = {"circle", "rect", "roundRect", "triangle", "diamond", "arrow"};
        if (ObjectUtils.isEmpty(series)) {
            return null;
        }

        StringBuilder sb = new StringBuilder();
        int colorIndex = 0;
        for (Series s : series) {
            sb.append("{");

            sb.append("\"type\":\"");
            sb.append(s.getType());
            sb.append("\",");

            sb.append("\"name\":\"");
            sb.append(s.getName());
            sb.append("\",");

            if (colorIndex >= colors.length) {
                colorIndex = 0;
            }

            if (isMark != null && isMark) {
                sb.append("\"symbol\":\"");
                sb.append(shapes[colorIndex]);
                sb.append("\",");
            } else {
                sb.append("\"symbol\":\"");
                sb.append("none");
                sb.append("\",");
            }

            sb.append("\"lineStyle\": {" +
                    "\"color\": \""+colors[colorIndex]+"\"," +
                    "\"width\": 1" +
                    "},");

            sb.append("\"data\":[");

            Map<Long, Float> markTimeMap = getMarkTimes(s.getData());
            boolean flag = false;
            for (Datum datum : s.getData()) {
                if (isMark != null && isMark) {
                    Float aFloat = markTimeMap.get(datum.getTime().getTime());
                    if (aFloat != null) {
                        sb.append("{\"value\": ["+datum.getTime().getTime()+", "+aFloat+"], \"symbol\": \""+shapes[colorIndex]+"\", \"symbolSize\": 6},");
                        continue;
                    }
                }
                if (s.getData().size() == 1) {
                    sb.append("{\"value\": [" + datum.getTime().getTime() + ", " + datum.getAttrVal() + "], \"symbol\": \"" + shapes[colorIndex] + "\"},");
                } else {
                    sb.append("{\"value\": ["+datum.getTime().getTime()+", "+datum.getAttrVal()+"], \"symbol\": \"none\"},");
                }
                flag = true;
            }
            if (flag) {
                sb.deleteCharAt(sb.length() - 1);
            }
            sb.append("]},");
            colorIndex++;
        }
        if (sb.charAt(sb.length() - 1) == ',') {
            sb.deleteCharAt(sb.length() - 1);
        }
        return sb.toString();
    }

    private static Map<Long, Float> getMarkTimes(List<Datum> data) {
        if (ObjectUtils.isEmpty(data)) {
            return Collections.emptyMap();
        }

        Map<Long, Float> map = new HashMap<>();
        Long lastTime = data.get(0).getTime().getTime();
        for (Datum datum : data) {
            long time = datum.getTime().getTime();
            Float val = datum.getAttrVal();
            if (time - lastTime > 23 * 3600 * 1000) {
                map.put(time, val);
                lastTime = time;
            }
        }
        return map;
    }

    /**
     * 将图片转换成Base64编码
     */
    public static String getImgBase64(String imgFile) {
        // 将图片文件转化为二进制流
        InputStream in;
        byte[] data = null;
        // 读取图片字节数组
        try {
            in = new FileInputStream(imgFile);
            data = new byte[in.available()];
            in.read(data);
            in.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Base64.getEncoder().encodeToString(data);
    }

    public static void executeCMD(String cmd) {
        System.out.println(cmd);
        Process process = null;
        int exitVal = 0;
        try {
            process = Runtime.getRuntime().exec(cmd);
            exitVal = process.waitFor();
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }

        if (exitVal != 0) {
            throw new RuntimeException("cmd任务执行失败");
        }
    }


    /**
     * 将两个数分成N段
     */
    private static String[] toNSections(double min, double max, int N) {
        if (min >= max) {
            return null;
        }

        double dept = (max - min) / N;
        String[] arr = new String[N + 1];
        for (int i = 0; i < arr.length; i++) {
            double val = new BigDecimal(min).setScale(3, BigDecimal.ROUND_HALF_UP).doubleValue();
            if (isInt(val)) {
                arr[i] = String.valueOf(Math.round(val));
            } else {
                arr[i] = String.valueOf(val);
            }
            min += dept;
        }
        return arr;
    }

    /**
     * 判断一个数字是否是整数
     */
    public static boolean isInt(double num) {
        return Math.abs(num - Math.round(num)) < Double.MIN_VALUE;
    }


}
