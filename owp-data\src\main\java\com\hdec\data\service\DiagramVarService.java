package com.hdec.data.service;

import com.alibaba.fastjson.JSONObject;
import com.hdec.common.domain.AttrCommon;
import com.hdec.common.domain.PointCommon;
import com.hdec.common.util.TimeUtil;
import com.hdec.data.domain.DiagramVar;
import com.hdec.data.domain.StatVar;
import com.hdec.data.domain.diagram.Option;
import com.hdec.data.domain.diagram.XParam;
import com.hdec.data.domain.diagram.YParam;
import com.hdec.data.feign.MonitorService;
import com.hdec.data.qo.ParseVarQo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 过程线变量业务逻辑类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class DiagramVarService extends VarServiceBase {

    @Autowired
    private TdService tdService;

    @Autowired
    private MonitorService monitorService;

    @Autowired
    private DiagramVarParseService diagramVarParse;


    private void drawLine(String pointDirect, List<Map<String, Object>> records, Date date) {
        JSONObject jsonObj = new JSONObject();
        // 每个元素一条线
        List<Integer> xValues1 = new ArrayList<>(records.size());
        List<String> xValues2 = new ArrayList<>(records.size());
        List<Float> yValues = new ArrayList<>(records.size());
        System.out.println("records.size():" + records.size());
        for (Map<String, Object> record : records) {
            Calendar calendar = Calendar.getInstance();
//            calendar.setTime(ts);

            int day = calendar.get(Calendar.DAY_OF_MONTH);
            int month = calendar.get(Calendar.MONTH) + 1;
            int year = calendar.get(Calendar.YEAR);

            String monthStr = year + "-" + month;
            xValues1.add(day);

            // month要考虑居中
            if (xValues2.contains(monthStr)) {
                xValues2.add("");
            } else {
                xValues2.add(monthStr);
            }

            if (record.get("attr_val") != null) {
                yValues.add(Float.parseFloat(String.valueOf(record.get("attr_val"))));
            } else {
                yValues.add(null);
            }
//            ts = addHour(ts, 4);
        }
        YParam yParam = new YParam();
        yParam.setName("xx");
        yParam.setData(yValues);
        yParam.setYAxisIndex(0);

        XParam xParam = new XParam();
        xParam.setSplit_index(0);
        xParam.setFirst_line(xValues1);
        xParam.setLast_line(xValues2);

        Option option = new Option();

        jsonObj.put("y_data", new YParam[] {yParam});
        jsonObj.put("x_data", xParam);
        jsonObj.put("option", option);

        String jsonString = jsonObj.toJSONString();
        System.out.println("jsonString:"+jsonString);
    }

    /**
     * 处理风机
     */
    private Object handleMultiVar(StatVar statVar, List<PointCommon> fieldPoints, List<AttrCommon> fieldAttrs, String startTime, String endTime) {
        /* 查询涉及到的所有测点并按仪器分组 */
        List<PointCommon> points = getRelatedPointIds(statVar, fieldPoints);
        Map<Integer, List<PointCommon>> instPoints = points.stream().collect(Collectors.groupingBy(PointCommon::getInstId));

        AtomicReference<String> unit = null;

        List<Map<String, Object>> totalLines = new ArrayList<>();
        instPoints.forEach((instId, ps) -> {
            // 获取分量ID
            AttrCommon attr = findAttrIdByName(fieldAttrs, statVar.getAttrName(), instId);
            if (attr == null) {
                return;
            }
            unit.set(attr.getUnit());

//            List<Map<String, Object>> lines = tdService.getTogetherData(instId, ps, attr.getId(), statVar.getDirect(), startTime, endTime, statVar.getStatItem());
//            totalLines.addAll(lines);
        });
        if (totalLines.size() == 0) {
            return " - ";
        }

        if ("最小值".equals(statVar.getStatItem())) {
            return totalLines.get(0).get("min") + getUnit(unit);
        } else if ("最大值".equals(statVar.getStatItem())) {
            return totalLines.get(0).get("max");
        } else if ("平均值".equals(statVar.getStatItem())) {
            return totalLines.get(0).get("avg");
        } else if ("最小值~最大值".equals(statVar.getStatItem())) {
            Object min = totalLines.get(0).get("min");
            Object max = totalLines.get(0).get("max");
            if (min != null && max != null) {
                return min + "~" + max;
            }
        } else if (statVar.getStatItem().contains("最小值的")) {
            Float min = totalLines.stream().map(x -> Float.parseFloat(String.valueOf(x.get("min")))).min(Comparator.comparing(x -> x)).orElse(null);
            Float max = totalLines.stream().map(x -> Float.parseFloat(String.valueOf(x.get("min")))).max(Comparator.comparing(x -> x)).orElse(null);
            if (min != null && max != null) {
                return min + "~" + max;
            }
        } else if (statVar.getStatItem().contains("最大值的")) {
            Float min = totalLines.stream().map(x -> Float.parseFloat(String.valueOf(x.get("max")))).min(Comparator.comparing(x -> x)).orElse(null);
            Float max = totalLines.stream().map(x -> Float.parseFloat(String.valueOf(x.get("max")))).max(Comparator.comparing(x -> x)).orElse(null);
            if (min != null && max != null) {
                return min + "~" + max;
            }
        } else if (statVar.getStatItem().contains("平均值的")) {
            Float min = totalLines.stream().map(x -> Float.parseFloat(String.valueOf(x.get("avg")))).min(Comparator.comparing(x -> x)).orElse(null);
            Float max = totalLines.stream().map(x -> Float.parseFloat(String.valueOf(x.get("avg")))).max(Comparator.comparing(x -> x)).orElse(null);
            if (min != null && max != null) {
                return min + "~" + max;
            }
        }
        return " - ";
    }


    private String getUnit(AtomicReference<String> unit) {
        if (unit != null) {
            return unit.get();
        }
        return "";
    }

    /**
     * 由分量名查找分量ID
     */
    private AttrCommon findAttrIdByName(List<AttrCommon> fieldAttrs, String attrName, Integer instId) {
        for (AttrCommon attr : fieldAttrs) {
            if (instId.equals(attr.getInstId()) && attrName.equals(attr.getName())) {
                return attr;
            }
        }
        return null;
    }
//
//    /**
//     * 查询涉及到的所有测点
//     */
//    private List<PointCommon> getRelatedPointIds(StatVar statVar, List<PointCommon> fieldPoints) {
//        List<String> seaFacilityIds = statVar.getSeaFacilityIds();
//        return fieldPoints.stream().filter(e -> seaFacilityIds.contains(e.getSeaFacilityId())).collect(Collectors.toList());
//    }
//
//    /**
//     * 查询涉及到的所有测点
//     */
//    private List<PointCommon> getRelatedPointIds(DiagramVar statVar, List<PointCommon> fieldPoints) {
//        List<String> seaFacilityIds = statVar.getSeaFacilityIds();
//        return fieldPoints.stream().filter(e -> seaFacilityIds.contains(e.getSeaFacilityId())).collect(Collectors.toList());
//    }


    /**
     * 将日期加上固定N小时
     */
    private static Date addHour(Date date, int hour) {
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(date);
        calendar.add(Calendar.HOUR, hour);
        return calendar.getTime();
    }



}
