//package com.hdec.data.service;
//
//import com.hdec.common.domain.*;
//import com.hdec.common.util.CommonUtil;
//import com.hdec.common.util.FileUtil;
//import com.hdec.common.vo.InstDirectAttr;
//import com.hdec.common.vo.ProcessLineVo;
//import com.hdec.data.domain.*;
//import com.hdec.data.domain.domin_word.*;
//import com.hdec.data.domain.report.*;
//import com.hdec.data.feign.MonitorService;
//import com.hdec.data.feign.WindService;
//import com.hdec.data.mapper.ChartMapper;
//import com.hdec.data.qo.DataQueryMinQo;
//import com.hdec.data.qo.DataQueryQo;
//import com.hdec.data.qo.VerifyQo;
//import com.hdec.data.util.ExcelUtil;
//import com.hdec.data.util.JsoupUtil;
//import com.hdec.data.vo.*;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.joda.time.DateTime;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.data.redis.core.RedisTemplate;
//import org.springframework.scheduling.annotation.EnableAsync;
//import org.springframework.stereotype.Service;
//import org.springframework.util.ObjectUtils;
//
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//import java.io.File;
//import java.io.FileInputStream;
//import java.io.IOException;
//import java.io.InputStream;
//import java.math.BigDecimal;
//import java.math.RoundingMode;
//import java.text.ParseException;
//import java.text.SimpleDateFormat;
//import java.util.*;
//import java.util.concurrent.TimeUnit;
//import java.util.regex.Matcher;
//import java.util.regex.Pattern;
//import java.util.stream.Collectors;
//
//import static java.util.stream.Collectors.groupingBy;
//import static java.util.stream.Collectors.reducing;
//
////import com.hdec.data.util.WordToPdfAsposeUtil;
//
///**
// * 数据查询
// *
// * <AUTHOR>
// */
//@Slf4j
//@Service
//@EnableAsync
//public class DataService222 {
//
//    @Autowired
//    private ChartMapper chartMapper;
//
//    @Autowired
//    private TdService tdService;
//
//    @Autowired
//    private WindService windService;
//
//    @Autowired
//    private MonitorService monitorService;
//
//    @Autowired
//    private RedisTemplate<String, Object> redisTemplate;
//
//    @Autowired
//    private ReportBaseInfoService infoService;
//
//    @Autowired
//    private ReportManageService reportService;
//
//    @Autowired
//    private FormulaService formulaService;
//
//    @Value("${templateFile}")
//    private String templateFile;
//
//    @Value("${exportFile}")
//    private String exportFile;
//
//    /**
//     * 数据查询(表)
//     */
//    public R queryTable(DataQueryQo qo) {
//        InstDirectAttr instDirectAttr = monitorService.getInstDirectAttr(qo.getPointIds()[0]);
//        if (ObjectUtils.isEmpty(instDirectAttr.getAttrs())) {
//            return R.error("选中测点所绑定仪器下没有中间量和成果量");
//        }
//
//        DataTableVo vo = tdService.selectData(instDirectAttr.getInstId(), qo.getStartTime(), qo.getEndTime(), qo.getPointIds(), qo.getPointNos(),
//                qo.getPageNum(), qo.getPageSize(), qo.getOrderBy(), qo.getOrderRule(), qo.getApproval(), instDirectAttr.getAttrs(), true);
//        vo.setSingleDirect(instDirectAttr.getDirects() == null ? false : instDirectAttr.getDirects().size() == 1);
//        return R.success(vo);
//    }
//
//    public R queryMinTable(DataQueryMinQo qo) {
//        InstDirectAttr instDirectAttr = monitorService.getInstDirectAttr(qo.getPointId());
//        if (ObjectUtils.isEmpty(instDirectAttr.getAttrs())) {
//            return R.error("选中测点所绑定仪器下没有中间量和成果量");
//        }
//
//        DataTableVo vo = tdService.selectMinData(instDirectAttr.getInstId(), qo.getStartTime(), qo.getEndTime(), qo.getPointId(), qo.getPointNo(),
//                qo.getPageNum(), qo.getPageSize(), qo.getApproval(), qo.getAttrId(), qo.getAttrName(), qo.getDirect(), qo.getLt(), qo.getConnector(), qo.getGt(), qo.getMin(), qo.getMax());
//        return R.success(vo);
//    }
//
//    /**
//     * 导出Excel
//     */
//    public void exportTable(String fieldNum, DataQueryQo qo, HttpServletResponse response) throws Exception {
//        InstDirectAttr instDirectAttr = monitorService.getInstDirectAttr(qo.getPointIds()[0]);
//        DataTableVo vo = tdService.selectData(instDirectAttr.getInstId(), qo.getStartTime(), qo.getEndTime(), qo.getPointIds(), qo.getPointNos(),
//                qo.getPageNum(), qo.getPageSize(), qo.getOrderBy(), qo.getOrderRule(), qo.getApproval(), instDirectAttr.getAttrs(), false);
//
//        List<Map<String, Object>> list = new ArrayList<>();
//        TableHead[] heads = vo.getHead();
//        String[] keys = new String[heads.length];
//        String[] titles = new String[heads.length];
//        for (int i = 0; i < heads.length; i++) {
//            keys[i] = heads[i].getName();
//            titles[i] = heads[i].getShowName();
//        }
//
//        List<List<CellVal>> body = vo.getBody();
//
//        if (!ObjectUtils.isEmpty(body)) {
//            for (List<CellVal> cells : body) {
//                Map<String, Object> oneData = new HashMap<>();
//                for (int i = 0; i < cells.size() - 1; i++) {
//                    oneData.put(keys[i], cells.get(i).getVal());
//                }
//                list.add(oneData);
//            }
//        }
//        ExcelUtil.createNBig("数据.xlsx", titles, keys, list, response);
//    }
//
//    /**
//     * 告警过程线
//     */
//    public List<Datum> alarmProcessLine(ProcessLineVo vo) throws ParseException {
//        InstDirectAttr info = monitorService.getInstDirectAttr(vo.getPointId());
//        if (ObjectUtils.isEmpty(info.getAttrs())) {
//            return Collections.emptyList();
//        }
//
//        String attrName = null;
//        List<AttrCommon> attrs = info.getAttrs();
//        if (!ObjectUtils.isEmpty(attrs)) {
//            for (AttrCommon attr : attrs) {
//                if (vo.getAttrId().equals(attr.getId())) {
//                    attrName = attr.getName();
//                }
//            }
//        }
//
//        if (!ObjectUtils.isEmpty(vo.getTime())) {
//            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//            DateTime dateTime = new DateTime(format.parse(vo.getTime()));
//            DateTime sTime = dateTime.minusMonths(3);
//            DateTime eTime = dateTime.plusMonths(3);
//            return tdService.alarmProcessLine(info.getInstId(), vo.getPointId(), vo.getDirection(), vo.getAttrId(), attrName,
//                    vo.getTime(), sTime.toString("yyyy-MM-dd HH:mm:ss"), eTime.toString("yyyy-MM-dd HH:mm:ss"));
//        } else {
//            return tdService.alarmProcessLine(info.getInstId(), vo.getPointId(), vo.getDirection(), vo.getAttrId(), attrName,
//                    null, vo.getStartTime(), vo.getEndTime());
//        }
//    }
//
//    /**
//     * 审核数据
//     */
//    public R verify(VerifyQo qo) {
//        InstDirectAttr instDirectAttr = monitorService.getInstDirectAttr(qo.getPointId());
//        if (ObjectUtils.isEmpty(instDirectAttr.getAttrs())) {
//            return R.error("系统错误");
//        }
//
//        tdService.verify(instDirectAttr.getInstId(), qo);
//        return R.success("审核成功");
//    }
//
//    /**
//     * 批量审核数据
//     */
//    public R verifyBatch(VerifyQo[] qo) {
//        if (ObjectUtils.isEmpty(qo)) {
//            return R.success("审核成功");
//        }
//
//        InstDirectAttr instDirectAttr = monitorService.getInstDirectAttr(qo[0].getPointId());
//        if (ObjectUtils.isEmpty(instDirectAttr.getAttrs())) {
//            return R.error("系统错误");
//        }
//
//        for (VerifyQo verifyQo : qo) {
//            tdService.verify(instDirectAttr.getInstId(), verifyQo);
//        }
//        return R.success("审核成功");
//    }
//
//    /**
//     * 删除数据
//     */
//    public R del(String fieldNum, VerifyQo qo, HttpServletRequest request) {
//        InstDirectAttr instDirectAttr = monitorService.getInstDirectAttr(qo.getPointId());
//        if (ObjectUtils.isEmpty(instDirectAttr.getAttrs())) {
//            return R.error("系统错误");
//        }
//
//        /* 获取用户信息 */
//        String sessionId = request.getHeader("sessionId");
//        ResourceCommon resource = (ResourceCommon) redisTemplate.opsForValue().get(sessionId);
//        if (resource == null) {
//            return R.error("系统错误");
//        }
//
//        tdService.del(fieldNum, instDirectAttr.getInstId(), qo, resource.getUserId(), resource.getUsername());
//        return R.success("删除成功");
//    }
//
//    /**
//     * 编辑数据
//     */
//    public R update(String fieldNum, VerifyQo qo, HttpServletRequest request) {
//        InstDirectAttr instDirectAttr = monitorService.getInstDirectAttr(qo.getPointId());
//        if (ObjectUtils.isEmpty(instDirectAttr.getAttrs())) {
//            return R.error("系统错误");
//        }
//
//        /* 获取用户信息 */
//        String sessionId = request.getHeader("sessionId");
//        ResourceCommon resource = (ResourceCommon) redisTemplate.opsForValue().get(sessionId);
//        if (resource == null) {
//            return R.error("系统错误");
//        }
//
//        tdService.update(fieldNum, instDirectAttr.getInstId(), qo, resource.getUserId(), resource.getUsername());
//        return R.success("编辑成功");
//    }
//
//    /**
//     * 还原数据
//     */
//    public R returnBack(String fieldNum, VerifyQo qo, HttpServletRequest request) {
//        InstDirectAttr instDirectAttr = monitorService.getInstDirectAttr(qo.getPointId());
//        if (ObjectUtils.isEmpty(instDirectAttr.getAttrs())) {
//            return R.error("系统错误");
//        }
//
//        /* 获取用户信息 */
//        String sessionId = request.getHeader("sessionId");
//        ResourceCommon resource = (ResourceCommon) redisTemplate.opsForValue().get(sessionId);
//        if (resource == null) {
//            return R.error("系统错误");
//        }
//
//        tdService.returnBack(instDirectAttr.getInstId(), qo, resource.getUserId(), resource.getUsername(), fieldNum);
//        return R.success("还原成功");
//    }
//
//    /**
//     * 数据查询(图)
//     */
//    public List<DataChartVo> queryChart(String fieldNum, Integer[] pointIds, String[] pointNos, Integer[] directs,
//                                         Integer[] attrIds, String[] attrNames, String startTime, String endTime, Integer[] approval,
//                                        Float min, Float max) throws Exception {
//        if (ObjectUtils.isEmpty(pointIds)) {
//            return null;
//        }
//        InstDirectAttr instDirectAttr = monitorService.getInstDirectAttr(pointIds[0]);
//        if (ObjectUtils.isEmpty(instDirectAttr.getAttrs())) {
//            return null;
//        }
//
//        List<DataChartVo> dataChartVos = tdService.queryChart(instDirectAttr.getDirects(), instDirectAttr.getInstId(), pointIds, pointNos,
//                directs, attrIds, attrNames, startTime, endTime, approval, min, max);
//        return dataChartVos;
//    }
//
//    /**
//     * 由测点ID获取测点绑定仪器的ID、方向、属性
//     */
//    public InstDirectAttr getInstDirectAttr(Integer pointId) {
//        return monitorService.getInstDirectAttr(pointId);
//    }
//
//    /**
//     * 获取测点最新成果量数据
//     */
//    public R pointLastAchieveData(Integer pointId) {
//        InstDirectAttr instInfo = monitorService.getInstDirectAttr(pointId);
//        List<AttrCommon> attrs = filterAchieve(instInfo.getAttrs());
//        if (ObjectUtils.isEmpty(attrs)) {
//            return R.error("选中测点所绑定仪器下没有成果量");
//        }
//
//        List<LastDataVo> lastDataVos = null;
//        try {
//            lastDataVos = tdService.pointLastAchieveData(instInfo.getInstId(), instInfo.getDirects(), attrs, pointId);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return R.success(lastDataVos);
//    }
//
//    /**
//     * 过滤成果量
//     */
//    private List<AttrCommon> filterAchieve(List<AttrCommon> attrs) {
//        if (ObjectUtils.isEmpty(attrs)) {
//            return Collections.emptyList();
//        }
//        return attrs.stream().filter(e -> "成果量".equals(e.getType())).collect(Collectors.toList());
//    }
//
//    /**
//     * 创建超级表
//     */
//    public void createSTableIfNotExist(Integer instId, List<Integer> attrIds) {
//        tdService.createSTableIfNotExist11111(instId, attrIds);
//    }
//
////    /**
////     * 生成某风场某月的报告
////     */
////    @Async
////    public void genReport(Integer reportId, ReportStatQo qo) throws Exception {
////        int picIndex = 1001;
////
////        ReportInfo reportInfo = new ReportInfo();
////
////        /* 设置报告名称 */
////        try {
////            configReportName(reportInfo, qo.getFieldNum());
////        } catch (Exception e) {
////            e.printStackTrace();
////        }
////
////        /* 设置报告时间 */
////        try {
////            configReportTime(reportInfo, qo.getStartTime(), qo.getEndTime());
////        } catch (Exception e) {
////            e.printStackTrace();
////        }
////
////        /* 设置报告编写时间 */
////        try {
////            configReportWriteTime(reportInfo, qo.getWriteTime());
////        } catch (Exception e) {
////            e.printStackTrace();
////        }
////
////        /* 设置报告年份和期数 */
////        try {
////            configReportPhase(reportInfo, qo.getIssueYear(), qo.getIssueNum(), qo.getStartTime());
////        } catch (Exception e) {
////            e.printStackTrace();
////        }
////
////        /* 设置报告编校审人员 */
////        try {
////            configReportPerson(reportInfo, qo.getFieldNum());
////        } catch (Exception e) {
////            e.printStackTrace();
////        }
////
////        /* 设置概览 */
////        try {
////            configOverviews(reportInfo, qo.getFieldNum());
////        } catch (Exception e) {
////            e.printStackTrace();
////        }
////
////        // 更新进度
////        updateProcess(reportId, 1, 5);
////
////        /* 获取该风场名称、所有仪器、测点、海上设施、属性 */
////        Map<String, List<StatItem>> tiltStatItemsMap;
////        Map<String, List<StatItem>> shockStatItemsMap;
////        Map<String, List<StatItem>> shockBoosterStatItemsMap;
////        Map<String, List<StatItem>> stressStatItemsMap;
////        Map<String, List<StatItem>> corrodeStatItemsMap;
////        Map<String, List<StatItem>> subsideStatItemsMap;
////        Map<String, List<ReportPic>> picsMap;
////        Map<Integer, PointCommon> pointMap;
////        List<SeaFacilityCommon> fieldSeaFacilities;
////        List<SeaStatItem> seaTiltStatItems;
////        List<SeaStatItem> seaShockStatItems;
////        List<StatItem> tiltStatItems;
////        List<StatItem> shockStatItems;
////        List<StatItem> stressStatItems;
////        List<ReportPic> pics = new ArrayList<>();
////
////        try {
////            List<InstCommon> fieldInsts = monitorService.allInstByField(qo.getFieldNum());
////            List<PointCommon> fieldPoints = monitorService.getPointsByFieldNum(qo.getFieldNum());
////            Map<String, List<PointCommon>> seaPointMap = toSeaPointMap(fieldPoints);
////            pointMap = toPointMap(fieldPoints);
////            fieldSeaFacilities = windService.allSeaFacility(qo.getFieldNum());
////            List<AttrCommon> fieldAttrs = monitorService.allAttrByField(qo.getFieldNum());
////
////            // 更新进度
////            updateProcess(reportId, 6, 10);
////
////            /* 获取倾斜仪器ID */
////            tiltStatItems = getTiltInfo(fieldInsts, seaPointMap, pointMap, fieldSeaFacilities, fieldAttrs,
////                    qo.getStartTime(), qo.getEndTime(), pics, "倾斜", "倾角", null);
////
////            // 更新进度
////            updateProcess(reportId, 11, 20);
////
////            shockStatItems = getTiltInfo(fieldInsts, seaPointMap, pointMap, fieldSeaFacilities, fieldAttrs,
////                    qo.getStartTime(), qo.getEndTime(), pics, "振动", "有效值", "双向");
////            // 更新进度
////            updateProcess(reportId, 21, 30);
////
////            List<StatItem> shockBoosterStatItems = getTiltInfo(fieldInsts, seaPointMap, pointMap, fieldSeaFacilities, fieldAttrs,
////                    qo.getStartTime(), qo.getEndTime(), pics, "振动", "有效值", "三向");
////            // 更新进度
////            updateProcess(reportId, 31, 40);
////
////            stressStatItems = getTiltInfo(fieldInsts, seaPointMap, pointMap, fieldSeaFacilities, fieldAttrs,
////                    qo.getStartTime(), qo.getEndTime(), pics, "钢板应力", "应力", null);
////            // 更新进度
////            updateProcess(reportId, 41, 50);
////
////            List<StatItem> corrodeStatItems = getTiltInfo(fieldInsts, seaPointMap, pointMap, fieldSeaFacilities, fieldAttrs,
////                    qo.getStartTime(), qo.getEndTime(), pics, "腐蚀", "电位", null);
////            // 更新进度
////            updateProcess(reportId, 51, 60);
////
////            List<StatItem> subsideStatItems = getTiltInfo(fieldInsts, seaPointMap, pointMap, fieldSeaFacilities, fieldAttrs,
////                    qo.getStartTime(), qo.getEndTime(), pics, "静力水准", "相对沉降", null);
////            // 更新进度
////            updateProcess(reportId, 61, 70);
////
////            seaTiltStatItems = getSeaTiltInfo(fieldInsts, seaPointMap, pointMap, fieldSeaFacilities, fieldAttrs,
////                    qo.getStartTime(), qo.getEndTime(), pics, "倾斜", "倾角", null);
////            // 更新进度
////            updateProcess(reportId, 71, 80);
////
////            seaShockStatItems = getSeaTiltInfo(fieldInsts, seaPointMap, pointMap, fieldSeaFacilities, fieldAttrs,
////                    qo.getStartTime(), qo.getEndTime(), pics, "振动", "有效值", "三向");
////            // 更新进度
////            updateProcess(reportId, 81, 90);
////
////            tiltStatItemsMap = tiltStatItems.stream().collect(groupingBy(StatItem::getSeaFacilityId));
////            shockStatItemsMap = shockStatItems.stream().collect(groupingBy(StatItem::getSeaFacilityId));
////            shockBoosterStatItemsMap = shockBoosterStatItems.stream().collect(groupingBy(StatItem::getSeaFacilityId));
////            stressStatItemsMap = stressStatItems.stream().collect(groupingBy(StatItem::getSeaFacilityId));
////            corrodeStatItemsMap = corrodeStatItems.stream().collect(groupingBy(StatItem::getSeaFacilityId));
////            subsideStatItemsMap = subsideStatItems.stream().collect(groupingBy(StatItem::getSeaFacilityId));
////            picsMap = pics.stream().collect(groupingBy(ReportPic::getSeaFacilityId));
////        } catch (Exception e) {
////            e.printStackTrace();
////            tiltStatItemsMap = new HashMap<>(0);
////            shockStatItemsMap = new HashMap<>(0);
////            shockBoosterStatItemsMap = new HashMap<>(0);
////            stressStatItemsMap = new HashMap<>(0);
////            corrodeStatItemsMap = new HashMap<>(0);
////            subsideStatItemsMap = new HashMap<>(0);
////            picsMap = new HashMap<>(0);
////            pointMap = new HashMap<>(0);
////            fieldSeaFacilities = new ArrayList<>(0);
////            seaTiltStatItems = new ArrayList<>(0);
////            seaShockStatItems = new ArrayList<>(0);
////            tiltStatItems = new ArrayList<>(0);
////            shockStatItems = new ArrayList<>(0);
////            stressStatItems = new ArrayList<>(0);
////        }
////
////        /* 升压站 */
////        ReportBoosterInfo reportBoosterInfo = null;
////        try {
////            reportBoosterInfo = getReportBoosterInfo(fieldSeaFacilities, pointMap, tiltStatItemsMap, shockBoosterStatItemsMap, stressStatItemsMap, corrodeStatItemsMap, subsideStatItemsMap, picsMap);
////        } catch (Exception e) {
////            e.printStackTrace();
////        }
////
////        /* 结论 */
////        ReportSummaryInfo typicalSummary = null;
////        ReportSummaryInfo atypicalSummary = null;
////        try {
////            typicalSummary = getReportTypicalSummaryInfo(fieldSeaFacilities, tiltStatItems, shockStatItems, stressStatItems, true);
////            atypicalSummary = getReportTypicalSummaryInfo(fieldSeaFacilities, tiltStatItems, shockStatItems, stressStatItems, false);
////        } catch (Exception e) {
////            e.printStackTrace();
////        }
////
////        /* 典型风机 */
////        List<ReportTypicalFanInfo> typicalFanInfos = new ArrayList<>();
////        List<ReportTypicalFanInfo> speFanInfos = new ArrayList<>();
////        try {
////            List<SeaFacilityCommon> typicalFans = fieldSeaFacilities.stream().filter(e -> e.getId().startsWith("1-")).filter(e -> e.getIsTypical() != null).filter(e -> e.getIsTypical()).collect(Collectors.toList());
////            for (SeaFacilityCommon typicalFan : typicalFans) {
////                ReportTypicalFanInfo typicalFanInfo = new ReportTypicalFanInfo(typicalFan.getName());
////                // 倾斜
////                fillTilt(typicalFan, typicalFanInfo, tiltStatItemsMap);
////                // 振动
////                fillShock(typicalFan, typicalFanInfo, shockStatItemsMap);
////                // 钢板应力
////                fillStress(typicalFan, typicalFanInfo, stressStatItemsMap, pointMap, picsMap);
////                // 腐蚀
////                fillCorrode(typicalFan, typicalFanInfo, corrodeStatItemsMap, pointMap, picsMap);
////                typicalFanInfo.setPicIndex(picIndex++);
////                typicalFanInfos.add(typicalFanInfo);
////            }
////            reportInfo.setTypicalFanInfos(typicalFanInfos);
////
////            /* 非典型风机 - 只限倾斜和振动 */
////            List<SeaFacilityCommon> speFans = fieldSeaFacilities.stream()
////                    .filter(e -> e.getId().startsWith("1-"))
////                    .filter(e -> e.getIsTypical() == null || !e.getIsTypical()).collect(Collectors.toList());
////
////            for (SeaFacilityCommon typicalFan : speFans) {
////                ReportTypicalFanInfo typicalFanInfo = new ReportTypicalFanInfo(typicalFan.getName());
////                // 倾斜
////                fillTilt(typicalFan, typicalFanInfo, tiltStatItemsMap);
////                // 振动
////                fillShock(typicalFan, typicalFanInfo, shockStatItemsMap);
////                speFanInfos.add(typicalFanInfo);
////            }
////        } catch (Exception e) {
////            e.printStackTrace();
////        }
////
////        List<ReportTypicalFanInfo> allList = new ArrayList<>();
////        try {
////            allList.addAll(typicalFanInfos);
////            allList.addAll(speFanInfos);
////
////            /* 排序 */
////            allList.sort((f1, f2) -> {
////                Integer no1 = CommonUtil.extractUniqueNum(f1.getNo());
////                Integer no2 = CommonUtil.extractUniqueNum(f2.getNo());
////                if (no1 == null && no2 == null) {
////                    return 0;
////                }
////                if (no1 == null) {
////                    return 1;
////                }
////                if (no2 == null) {
////                    return -1;
////                }
////                return no1 - no2;
////            });
////        } catch (Exception e) {
////            e.printStackTrace();
////        }
////
////        /* 非典型风机 */
////        ReportTypicalFanInfo nonTypicalFanInfo = new ReportTypicalFanInfo();
////        List<ReportNonTypicalFanInfo> nonTypicalFanInfos = new ArrayList<>();
////        try {
////            List<SeaFacilityCommon> nonTypicalFans = fieldSeaFacilities.stream().filter(e -> e.getId().startsWith("1-")).filter(e -> (e.getIsTypical() == null || !e.getIsTypical())).collect(Collectors.toList());
////            // 倾斜
////            fillNonTypicalTilt(nonTypicalFans, nonTypicalFanInfo, tiltStatItemsMap);
////            // 振动
////            fillNonTypicalShock(nonTypicalFans, nonTypicalFanInfo, shockStatItemsMap);
////            // 钢板应力
////            fillNonTypicalStress(nonTypicalFans, nonTypicalFanInfo, stressStatItemsMap, pointMap);
////            // 腐蚀
////            for (SeaFacilityCommon nonTypicalFan : nonTypicalFans) {
////                fillNonTypicalCorrode(nonTypicalFan, nonTypicalFanInfos, corrodeStatItemsMap, pointMap, picsMap);
////            }
////            reportInfo.setNonTypicalFanInfo(nonTypicalFanInfo);
////            reportInfo.setNonTypicalFanInfos(nonTypicalFanInfos);
////        } catch (Exception e) {
////            e.printStackTrace();
////        }
////
////        List<ReportPic> tiltPics = null;
////        List<ReportPic> seaTiltPics = null;
////        List<ReportPic> shockPics = null;
////        List<ReportPic> seaShockPics = null;
////        try {
////            tiltPics = pics.stream().filter(e -> "倾斜".equals(e.getType()) && e.getSeaFacilityId().startsWith("1-")).collect(Collectors.toList());
////            seaTiltPics = pics.stream().filter(e -> "倾斜".equals(e.getType()) && e.getSeaFacilityId().startsWith("2-")).collect(Collectors.toList());
////            shockPics = pics.stream().filter(e -> "振动".equals(e.getType()) && e.getSeaFacilityId().startsWith("1-")).collect(Collectors.toList());
////            seaShockPics = pics.stream().filter(e -> "振动".equals(e.getType()) && e.getSeaFacilityId().startsWith("2-")).collect(Collectors.toList());
////        } catch (Exception e) {
////            e.printStackTrace();
////        }
////
////        // 更新进度
////        updateProcess(reportId, 91, 95);
////
////        Map<String, Object> dataMap = new HashMap<>();
////        dataMap.put("fanType", qo.getTypicalType());
////        dataMap.put("aType", qo.getAtypicalType());
////        dataMap.put("info", reportInfo);
////        dataMap.put("fans", typicalFanInfos);
////        dataMap.put("allList", allList);
////        dataMap.put("aFan", nonTypicalFanInfo);
////        dataMap.put("aFans", nonTypicalFanInfos);
////        dataMap.put("booster", reportBoosterInfo);
////
////        /* 结论 */
////        dataMap.put("typical", typicalSummary);
////        dataMap.put("atypical", atypicalSummary);
////
////        /* 图片 */
////        dataMap.put("tiltPics", tiltPics);
////        dataMap.put("tiltPics", tiltPics);
////        dataMap.put("seaTiltPics", seaTiltPics);
////        dataMap.put("shockPics", shockPics);
////        dataMap.put("seaShockPics", seaShockPics);
////
////        dataMap.put("seaTiltStatItems", seaTiltStatItems);
////        dataMap.put("seaShockStatItems", seaShockStatItems);
////
////        String uuid = CommonUtil.uuid();
////        String outputDirPre = "/var/owp-upload-files/reports/"+ uuid + "/";
////        makeExist(outputDirPre);
////        String pre = qo.getIssueYear() + "第" + qo.getIssueNum() + "期(总第" + qo.getIssueNum() + "期)月报";
////        String outputDirSuf = pre + ".doc";
////        String pdfOutputDirSuf = pre + ".pdf";
////        WordExportUtil.createDocFile(templateFile, dataMap, outputDirPre + outputDirSuf);
////
////        // 更新报告状态
////        reportService.updatePath(reportId, "已完成", "owp-files/reports/" + uuid + "/" + outputDirSuf, "owp-files/reports/" + uuid + "/" + pdfOutputDirSuf);
////    }
//
//    /**
//     * 更新进度
//     */
//    private void updateProcess(Integer reportId, int min, int max) {
//        try {
//            reportService.updateStatus(reportId, "生成中(" + CommonUtil.randomInt(min, max) + "%)");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    private void makeExist(String outputDirPre) {
//        File f = new File(outputDirPre);
//        if (!f.exists()) {
//            f.mkdirs();
//        }
//    }
//
//    /**
//     * 设置概览
//     */
//    private void configOverviews(ReportInfo reportInfo, String fieldNum) {
//        ReportBaseInfo baseInfo = infoService.getBaseInfo(fieldNum);
//        if (baseInfo == null) {
//            return;
//        }
//
//        List<Map<String, List<Map<String, Object>>>> projectOverviews = JsoupUtil.getRichText(baseInfo.getProjectOverview());
//        List<Map<String, List<Map<String, Object>>>> monitorOverviews = JsoupUtil.getRichText(baseInfo.getMonitorOverview());
//        reportInfo.setProjectOverviews(projectOverviews);
//        reportInfo.setMonitorOverviews(monitorOverviews);
//    }
//
//    /**
//     * 设置报告编校审人员
//     */
//    private void configReportPerson(ReportInfo reportInfo, String fieldNum) {
//        ReportPerson person = infoService.getPersonConf(fieldNum);
//        if (person != null) {
//            reportInfo.setEditor(person.getEditor());
//            reportInfo.setChecker(person.getChecker());
//            reportInfo.setReviewer(person.getReviewer());
//            reportInfo.setCompany(person.getCompany());
//            reportInfo.setDept(person.getDept());
//        }
//    }
//
//    /**
//     * 获取典型风机总结信息
//     */
//    private ReportSummaryInfo getReportTypicalSummaryInfo(List<SeaFacilityCommon> seaFacilities, List<StatItem> tiltStatItems,
//                                                          List<StatItem> shockStatItems, List<StatItem> stressStatItems,
//                                                          boolean isTypical) {
//        ReportSummaryInfo summaryInfo = new ReportSummaryInfo();
//
//        // 获取目标风机的ID
//        List<SeaFacilityCommon> fans = seaFacilities.stream()
//                .filter(e -> e.getId().startsWith("1-"))
//                .filter(e -> (isTypical ? e.getIsTypical() != null && e.getIsTypical() : e.getIsTypical() == null || !e.getIsTypical())).collect(Collectors.toList());
//
//        if (ObjectUtils.isEmpty(fans)) {
//            return summaryInfo;
//        }
//        List<String> fanIds = fans.stream().map(SeaFacilityCommon::getId).collect(Collectors.toList());
//        System.out.println("fanIds:" + fanIds);
//
//        /* 倾斜 */
//        List<StatItem> tiltBaseMinItems = tiltStatItems.stream().filter(e -> fanIds.contains(e.getSeaFacilityId()) && e.getMin() != null && "基础顶".equals(e.getPosition())).collect(Collectors.toList());
//        System.out.println("tiltBaseMinItems:" + tiltBaseMinItems);
//        if (tiltBaseMinItems.size() > 0) {
//            summaryInfo.setTiltBaseMin(tiltBaseMinItems.stream().mapToDouble(StatItem::getMin).min().orElse(0));
//        }
//        List<StatItem> tiltBaseMaxItems = tiltStatItems.stream().filter(e -> fanIds.contains(e.getSeaFacilityId()) && e.getMax() != null && "基础顶".equals(e.getPosition())).collect(Collectors.toList());
//        System.out.println("tiltBaseMaxItems:" + tiltBaseMaxItems);
//        if (tiltBaseMaxItems.size() != 0) {
//            summaryInfo.setTiltBaseMax(tiltBaseMaxItems.stream().mapToDouble(StatItem::getMax).max().orElse(0));
//        }
//        List<StatItem> tiltBaseAvgItems = tiltStatItems.stream().filter(e -> fanIds.contains(e.getSeaFacilityId()) && e.getAvg() != null && "基础顶".equals(e.getPosition())).collect(Collectors.toList());
//        System.out.println("tiltBaseAvgItems:" + tiltBaseAvgItems);
//        if (tiltBaseAvgItems.size() != 0) {
//            summaryInfo.setTiltBaseAvg(tiltBaseAvgItems.stream().mapToDouble(StatItem::getAvg).average().orElse(0));
//        }
//
//        List<StatItem> tiltTopMinItems = tiltStatItems.stream().filter(e -> fanIds.contains(e.getSeaFacilityId()) && e.getMin() != null && "塔筒顶".equals(e.getPosition())).collect(Collectors.toList());
//        if (tiltTopMinItems.size() > 0) {
//            summaryInfo.setTiltTopMin(tiltTopMinItems.stream().mapToDouble(StatItem::getMin).min().orElse(0));
//        }
//        List<StatItem> tiltTopMaxItems = tiltStatItems.stream().filter(e -> fanIds.contains(e.getSeaFacilityId()) && e.getMax() != null && "塔筒顶".equals(e.getPosition())).collect(Collectors.toList());
//        if (tiltTopMaxItems.size() != 0) {
//            summaryInfo.setTiltTopMax(tiltTopMaxItems.stream().mapToDouble(StatItem::getMax).max().orElse(0));
//        }
//        List<StatItem> tiltTopAvgItems = tiltStatItems.stream().filter(e -> fanIds.contains(e.getSeaFacilityId()) && e.getAvg() != null && "塔筒顶".equals(e.getPosition())).collect(Collectors.toList());
//        if (tiltTopAvgItems.size() != 0) {
//            summaryInfo.setTiltTopAvg(tiltTopAvgItems.stream().mapToDouble(StatItem::getAvg).average().orElse(0));
//        }
//
//        /* 振动 */
//        List<StatItem> shockBaseMinItems = shockStatItems.stream().filter(e -> fanIds.contains(e.getSeaFacilityId()) && e.getMin() != null && "基础顶".equals(e.getPosition())).collect(Collectors.toList());
//        if (shockBaseMinItems.size() > 0) {
//            summaryInfo.setShockBaseMin(shockBaseMinItems.stream().mapToDouble(StatItem::getMin).min().orElse(0));
//        }
//        List<StatItem> shockBaseMaxItems = shockStatItems.stream().filter(e -> fanIds.contains(e.getSeaFacilityId()) && e.getMax() != null && "基础顶".equals(e.getPosition())).collect(Collectors.toList());
//        if (shockBaseMaxItems.size() != 0) {
//            summaryInfo.setShockBaseMax(shockBaseMaxItems.stream().mapToDouble(StatItem::getMax).max().orElse(0));
//        }
//
//        List<StatItem> shockTopMinItems = shockStatItems.stream().filter(e -> fanIds.contains(e.getSeaFacilityId()) && e.getMin() != null && "塔筒顶".equals(e.getPosition())).collect(Collectors.toList());
//        if (shockTopMinItems.size() > 0) {
//            summaryInfo.setShockTopMin(shockTopMinItems.stream().mapToDouble(StatItem::getMin).min().orElse(0));
//        }
//        List<StatItem> shockTopMaxItems = shockStatItems.stream().filter(e -> fanIds.contains(e.getSeaFacilityId()) && e.getMax() != null && "塔筒顶".equals(e.getPosition())).collect(Collectors.toList());
//        if (shockTopMaxItems.size() != 0) {
//            summaryInfo.setShockTopMax(shockTopMaxItems.stream().mapToDouble(StatItem::getMax).max().orElse(0));
//        }
//
//        /* 应力 */
//        List<StatItem> stressBaseMinItems = stressStatItems.stream().filter(e -> fanIds.contains(e.getSeaFacilityId()) && e.getMin() != null && "基础顶".equals(e.getPosition())).collect(Collectors.toList());
//        if (stressBaseMinItems.size() > 0) {
//            summaryInfo.setStressMin(stressBaseMinItems.stream().mapToDouble(StatItem::getMin).min().orElse(0));
//        }
//        List<StatItem> stressBaseMaxItems = stressStatItems.stream().filter(e -> fanIds.contains(e.getSeaFacilityId()) && e.getMax() != null && "基础顶".equals(e.getPosition())).collect(Collectors.toList());
//        if (stressBaseMaxItems.size() != 0) {
//            summaryInfo.setStressMax(stressBaseMaxItems.stream().mapToDouble(StatItem::getMax).max().orElse(0));
//        }
//        return summaryInfo;
//    }
//
//    /**
//     * 获取升压站报告信息
//     */
//    private ReportBoosterInfo getReportBoosterInfo(List<SeaFacilityCommon> seaFacilities, Map<Integer, PointCommon> pointMap,
//                                                   Map<String, List<StatItem>> tiltStatItemsMap, Map<String, List<StatItem>> shockStatItemsMap,
//                                                   Map<String, List<StatItem>> stressStatItemsMap, Map<String, List<StatItem>> corrodeStatItemsMap,
//                                                   Map<String, List<StatItem>> subsideStatItemsMap, Map<String, List<ReportPic>> picsMap) {
//        ReportBoosterInfo boosterInfo = new ReportBoosterInfo();
//
//        /* 获取升压站 */
//        SeaFacilityCommon booster = filterBooster(seaFacilities);
//        if (booster == null) {
//            return boosterInfo;
//        }
//
//        /* 倾斜 */
//        List<StatItem> tiltStatItems = tiltStatItemsMap.get(booster.getId());
//        if (!ObjectUtils.isEmpty(tiltStatItems)) {
//            Double min = null;
//            long tiltStatItemsCount = tiltStatItems.stream().filter(e -> e.getMin() != null).count();
//            if (tiltStatItemsCount != 0) {
//                min = tiltStatItems.stream().filter(e -> e.getMin() != null).mapToDouble(StatItem::getMin).min().orElse(0);
//            }
//            Double max = null;
//            if (tiltStatItemsCount != 0) {
//                max = tiltStatItems.stream().filter(e -> e.getMax() != null).mapToDouble(StatItem::getMax).max().orElse(0);
//            }
//            Double avg = null;
//            if (tiltStatItemsCount != 0) {
//                avg = tiltStatItems.stream().filter(e -> e.getAvg() != null).mapToDouble(StatItem::getAvg).average().orElse(0);
//            }
//            boosterInfo.setTiltMin(min);
//            boosterInfo.setTiltMax(max);
//            boosterInfo.setTiltAvg(avg);
//        }
//
//        /* 沉降 */
//        List<StatItem> subsideStatItems = subsideStatItemsMap.get(booster.getId());
//        if (!ObjectUtils.isEmpty(subsideStatItems)) {
//            List<StatItem> settles = subsideStatItems.stream().filter(e -> e.getPointId() != null).collect(Collectors.toList());
//            if (settles.size() >= 2) {
//                StatItem first = settles.get(0);
//                List<StatItem> statItems = settles.subList(1, settles.size());
//
//                /* 最小值、最大值 */
//                Double min = null;
//                long count = statItems.stream().filter(e -> e.getMin() != null).count();
//                if (count != 0) {
//                    min = statItems.stream().filter(e -> e.getMin() != null).mapToDouble(StatItem::getMin).min().orElse(0);
//                }
//                Double max = null;
//                if (count != 0) {
//                    max = statItems.stream().filter(e -> e.getMax() != null).mapToDouble(StatItem::getMax).max().orElse(0);
//                }
//                boosterInfo.setSettleMin(min);
//                boosterInfo.setSettleMax(max);
//
//                /* 平均值的最小值、最大值 */
//                Double avgMin = null;
//                count = statItems.stream().filter(e -> e.getAvg() != null).count();
//                if (count != 0) {
//                    avgMin = statItems.stream().filter(e -> e.getAvg() != null).mapToDouble(StatItem::getAvg).min().orElse(0);
//                }
//                Double avgMax = null;
//                if (count != 0) {
//                    avgMax = statItems.stream().filter(e -> e.getAvg() != null).mapToDouble(StatItem::getAvg).max().orElse(0);
//                }
//                boosterInfo.setSettleAvgMin(avgMin);
//                boosterInfo.setSettleAvgMax(avgMax);
//
//                /* 测点 */
//                List<SettlePoint> settlePoints = new ArrayList<>();
//                for (StatItem item : statItems) {
//                    if (item.getPointId() == null) {
//                        continue;
//                    }
//                    PointCommon point = pointMap.get(item.getPointId());
//                    if (point != null) {
//                        settlePoints.add(new SettlePoint(item.getPointNo(), point.getInstallElevation(), item.getMin(), item.getMax(), item.getAvg()));
//                    }
//                }
//                if (!ObjectUtils.isEmpty(settlePoints)) {
//                    boosterInfo.setSettlePoints(settlePoints);
//                }
//            }
//        }
//
//        /* 振动 */
//        List<StatItem> shockStatItems = shockStatItemsMap.get(booster.getId());
//        if (!ObjectUtils.isEmpty(shockStatItems)) {
//            Double min = null;
//            long shockStatItemsCount = shockStatItems.stream().filter(e -> e.getMin() != null).count();
//            if (shockStatItemsCount != 0) {
//                min = shockStatItems.stream().filter(e -> e.getMin() != null).mapToDouble(StatItem::getMin).min().orElse(0);
//            }
//            Double max = null;
//            if (shockStatItemsCount != 0) {
//                max = shockStatItems.stream().filter(e -> e.getMin() != null).mapToDouble(StatItem::getMax).max().orElse(0);
//            }
//            boosterInfo.setShockMin(min);
//            boosterInfo.setShockMax(max);
//        }
//
//        /* 应力 */
//        List<StatItem> stressStatItems = stressStatItemsMap.get(booster.getId());
//        if (!ObjectUtils.isEmpty(stressStatItems)) {
//            /* 最小值、最大值 */
//            Double min = null;
//            long count = stressStatItems.stream().filter(e -> e.getMin() != null).count();
//            if (count != 0) {
//                min = stressStatItems.stream().filter(e -> e.getMin() != null).mapToDouble(StatItem::getMin).min().orElse(0);
//            }
//            Double max = null;
//            if (count != 0) {
//                max = stressStatItems.stream().filter(e -> e.getMax() != null).mapToDouble(StatItem::getMax).max().orElse(0);
//            }
//            boosterInfo.setStressMin(min);
//            boosterInfo.setStressMax(max);
//
//            /* 平均值的最小值、最大值 */
//            Double avgMin = null;
//            count = stressStatItems.stream().filter(e -> e.getAvg() != null).count();
//            if (count != 0) {
//                avgMin = stressStatItems.stream().filter(e -> e.getAvg() != null).mapToDouble(StatItem::getAvg).min().orElse(0);
//            }
//            Double avgMax = null;
//            if (count != 0) {
//                avgMax = stressStatItems.stream().filter(e -> e.getAvg() != null).mapToDouble(StatItem::getAvg).max().orElse(0);
//            }
//            boosterInfo.setStressAvgMin(avgMin);
//            boosterInfo.setStressAvgMax(avgMax);
//
//            /* 测点 */
//            List<StressPoint> stressPoints = new ArrayList<>();
//            for (StatItem item : stressStatItems) {
//                if (item.getPointId() == null) {
//                    continue;
//                }
//                PointCommon point = pointMap.get(item.getPointId());
//                if (point != null) {
//                    stressPoints.add(new StressPoint(item.getPointNo(), point.getInstallElevation(), point.getInstallOrient(), item.getMin(), item.getMax(), item.getAvg()));
//                }
//            }
//            if (!ObjectUtils.isEmpty(stressPoints)) {
//                boosterInfo.setStressPoints(stressPoints);
//            }
//
//            /* 图片 */
//            List<ReportPic> reportPics = picsMap.get(booster.getId());
//            if (!ObjectUtils.isEmpty(reportPics)) {
//                for (ReportPic reportPic : reportPics) {
//                    if ("钢板应力".equals(reportPic.getType())) {
//                        boosterInfo.setStressPic(reportPic.getBase64());
//                    } else if ("静力水准".equals(reportPic.getType())) {
//                        boosterInfo.setSettlePicBase64(reportPic.getBase64());
//                    }
//                }
//            }
//
//            /* 腐蚀 */
//            List<StatItem> corrodeStatItems = corrodeStatItemsMap.get(booster.getId());
//            if (!ObjectUtils.isEmpty(corrodeStatItems)) {
//                int pointCount = 0;
//                StringBuilder pointNoSb = new StringBuilder();
//                StringBuilder pointAvgValSb = new StringBuilder();
//                for (StatItem item : corrodeStatItems) {
//                    if (item.getPointNo() == null) {
//                        continue;
//                    }
//                    pointCount++;
//                    pointNoSb.append(item.getPointNo() + "、");
//                    pointAvgValSb.append(formatDouble(item.getAvg()) + "、");
//                }
//                if (pointCount > 0) {
//                    pointNoSb.deleteCharAt(pointNoSb.length() - 1);
//                    pointAvgValSb.deleteCharAt(pointAvgValSb.length() - 1);
//                }
//                String speStr = null;
//                if (pointCount > 2) {
//                    // 两个测点用顿号，三个及以上测点用~
//                    String start = null;
//                    String end = null;
//                    String[] arr = pointNoSb.toString().split("、");
//                    String[] split = arr[0].split("-");
//                    if (split.length == 2) {
//                        start = arr[0];
//                    }
//                    String[] lastSplit = arr[arr.length - 1].split("-");
//                    if (lastSplit.length == 2) {
//                        end = lastSplit[1];
//                    }
//                    if (start != null && end != null) {
//                        speStr = start + "~" + end;
//                    }
//                }
//                boosterInfo.setCorrodeSize(pointCount);
//                if (speStr == null) {
//                    boosterInfo.setCorrodePointNos(pointNoSb.toString());
//                } else {
//                    boosterInfo.setCorrodePointNos(speStr);
//                }
//                boosterInfo.setCorrodePointValues(pointAvgValSb.toString());
//
//                // 图片
//                List<ReportPic> pics = picsMap.get(booster.getId());
//                if (!ObjectUtils.isEmpty(pics)) {
//                    for (ReportPic pic : pics) {
//                        if ("腐蚀".equals(pic.getType())) {
//                            boosterInfo.setCorrodePic(pic.getBase64());
//                        }
//                    }
//                }
//            }
//        }
//        return boosterInfo;
//    }
//
//    /**
//     * 从海上设施中过滤升压站
//     */
//    private SeaFacilityCommon filterBooster(List<SeaFacilityCommon> seaFacilities) {
//        for (SeaFacilityCommon seaFacility : seaFacilities) {
//            if (seaFacility.getId().startsWith("2-")) {
//                return seaFacility;
//            }
//        }
//        return null;
//    }
//
//    private void fillNonTypicalCorrode(SeaFacilityCommon nonTypicalFan, List<ReportNonTypicalFanInfo> nonTypicalFanInfos, Map<String,
//            List<StatItem>> corrodeStatItemsMap, Map<Integer, PointCommon> pointMap, Map<String, List<ReportPic>> picsMap) {
//        List<StatItem> items = corrodeStatItemsMap.get(nonTypicalFan.getId());
//        if (ObjectUtils.isEmpty(items)) {
//            return;
//        }
//
//        int count = 0;
//        StringBuilder pointNoSb = new StringBuilder();
//        StringBuilder pointAvgValSb = new StringBuilder();
//        for (StatItem item : items) {
//            if (item.getPointNo() == null) {
//                continue;
//            }
//            count++;
//            pointNoSb.append(item.getPointNo() + "、");
//            pointAvgValSb.append(formatDouble(item.getAvg()) + "、");
//        }
//        if (count > 0) {
//            pointNoSb.deleteCharAt(pointNoSb.length() - 1);
//            pointAvgValSb.deleteCharAt(pointAvgValSb.length() - 1);
//        }
//        String spe = null;
//        if (count > 2) {
//            // 两个测点用顿号，三个及以上测点用~
//            String start = null;
//            String end = null;
//            String[] arr = pointNoSb.toString().split("、");
//            String[] split = arr[0].split("-");
//            if (split.length == 2) {
//                start = arr[0];
//            }
//            String[] lastSplit = arr[arr.length - 1].split("-");
//            if (lastSplit.length == 2) {
//                end = lastSplit[1];
//            }
//            if (start != null && end != null) {
//                spe = start + "~" + end;
//            }
//        }
//        ReportNonTypicalFanInfo typicalFanInfo = new ReportNonTypicalFanInfo();
//        typicalFanInfo.setNo(nonTypicalFan.getName());
//        typicalFanInfo.setCorrodeSize(count);
//        if (spe == null) {
//            typicalFanInfo.setCorrodePointNos(pointNoSb.toString());
//        } else {
//            typicalFanInfo.setCorrodePointNos(spe);
//        }
//        typicalFanInfo.setCorrodePointValues(pointAvgValSb.toString());
//
//        // 设置图片
//        List<ReportPic> reportPics = picsMap.get(nonTypicalFan.getId());
//        if (!ObjectUtils.isEmpty(reportPics)) {
//            ReportPic reportPic = reportPics.stream().filter((e -> "腐蚀".equals(e.getType()))).findFirst().orElse(null);
//            if (reportPic != null) {
//                typicalFanInfo.setCorrodePicBase64(reportPic.getBase64());
//            }
//        }
//
//        nonTypicalFanInfos.add(typicalFanInfo);
//    }
//
//    private void fillNonTypicalStress(List<SeaFacilityCommon> nonTypicalFans, ReportTypicalFanInfo nonTypicalFanInfo, Map<String, List<StatItem>> stressStatItemsMap, Map<Integer, PointCommon> pointMap) {
//
//    }
//
//    private void fillNonTypicalShock(List<SeaFacilityCommon> nonTypicalFans, ReportTypicalFanInfo nonTypicalFanInfo, Map<String, List<StatItem>> shockStatItemsMap) {
//        if (ObjectUtils.isEmpty(nonTypicalFans)) {
//            return;
//        }
//
//        Double xMax = null;
//        Double yMax = null;
//        for (SeaFacilityCommon nonTypicalFan : nonTypicalFans) {
//            List<StatItem> items = shockStatItemsMap.get(nonTypicalFan.getId());
//            if (ObjectUtils.isEmpty(items)) {
//                return;
//            }
//            for (StatItem item : items) {
//                if ("基础顶".equals(item.getPosition()) && "X".equals(item.getDirect())) {
//                    if (xMax == null || (item.getMax() != null && xMax < item.getMax())) {
//                        xMax = item.getMax();
//                    }
//                } else if ("基础顶".equals(item.getPosition()) && "Y".equals(item.getDirect())) {
//                    if (yMax == null || (item.getMax() != null && yMax < item.getMax())) {
//                        yMax = item.getMax();
//                    }
//                }
//            }
//        }
//
//        nonTypicalFanInfo.setShockMaxX(xMax);
//        nonTypicalFanInfo.setShockMaxY(yMax);
//    }
//
//    private void fillNonTypicalTilt(List<SeaFacilityCommon> nonTypicalFans, ReportTypicalFanInfo nonTypicalFanInfo, Map<String, List<StatItem>> tiltStatItemsMap) {
//        if (ObjectUtils.isEmpty(nonTypicalFans)) {
//            return;
//        }
//
//        Double baseMinX = null;
//        Double baseMaxX = null;
//        Double baseAvgX_count = 0d;
//        Double baseAvgX_sum = 0d;
//        Double baseMinY = null;
//        Double baseMaxY = null;
//        Double baseAvgY_count = 0d;
//        Double baseAvgY_sum = 0d;
//        Double topMinX = null;
//        Double topMaxX = null;
//        Double topAvgX_count = 0d;
//        Double topAvgX_sum = 0d;
//        Double topMinY = null;
//        Double topMaxY = null;
//        Double topAvgY_count = 0d;
//        Double topAvgY_sum = 0d;
//        for (SeaFacilityCommon nonTypicalFan : nonTypicalFans) {
//            List<StatItem> tiltItems = tiltStatItemsMap.get(nonTypicalFan.getId());
//            if (ObjectUtils.isEmpty(tiltItems)) {
//                return;
//            }
//            for (StatItem tiltItem : tiltItems) {
//                if ("基础顶".equals(tiltItem.getPosition()) && "X".equals(tiltItem.getDirect())) {
//                    if (baseMinX == null || (tiltItem.getMin() != null && baseMinX > tiltItem.getMin())) {
//                        baseMinX = tiltItem.getMin();
//                    }
//                    if (baseMaxX == null || (tiltItem.getMax() != null && baseMaxX < tiltItem.getMax())) {
//                        baseMaxX = tiltItem.getMax();
//                    }
//                    if (tiltItem.getAvg() != null) {
//                        baseAvgX_count ++;
//                        baseAvgX_sum += tiltItem.getAvg();
//                    }
//                } else if ("基础顶".equals(tiltItem.getPosition()) && "Y".equals(tiltItem.getDirect())) {
//                    if (baseMinY == null || (tiltItem.getMin() != null && baseMinY > tiltItem.getMin())) {
//                        baseMinY = tiltItem.getMin();
//                    }
//                    if (baseMaxY == null || (tiltItem.getMax() != null && baseMaxY < tiltItem.getMax())) {
//                        baseMaxY = tiltItem.getMax();
//                    }
//                    if (tiltItem.getAvg() != null) {
//                        baseAvgY_count ++;
//                        baseAvgY_sum += tiltItem.getAvg();
//                    }
//                } else if ("塔筒顶".equals(tiltItem.getPosition()) && "X".equals(tiltItem.getDirect())) {
//                    if (topMinX == null || (tiltItem.getMin() != null && topMinX > tiltItem.getMin())) {
//                        topMinX = tiltItem.getMin();
//                    }
//                    if (topMaxX == null || (tiltItem.getMax() != null && topMaxX < tiltItem.getMax())) {
//                        topMaxX = tiltItem.getMax();
//                    }
//                    if (tiltItem.getAvg() != null) {
//                        topAvgX_count ++;
//                        topAvgX_sum += tiltItem.getAvg();
//                    }
//                } else if ("塔筒顶".equals(tiltItem.getPosition()) && "Y".equals(tiltItem.getDirect())) {
//                    if (topMinY == null || (tiltItem.getMin() != null && topMinY > tiltItem.getMin())) {
//                        topMinY = tiltItem.getMin();
//                    }
//                    if (topMaxY == null || (tiltItem.getMax() != null && topMaxY < tiltItem.getMax())) {
//                        topMaxY = tiltItem.getMax();
//                    }
//                    if (tiltItem.getAvg() != null) {
//                        topAvgY_count ++;
//                        topAvgY_sum += tiltItem.getAvg();
//                    }
//                }
//            }
//        }
//
//        nonTypicalFanInfo.setBaseMinX(baseMinX);
//        nonTypicalFanInfo.setBaseMaxX(baseMaxX);
//        if (baseAvgX_count != 0) {
//            nonTypicalFanInfo.setBaseAvgX(baseAvgX_sum / baseAvgX_count);
//        }
//        nonTypicalFanInfo.setBaseMinY(baseMinY);
//        nonTypicalFanInfo.setBaseMaxY(baseMaxY);
//        if (baseAvgY_count != 0) {
//            nonTypicalFanInfo.setBaseAvgY(baseAvgY_sum / baseAvgY_count);
//        }
//        nonTypicalFanInfo.setTopMinX(topMinX);
//        nonTypicalFanInfo.setTopMaxX(topMaxX);
//        if (topAvgX_count != 0) {
//            nonTypicalFanInfo.setTopAvgX(topAvgX_sum / topAvgX_count);
//        }
//        nonTypicalFanInfo.setTopMinY(topMinY);
//        nonTypicalFanInfo.setTopMaxY(topMaxY);
//        if (topAvgY_count != 0) {
//            nonTypicalFanInfo.setTopAvgY(topAvgY_sum / topAvgY_count);
//        }
//    }
//
//    public static String formatDouble(Double value) {
//        if (value == null) {
//            return null;
//        }
//        BigDecimal bd = new BigDecimal(value);
//        bd = bd.setScale(3, RoundingMode.HALF_UP);
//        return bd.toString();
//    }
//
//    private void fillCorrode(SeaFacilityCommon typicalFan, ReportTypicalFanInfo typicalFanInfo, Map<String, List<StatItem>> corrodeStatItemsMap,
//                             Map<Integer, PointCommon> pointMap, Map<String, List<ReportPic>> picsMap) {
//        List<StatItem> items = corrodeStatItemsMap.get(typicalFan.getId());
//        if (ObjectUtils.isEmpty(items)) {
//            return;
//        }
//
//        int count = 0;
//        StringBuilder pointNoSb = new StringBuilder();
//        StringBuilder pointAvgValSb = new StringBuilder();
//        for (StatItem item : items) {
//            if (item.getPointNo() == null) {
//                continue;
//            }
//            count++;
//            pointNoSb.append(item.getPointNo() + "、");
//            pointAvgValSb.append(formatDouble(item.getAvg()) + "、");
//        }
//        if (count > 0) {
//            pointNoSb.deleteCharAt(pointNoSb.length() - 1);
//            pointAvgValSb.deleteCharAt(pointAvgValSb.length() - 1);
//        }
//        String spe = null;
//        if (count > 2) {
//            // 两个测点用顿号，三个及以上测点用~
//            String start = null;
//            String end = null;
//            String[] arr = pointNoSb.toString().split("、");
//            String[] split = arr[0].split("-");
//            if (split.length == 2) {
//                start = arr[0];
//            }
//            String[] lastSplit = arr[arr.length - 1].split("-");
//            if (lastSplit.length == 2) {
//                end = lastSplit[1];
//            }
//            if (start != null && end != null) {
//                spe = start + "~" + end;
//            }
//        }
//        typicalFanInfo.setCorrodeSize(count);
//
//        if (spe == null) {
//            typicalFanInfo.setCorrodePointNos(pointNoSb.toString());
//        } else {
//            typicalFanInfo.setCorrodePointNos(spe);
//        }
//        typicalFanInfo.setCorrodePointValues(pointAvgValSb.toString());
//
//        // 设置图片
//        List<ReportPic> reportPics = picsMap.get(typicalFan.getId());
//        if (!ObjectUtils.isEmpty(reportPics)) {
//            ReportPic reportPic = reportPics.stream().filter((e -> "腐蚀".equals(e.getType()))).findFirst().orElse(null);
//            if (reportPic != null) {
//                typicalFanInfo.setCorrodePicBase64(reportPic.getBase64());
//            }
//        }
//    }
//
//    private void fillStress(SeaFacilityCommon typicalFan, ReportTypicalFanInfo typicalFanInfo, Map<String, List<StatItem>> stressStatItemsMap,
//                            Map<Integer, PointCommon> pointMap, Map<String, List<ReportPic>> picsMap) {
//        List<StatItem> stressStatItems = stressStatItemsMap.get(typicalFan.getId());
//        if (ObjectUtils.isEmpty(stressStatItems)) {
//            return;
//        }
//
//        /* 最小值、最大值 */
//        Double min = null;
//        long count = stressStatItems.stream().filter(e -> e.getMin() != null).count();
//        if (count != 0) {
//            min = stressStatItems.stream().filter(e -> e.getMin() != null).mapToDouble(StatItem::getMin).min().orElse(0);
//        }
//        Double max = null;
//        if (count != 0) {
//            max = stressStatItems.stream().filter(e -> e.getMax() != null).mapToDouble(StatItem::getMax).max().orElse(0);
//        }
//        typicalFanInfo.setStressMin(min);
//        typicalFanInfo.setStressMax(max);
//
//        /* 平均值的最小值、最大值 */
//        Double avgMin = null;
//        count = stressStatItems.stream().filter(e -> e.getAvg() != null).count();
//        if (count != 0) {
//            avgMin = stressStatItems.stream().filter(e -> e.getAvg() != null).mapToDouble(StatItem::getAvg).min().orElse(0);
//        }
//        Double avgMax = null;
//        if (count != 0) {
//            avgMax = stressStatItems.stream().filter(e -> e.getAvg() != null).mapToDouble(StatItem::getAvg).max().orElse(0);
//        }
//        typicalFanInfo.setStressAvgMin(avgMin);
//        typicalFanInfo.setStressAvgMax(avgMax);
//
//        List<StressPoint> stressTables = new ArrayList<>();
//        for (StatItem item : stressStatItems) {
//            if (item.getPointId() == null) {
//                continue;
//            }
//            PointCommon point = pointMap.get(item.getPointId());
//            if (point != null) {
//                stressTables.add(new StressPoint(item.getPointNo(), point.getInstallElevation(), point.getInstallOrient(), item.getMin(), item.getMax(), item.getAvg()));
//            }
//        }
//        typicalFanInfo.setStressTables(stressTables);
//
//        // 设置图片
//        List<ReportPic> reportPics = picsMap.get(typicalFan.getId());
//        if (!ObjectUtils.isEmpty(reportPics)) {
//            ReportPic reportPic = reportPics.stream().filter((e -> "钢板应力".equals(e.getType()))).findFirst().orElse(null);
//            if (reportPic != null) {
//                typicalFanInfo.setStressPicBase64(reportPic.getBase64());
//            }
//        }
//    }
//
//    private void fillShock(SeaFacilityCommon typicalFan, ReportTypicalFanInfo typicalFanInfo, Map<String, List<StatItem>> shockStatItemsMap) {
//        List<StatItem> items = shockStatItemsMap.get(typicalFan.getId());
//        if (ObjectUtils.isEmpty(items)) {
//            return;
//        }
//
//        Double xMax = null;
//        Double yMax = null;
//        for (StatItem item : items) {
//            if ("基础顶".equals(item.getPosition()) && "X".equals(item.getDirect())) {
//                if (xMax == null || xMax < item.getMax()) {
//                    xMax = item.getMax();
//                }
//            } else if ("基础顶".equals(item.getPosition()) && "Y".equals(item.getDirect())) {
//                if (yMax == null || yMax < item.getMax()) {
//                    yMax = item.getMax();
//                }
//            }
//        }
//        typicalFanInfo.setShockMaxX(xMax);
//        typicalFanInfo.setShockMaxY(yMax);
//    }
//
//
//    private void fillTilt(SeaFacilityCommon typicalFan, ReportTypicalFanInfo typicalFanInfo, Map<String, List<StatItem>> tiltStatItemsMap) {
//        List<StatItem> tiltItems = tiltStatItemsMap.get(typicalFan.getId());
//        if (ObjectUtils.isEmpty(tiltItems)) {
//            return;
//        }
//
//        for (StatItem tiltItem : tiltItems) {
//            if ("基础顶".equals(tiltItem.getPosition()) && "X".equals(tiltItem.getDirect())) {
//                typicalFanInfo.setBaseMinX(tiltItem.getMin());
//                typicalFanInfo.setBaseMaxX(tiltItem.getMax());
//                typicalFanInfo.setBaseAvgX(tiltItem.getAvg());
//            } else if ("基础顶".equals(tiltItem.getPosition()) && "Y".equals(tiltItem.getDirect())) {
//                typicalFanInfo.setBaseMinY(tiltItem.getMin());
//                typicalFanInfo.setBaseMaxY(tiltItem.getMax());
//                typicalFanInfo.setBaseAvgY(tiltItem.getAvg());
//            } else if ("塔筒顶".equals(tiltItem.getPosition()) && "X".equals(tiltItem.getDirect())) {
//                typicalFanInfo.setTopMinX(tiltItem.getMin());
//                typicalFanInfo.setTopMaxX(tiltItem.getMax());
//                typicalFanInfo.setTopAvgX(tiltItem.getAvg());
//            } else if ("塔筒顶".equals(tiltItem.getPosition()) && "Y".equals(tiltItem.getDirect())) {
//                typicalFanInfo.setTopMinY(tiltItem.getMin());
//                typicalFanInfo.setTopMaxY(tiltItem.getMax());
//                typicalFanInfo.setTopAvgY(tiltItem.getAvg());
//            }
//        }
//    }
//
//    /**
//     * 设置报告编写时间
//     */
//    private void configReportWriteTime(ReportInfo reportInfo, String writeTime) {
//        String[] timeArr = writeTime.split("-");
//        reportInfo.setWriteTimeYear(Integer.parseInt(timeArr[0]));
//        reportInfo.setWriteTimeMonth(Integer.parseInt(timeArr[1]));
//        reportInfo.setWriteTimeDay(Integer.parseInt(timeArr[2]));
//    }
//
//    /**
//     * 设置报告年份和期数
//     */
//    private void configReportPhase(ReportInfo reportInfo, Integer year, Integer phaseNum, String startTime) {
//        reportInfo.setYear(year);
//        reportInfo.setTotalPhaseNum(phaseNum);
//
//        String[] arr = startTime.split("-");
//        if (arr != null && arr.length > 1) {
//            reportInfo.setPhaseNum(Integer.parseInt(arr[1]));
//        }
//    }
//
//    /**
//     * 设置报告名称
//     */
//    private void configReportName(ReportInfo reportInfo, String fieldNum) {
//        String fieldName = windService.getFieldNameByFieldNum(fieldNum);
//        reportInfo.setFieldName(fieldName);
//    }
//
//
//    private List<StatItem> attrProcessLine(List<InstCommon> fieldInsts, Map<String, List<PointCommon>> seaPointMap,
//                                           Map<Integer, PointCommon> pointMap, List<SeaFacilityCommon> fieldSeaFacilities,
//                                           List<AttrCommon> fieldAttrs, String startTime, String endTime, List<ReportPic> pics,
//                                           String monitorItem, String attrName, String direct, Set<String> pointDirectSet, Integer pointNum,
//                                           int totalSize, int chartId, double[] wH, String dirName, Boolean isMark, List<PngInfo> pngInfos) throws ParseException {
//        Integer instId = getInstId(fieldInsts, monitorItem, direct);
//        if (instId == null || ObjectUtils.isEmpty(fieldSeaFacilities)) {
//            return Collections.emptyList();
//        }
//        log.info("{}仪器ID: {}", monitorItem, instId);
//
//        List<StatItem> statItems = new ArrayList<>();
//
//        Integer attrId = getAttrId(fieldAttrs, instId, attrName);
//        log.info("attrId = {}, attrName = {}", attrId, attrName);
//        if (attrId == null) {
//            log.error("找不到该attrId：{}", attrId);
//            return Collections.emptyList();
//        }
//
//        /* 获取基础顶、塔筒顶数据 */
//        for (String pointDirect : pointDirectSet) {
//            String[] split = pointDirect.split("-");
//            int pointId = Integer.parseInt(split[0]);
//            int d = 0;
//            if ("X".equals(split[1])) {
//                d = 1;
//            } else if ("Y".equals(split[1])) {
//                d = 2;
//            } else if ("Z".equals(split[1])) {
//                d = 3;
//            }
//            List<Datum> datumList = getData(instId, attrId, startTime, endTime, pointId, d);
//
//            // 生成图片
//            genPic(null, datumList, pointId, null, pointMap,
//                    pics, monitorItem, statItems, split[1], attrName, startTime, endTime,
//                    pointDirectSet, pointNum, totalSize, chartId, wH, dirName, isMark, pngInfos);
//
//            try {
//                TimeUnit.MILLISECONDS.sleep(200);
//            } catch (InterruptedException e) {
//                e.printStackTrace();
//            }
//        }
//        return statItems;
//    }
//
//    /**
//     * 生成过程线图片
//     */
//    private void genPic(String seaFacilityId, List<Datum> datumList, Integer pointId,
//                        SeaFacilityCommon seaFacility, Map<Integer, PointCommon> pointMap, List<ReportPic> pics, String monitorItem, List<StatItem> statItems,
//                        String direct, String attrName, String startTime, String endTime, Set<String> pointDirectSet, Integer pointNum, int totalSize,
//                        int chartId, double[] wH, String dirName, Boolean isMark, List<PngInfo> pngInfos) {
//        String pointNo = pointMap.get(pointId) == null ? "" : pointMap.get(pointId).getNo();
//        pngInfos.add(new PngInfo(pointId, pointNo, direct, datumList, monitorItem));
//    }
//
//    private void updateMyProcess(int chartId, int totalSize) {
//        double process = ++curCount * 1.0 / totalSize * 100;
//        if (process > 99) {
//            process = 99;
//        }
//        chartMapper.updateStatus(chartId, "生成中(" + String.format("%.1f", process) + "%)", null);
//    }
//
//    static int x = 1000;
//
//    private void pngData(String seaFacilityId, List<Series> series, String monitorItem, List<ReportPic> pics, String directStr,
//                         String attrName, List<Long> millis, int chartId, double[] wH, String dirName, Boolean isMark) {
//        File dir = new File("/data/highcharts-export/" + dirName);
//        if (!dir.exists()) {
//            dir.mkdirs();
//        }
//
//        if (ObjectUtils.isEmpty(series)) {
//            return;
//        }
//        String arrStr = serializeSeries(series, isMark);
//        String pointNo = series.stream().map(Series::getName).collect(Collectors.joining(","));
//        String json = null;
//        try {
//            json = CommonUtil.readResources("data/highcharts-example-chart.json");
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//        if (json == null) {
//            return;
//        }
//        json = json.replace("customWidth", String.valueOf(wH[0]));
//        json = json.replace("customHeight", String.valueOf(wH[1]));
//        json = json.replace("分量名", attrName);
//        json = json.replace("element", arrStr);
//        json = json.replace("millisXXX", StringUtils.join(millis, ","));
//        String jsonPath = dir.getAbsolutePath() + "/infile_" + attrName + "-" + pointNo + ".json";
//        try {
//            FileUtil.writeFile(jsonPath, json);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        String outPngPath = dir.getAbsolutePath() + "/outfile_" + attrName + "-" + pointNo + ".png";
//        executeCMD("highcharts-export-server --globalOptions {global:{useUTC:false}} --scale 2 -infile " + jsonPath + " -outfile " + outPngPath);
//        System.out.println("正在生成图片：" + outPngPath);
//        pics.add(new ReportPic(String.valueOf(x++), seaFacilityId, monitorItem, pointNo, getImgBase64(outPngPath), directStr));
//        for (File file : dir.listFiles()) {
//            if (!file.getName().endsWith(".png")) {
//                file.delete();
//            }
//        }
//    }
//
//    private static String serializeSeries(List<Series> series, Boolean isMark) {
//        String[] colors = {"#3070b7", "#c95c2e", "#00ff00", "#9900ff", "#ff00ff", "#0865f7"};
//        if (ObjectUtils.isEmpty(series)) {
//            return null;
//        }
//
//        StringBuilder sb = new StringBuilder();
//        int colorIndex = 0;
//        for (Series s : series) {
//            sb.append("{");
//            sb.append("\"lineWidth\":");
//            sb.append(s.getLineWidth());
//            sb.append(",");
//
//            sb.append("\"type\":\"");
//            sb.append(s.getType());
//            sb.append("\",");
//
//            sb.append("\"name\":\"");
//            sb.append(s.getName());
//            sb.append("\",");
//
//            sb.append("\"color\":\"");
//            sb.append(colors[colorIndex++]);
//            sb.append("\",");
//            if (colorIndex >= colors.length) {
//                colorIndex = 0;
//            }
//
//            sb.append("\"data\":[");
//
//            Map<Long, Float> markTimeMap = getMarkTimes(s.getData());
//            boolean flag = false;
//            for (Datum datum : s.getData()) {
//                if (isMark != null && isMark) {
//                    Float aFloat = markTimeMap.get(datum.getTime().getTime());
//                    if (aFloat != null) {
//                        sb.append("{\"marker\": {\"enabled\": true, \"radius\": 3}, \"x\": " + datum.getTime().getTime() + ", \"y\": " + aFloat + "},");
//                        continue;
//                    }
//                }
//                sb.append("[" + datum.getTime().getTime() + "," + datum.getAttrVal() + "],");
//                flag = true;
//            }
//            if (flag) {
//                sb.deleteCharAt(sb.length() - 1);
//            }
//            sb.append("]},");
//        }
//        if (sb.charAt(sb.length() - 1) == ',') {
//            sb.deleteCharAt(sb.length() - 1);
//        }
//        return sb.toString();
//    }
//
//    private static Map<Long, Float> getMarkTimes(List<Datum> data) {
//        if (ObjectUtils.isEmpty(data)) {
//            return Collections.emptyMap();
//        }
//
//        Map<Long, Float> map = new HashMap<>();
//        Long lastTime = data.get(0).getTime().getTime();
//        for (Datum datum : data) {
//            long time = datum.getTime().getTime();
//            Float val = datum.getAttrVal();
//            if (time - lastTime > 23 * 3600 * 1000) {
//                map.put(time, val);
//                lastTime = time;
//            }
//        }
//        return map;
//    }
//
//    /**
//     * 将图片转换成Base64编码
//     * @param imgFile 待处理图片地址
//     * @return
//     */
//    public static String getImgBase64(String imgFile) {
//        // 将图片文件转化为二进制流
//        InputStream in = null;
//        byte[] data = null;
//        // 读取图片字节数组
//        try {
//            in = new FileInputStream(imgFile);
//            data = new byte[in.available()];
//            in.read(data);
//            in.close();
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//        return Base64.getEncoder().encodeToString(data);
//    }
//
//
//    public static void executeCMD(String cmd) {
//        try {
//            Runtime mt = Runtime.getRuntime();
//            Process pro = mt.exec(cmd);
//            InputStream ers = pro.getErrorStream();
//            pro.waitFor();
//        } catch (IOException ioe) {
//            ioe.printStackTrace();
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }
//    }
//
//    private Integer getAttrId(List<AttrCommon> fieldAttrs, Integer instId, String attrName) {
//        if (ObjectUtils.isEmpty(fieldAttrs)) {
//            return null;
//        }
//
//        for (AttrCommon attr : fieldAttrs) {
//            if (instId.equals(attr.getInstId()) && attr.getName().startsWith(attrName)) {
//                return attr.getId();
//            }
//        }
//        return null;
//    }
//
//    /**
//     * 获取倾斜仪器ID
//     */
//    private Integer getInstId(List<InstCommon> fieldInsts, String type, String direct) {
//        if (ObjectUtils.isEmpty(fieldInsts)) {
//            return null;
//        }
//
//        for (InstCommon inst : fieldInsts) {
//            if (direct != null) {
//                if (inst.getMonitorName().equals(type) && inst.getDirect().equals(direct)) {
//                    return inst.getId();
//                }
//            } else {
//                if (inst.getMonitorName().equals(type)) {
//                    return inst.getId();
//                }
//            }
//        }
//        return null;
//    }
//
//    public static long secondsBetween(String startTime, String endTime) throws ParseException {
//        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
//        long eTime = df.parse(endTime).getTime();
//        long sTime = df.parse(startTime).getTime();
//        long diff = (eTime - sTime) / 1000;
//        return diff;
//    }
//
//    private List<Datum> getData(Integer instId, Integer attrId, String startTime, String endTime, Integer pointId, int direct) throws ParseException {
//
//        String tabName = "inst_" + instId;
//        String colName = "c_" + attrId;
////        StringBuilder sql = new StringBuilder("select ts, point, direct, " + colName + " from " + tabName + " where ts between '" + startTime + " 00:00:00' and '" + endTime + " 23:59:59'");
////        // 测点范围
////        sql.append(" and point in " + getStrPointIds(pointIds));
//
////        long seconds = secondsBetween(startTime, endTime);
////        int size = 800;
////        int interval;
////        if (seconds <= size) {
////            interval = 60;
////        } else {
////            interval = (int) (seconds / size);
////        }
////
////        StringBuilder sql = new StringBuilder("select first(ts) as ts, first(point) as point, first(direct) as direct, first(" + colName + ") as " + colName + " from " + tabName + " where ts between '" + startTime + " 00:00:00' and '" + endTime + " 23:59:59'");
////        // 测点范围
////        sql.append(" and point = " + pointId);
////        sql.append(" and direct = " + direct);
////        sql.append(" and approval != 3");
////        sql.append(" INTERVAL(" + interval + "s)");
//
//        StringBuilder sql = new StringBuilder("select ts, point, direct, " + colName + " from " + tabName + " where ts between '" + startTime + " 00:00:00' and '" + endTime + " 23:59:59'");
//        // 测点范围
//        sql.append(" and point = " + pointId);
//        sql.append(" and direct = " + direct);
//        sql.append(" and approval != 3");
//
//
//        log.info("sql:{}", sql);
//
//        List<Map<String, Object>> records = tdService.selectMulti(sql.toString());
//        System.out.println("records.size():"+records.size());
//        if (records.size() == 0) {
//            return Collections.emptyList();
//        }
//
//        List<Datum> data = new ArrayList<>();
//        for (Map<String, Object> record : records) {
//            Date ts = (Date) record.get("ts");
//            Integer point = (Integer) record.get("point");
//            Integer d = (Integer) record.get("direct");
//            Float attrVal = (Float) record.get(colName);
//            if (attrVal != null) {
//                data.add(new Datum(point, d, ts, attrId, attrVal));
//            }
//        }
//        return data;
//    }
//
//    private String getStrPointIds(List<Integer> pointIds) {
//        StringBuilder sb = new StringBuilder("(");
//        for (Integer pointId : pointIds) {
//            sb.append(pointId);
//            sb.append(",");
//        }
//        sb.deleteCharAt(sb.length() - 1);
//        sb.append(")");
//        return sb.toString();
//    }
//
//    /**
//     * 获取相关测点
//     */
//    private List<Integer> getRelatePointIds(Integer instId, String seaId, Map<String, List<PointCommon>> seaPointMap, boolean isTop) {
//        List<PointCommon> points = seaPointMap.get(seaId);
//        if (ObjectUtils.isEmpty(points)) {
//            return Collections.emptyList();
//        }
//
//        return points.stream().filter(e -> {
//            if (!instId.equals(e.getInstId())) {
//                return false;
//            }
//            if (isTop) {
//                return e.getInstallElevation().startsWith("塔筒顶");
//            }
//            return !e.getInstallElevation().startsWith("塔筒顶");
//        }).map(PointCommon::getId).collect(Collectors.toList());
//    }
//
//    /**
//     * 获取某风场下所有测点，并按海上设施分类
//     */
//    private Map<String, List<PointCommon>> toSeaPointMap(List<PointCommon> fieldPoints) {
//        if (ObjectUtils.isEmpty(fieldPoints)) {
//            return Collections.emptyMap();
//        }
//
//        return fieldPoints.stream().collect(groupingBy(PointCommon::getSeaFacilityId));
//    }
//
//    /**
//     * 获取某风场下所有测点，并按海上设施分类
//     */
//    private Map<Integer, PointCommon> toPointMap(List<PointCommon> fieldPoints) {
//        if (ObjectUtils.isEmpty(fieldPoints)) {
//            return Collections.emptyMap();
//        }
//
//        Map<Integer, PointCommon> map = new HashMap<>(fieldPoints.size());
//        for (PointCommon point : fieldPoints) {
//            map.put(point.getId(), point);
//        }
//        return map;
//    }
//
//    private static int curCount = 0;
//
////    /**
////     * 生成过程线
////     */
////    public void genChart(Chart chart) throws ParseException {
////        double[] wH = parseWH(chart.getImgType());
////
////        String fieldNum = chart.getFieldNum();
////        Set<String> set = new HashSet<>();
////        set.addAll(Arrays.asList(chart.getIds().split(",")));
////
////        List<Long> millis = null;
////        try {
////            millis = TimeUtil.apartDate(chart.getStartTime(), chart.getEndTime(), 5);
////        } catch (ParseException e) {
////            e.printStackTrace();
////        }
////
////        /* 准备数据 */
////        List<InstCommon> fieldInsts = monitorService.allInstByField(fieldNum);
////        List<PointCommon> fieldPoints = monitorService.getPointsByFieldNum(fieldNum);
////        Map<String, List<PointCommon>> seaPointMap = toSeaPointMap(fieldPoints);
////        Map<Integer, PointCommon> pointMap = toPointMap(fieldPoints);
////
////        List<SeaFacilityCommon> fieldSeaFacilities = windService.allSeaFacility(fieldNum);
////        List<AttrCommon> fieldAttrs = monitorService.allAttrByField(fieldNum);
////
////        List<ReportPic> pics = new ArrayList<>();
////
////        List<ChartAttr> chartAttrs = chart.getChartAttrs();
////        int totalSize = chartAttrs.size() * set.size() * 2;
////        String dirName = chart.getId() + "-" + chart.getItemName() + "-" + chart.getAttrNames();
////        dirName = cleanSymbol(dirName);
////        for (String attrName : chartAttrs) {
////            List<PngInfo> pngInfos = new ArrayList<>();
////
////            attrProcessLine(fieldInsts, seaPointMap, pointMap, fieldSeaFacilities, fieldAttrs,
////                    chart.getStartTime(), chart.getEndTime(), pics, chart.getItemName(), attrName, null, set,
////                    chart.getPointNum(), totalSize, chart.getId(), wH, dirName, chart.getIsMark(), pngInfos);
////
////            Map<Integer, List<PngInfo>> pointPngInfo = pngInfos.stream().collect(groupingBy(PngInfo::getPointId));
////            int count = 0;
////            List<Series> series = new ArrayList<>();
////            for (Map.Entry<Integer, List<PngInfo>> entry : pointPngInfo.entrySet()) {
////                List<PngInfo> infos = entry.getValue();
////                for (PngInfo info : infos) {
////                    series.add(new Series(info.getPointNo() + "-" + info.getDirect(), info.getMonitorItem(), info.getData()));
////                }
////                count++;
////
////                if (count == chart.getPointNum()) {
////                    if (series.size() == 0) {
////                        continue;
////                    }
////
////                    pngData(null, series, chart.getItemName(), pics, null, attrName, millis, chart.getId(), wH, dirName, chart.getIsMark());
////                    updateMyProcess(chart.getId(), totalSize);
////                    series.clear();
////                    count = 0;
////                }
////            }
////            if (series.size() > 0) {
////                pngData(null, series, chart.getItemName(), pics, null, attrName, millis, chart.getId(), wH, dirName, chart.getIsMark());
////            }
////        }
////        curCount = 0;
////        try {
////            ZipUtil.zipDir("/data/highcharts-export/" + dirName, "/var/owp-upload-files/export");
////        } catch (Exception e) {
////            e.printStackTrace();
////            chartMapper.updateStatus(chart.getId(), "已完成", null);
////            return;
////        }
////        chartMapper.updateStatus(chart.getId(), "已完成", "owp-files/export/" + dirName + ".zip");
////    }
//
//    private List<Integer> getPointIds(Set<String> set) {
//        List<Integer> list = new ArrayList<>();
//        for (String s : set) {
//            list.add(Integer.parseInt(s.split("-")[0]));
//        }
//        return list;
//    }
//
//    /**
//     * 解析宽高
//     */
//    private double[] parseWH(String imgType) {
//        String[] arr = imgType.replace("cm", "").split("×");
//        if (arr.length == 2) {
//            return new double[] {Double.parseDouble(arr[0]) * 38, Double.parseDouble(arr[1]) * 38};
//        }
//        return new double[] {731, 216};
//    }
//
//    public String cleanSymbol(String oldString){
//        String regEx = "[\n`~!@#$%^&*()_+=|{}':;'\\[\\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。， 、？]";
//        String aa = "";
//        Pattern p = Pattern.compile(regEx);
//        Matcher m = p.matcher(oldString);
//        return m.replaceAll(aa).trim();
//    }
//
//
////    /**
////     * 生成图表
////     */
////    public void genCharts(ChartsQo qo) {
////        Set<String> set = new HashSet<>();
////        set.addAll(Arrays.asList(qo.getIds()));
////
////        /* 获取该风场名称、所有仪器、测点、海上设施、属性 */
////        Map<String, List<StatItem>> tiltStatItemsMap;
////        Map<String, List<StatItem>> shockStatItemsMap;
////        Map<String, List<StatItem>> shockBoosterStatItemsMap;
////        Map<String, List<StatItem>> stressStatItemsMap;
////        Map<String, List<StatItem>> corrodeStatItemsMap;
////        Map<String, List<StatItem>> subsideStatItemsMap;
////        Map<String, List<ReportPic>> picsMap;
////        Map<Integer, PointCommon> pointMap;
////        List<SeaFacilityCommon> fieldSeaFacilities;
////        List<SeaStatItem> seaTiltStatItems;
////        List<SeaStatItem> seaShockStatItems;
////        List<StatItem> tiltStatItems;
////        List<StatItem> shockStatItems;
////        List<StatItem> stressStatItems;
////        List<ReportPic> pics = new ArrayList<>();
////
////        try {
////            List<InstCommon> fieldInsts = monitorService.allInstByField(qo.getFieldNum());
////            List<PointCommon> fieldPoints = monitorService.getPointsByFieldNum(qo.getFieldNum());
////            Map<String, List<PointCommon>> seaPointMap = toSeaPointMap(fieldPoints);
////            pointMap = toPointMap(fieldPoints);
////            fieldSeaFacilities = windService.allSeaFacility(qo.getFieldNum());
////            List<AttrCommon> fieldAttrs = monitorService.allAttrByField(qo.getFieldNum());
////
////            /* 获取倾斜仪器ID */
////            tiltStatItems = getTiltInfo(fieldInsts, seaPointMap, pointMap, fieldSeaFacilities, fieldAttrs,
////                    qo.getStartTime(), qo.getEndTime(), pics, "倾斜", "倾角", null, set);
////
////            shockStatItems = getTiltInfo(fieldInsts, seaPointMap, pointMap, fieldSeaFacilities, fieldAttrs,
////                    qo.getStartTime(), qo.getEndTime(), pics, "振动", "有效值", "双向");
////
////            List<StatItem> shockBoosterStatItems = getTiltInfo(fieldInsts, seaPointMap, pointMap, fieldSeaFacilities, fieldAttrs,
////                    qo.getStartTime(), qo.getEndTime(), pics, "振动", "有效值", "三向");
////
////            stressStatItems = getTiltInfo(fieldInsts, seaPointMap, pointMap, fieldSeaFacilities, fieldAttrs,
////                    qo.getStartTime(), qo.getEndTime(), pics, "钢板应力", "应力", null);
////
////            List<StatItem> corrodeStatItems = getTiltInfo(fieldInsts, seaPointMap, pointMap, fieldSeaFacilities, fieldAttrs,
////                    qo.getStartTime(), qo.getEndTime(), pics, "腐蚀", "电位", null);
////
////            List<StatItem> subsideStatItems = getTiltInfo(fieldInsts, seaPointMap, pointMap, fieldSeaFacilities, fieldAttrs,
////                    qo.getStartTime(), qo.getEndTime(), pics, "静力水准", "相对沉降", null);
////
////            seaTiltStatItems = getSeaTiltInfo(fieldInsts, seaPointMap, pointMap, fieldSeaFacilities, fieldAttrs,
////                    qo.getStartTime(), qo.getEndTime(), pics, "倾斜", "倾角", null);
////
////            seaShockStatItems = getSeaTiltInfo(fieldInsts, seaPointMap, pointMap, fieldSeaFacilities, fieldAttrs,
////                    qo.getStartTime(), qo.getEndTime(), pics, "振动", "有效值", "三向");
////
////            picsMap = pics.stream().collect(groupingBy(ReportPic::getSeaFacilityId));
////        } catch (Exception e) {
////            e.printStackTrace();
////        }
////    }
//
//}
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
