package com.hdec.data.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hdec.common.domain.R;
import com.hdec.data.domain.OneTouchQo;
import com.hdec.data.domain.Outline;
import com.hdec.data.domain.OutlineNav;
import com.hdec.data.domain.ReportOutlineNav;
import com.hdec.data.domain.template.Mould;
import com.hdec.data.qo.MouldVal;
import com.hdec.data.qo.OutlineQo;
import com.hdec.data.service.ReportOutlineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Set;

/**
 * 报告大纲控制器
 *
 * <AUTHOR>
 */
@Api(tags = "报告大纲管理")
@Validated
@Slf4j
@RestController
@RequestMapping("api/data/outline")
public class ReportOutlineController {

    @Autowired
    private ReportOutlineService outlineService;

    /**
     * 大纲导航树
     */
    @ApiOperation("大纲导航树")
    @GetMapping("nav/{outlineId}")
    public R nav(@RequestHeader("fieldNum") String fieldNum, @PathVariable Integer outlineId) {
        List<ReportOutlineNav> navs = outlineService.nav(fieldNum, outlineId);
        return R.success(navs);
    }

    /**
     * 获取某大纲导航树告警情况
     */
    @ApiOperation("获取某大纲树告警情况")
    @GetMapping("nav/warn/{outlineId}")
    public R navWarn(@RequestHeader("fieldNum") String fieldNum, @PathVariable Integer outlineId) {
        Set<Integer> warnNavIds = outlineService.navWarn(fieldNum, outlineId);
        return R.success(warnNavIds);
    }

    /**
     * 新增大纲导航
     */
    @ApiOperation("新增大纲导航")
    @PostMapping("nav/add")
    public R navAdd(@RequestHeader("fieldNum") String fieldNum, @RequestBody OutlineNav nav) {
        return outlineService.navAdd(fieldNum, nav);
    }

    /**
     * 修改大纲导航
     */
    @ApiOperation("修改大纲导航")
    @PostMapping("nav/update")
    public R navUpdate(@RequestBody OutlineNav nav) {
        return outlineService.navUpdate(nav);
    }

    /**
     * 删除大纲导航
     */
    @ApiOperation("删除大纲导航")
    @DeleteMapping("nav/delete/{id}")
    public R navDelete(@PathVariable Integer id) {
        outlineService.navDelete(id);
        return R.success("删除成功");
    }

    /**
     * 一键生成大纲导航
     */
    @ApiOperation("一键生成大纲导航")
    @GetMapping("nav/oneTouch/{outlineId}")
    public R oneTouch(@RequestHeader("fieldNum") String fieldNum, @PathVariable Integer outlineId) {
        outlineService.oneTouch(fieldNum, outlineId);
        return R.success("生成成功");
    }

    /**
     * 一键生成大纲导航
     */
    @ApiOperation("一键生成大纲导航")
    @PostMapping("nav/oneTouch2")
    public R oneTouch2(@RequestHeader("fieldNum") String fieldNum, @RequestBody OneTouchQo qo) {
        outlineService.oneTouch2(fieldNum, qo.getOutlineId(), qo.getType(), qo.getVars());
        return R.success("生成成功");
    }

    /**
     * 大纲列表
     */
    @ApiOperation("大纲列表")
    @PostMapping("list")
    public R list(@RequestHeader("fieldNum") String fieldNum, @RequestBody OutlineQo qo) {
        PageHelper.startPage(qo.getPageNum(), qo.getPageSize());
        List<Outline> navs = outlineService.list(fieldNum, qo);
        return R.success(new PageInfo<>(navs));
    }

    /**
     * 新增大纲
     */
    @ApiOperation("新增大纲")
    @PostMapping("add")
    public R add(@RequestHeader("fieldNum") String fieldNum, @RequestBody Outline outline, HttpServletRequest request) {
        return outlineService.add(fieldNum, outline, request);
    }

    /**
     * 修改大纲
     */
    @ApiOperation("修改大纲")
    @PostMapping("update")
    public R update(@RequestBody Outline outline) {
        return outlineService.update(outline);
    }

    /**
     * 批量删除大纲
     */
    @ApiOperation("批量删除大纲")
    @DeleteMapping("{ids}")
    public R deleteBatch(@RequestHeader("fieldNum") String fieldNum, @PathVariable Integer[] ids) {
        return outlineService.delete(fieldNum, ids);
    }

    /**
     * 模板绑定到导航
     */
    @ApiOperation("模板绑定到导航")
    @GetMapping("addMould2Nav/{navId}/{mouldId}")
    public R addMould2Nav(@PathVariable Integer navId, @PathVariable Integer mouldId) {
        return outlineService.addMould2Nav(navId, mouldId);
    }

    /**
     * 模板移除大纲
     */
    @ApiOperation("模板移除大纲")
    @DeleteMapping("removeMould2Nav/{navId}/{mouldId}")
    public R removeMould2Nav(@PathVariable Integer navId, @PathVariable Integer mouldId) {
        outlineService.removeMould2Nav(navId, mouldId);
        return R.success("移除成功");
    }

    /**
     * 获取导航下所有模板
     */
    @ApiOperation("获取导航下所有模板")
    @GetMapping("getMouldByNav/{navId}")
    public R getMouldByNav(@RequestHeader("fieldNum") String fieldNum, @PathVariable Integer navId) {
        List<Mould> moulds = outlineService.getMouldByNav(fieldNum, navId);
        return R.success(moulds);
    }

    /**
     * 为大纲内的模板设置值
     */
    @ApiOperation("为大纲内的模板设置值")
    @PostMapping("setMouldValue")
    public R setMouldValue(@RequestHeader("fieldNum") String fieldNum, @RequestBody MouldVal mouldVal) {
        outlineService.setMouldValue(fieldNum, mouldVal);
        return R.success("设置成功");
    }

    /**
     * 大纲生成PDF
     */
    @ApiOperation("大纲生成PDF")
    @GetMapping("genPdf/{outlineId}")
    public void genPdf(@RequestHeader("fieldNum") String fieldNum, @PathVariable Integer outlineId, HttpServletResponse response) throws Exception {
        outlineService.genPdf(fieldNum, outlineId, response);
    }

    /**
     * 风场下所有大纲
     */
    @ApiOperation("风场下所有大纲")
    @PostMapping("listByField/{fieldNum}")
    public R list(@PathVariable("fieldNum") String fieldNum) {
        List<Outline> outlines = outlineService.listByField(fieldNum);
        return R.success(outlines);
    }

    /**
     * 导入大纲导航
     */
    @ApiOperation("导入大纲导航")
    @GetMapping("importOutlineNav/{curOutlineId}/{selectedOutlineId}")
    public R importOutlineNav(@RequestHeader("fieldNum") String fieldNum,
                              @PathVariable Integer curOutlineId, @PathVariable Integer selectedOutlineId) {
        return outlineService.importOutlineNav(fieldNum, curOutlineId, selectedOutlineId);
    }

    /**
     * 复制大纲
     */
    @ApiOperation("复制大纲")
    @PostMapping("copy")
    public R copy(@RequestHeader("fieldNum") String fieldNum,
                  @RequestHeader("sessionId") String sessionId,
                  @RequestBody Outline outline) {
        try {
            outlineService.copy(fieldNum, outline, sessionId);
            return R.success("复制成功");
        } catch (Exception e) {
            return R.error(e.getMessage());
        }
    }

}
