package com.hdec.data.conector;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;
import java.util.Map;

@Data
@ConfigurationProperties(prefix = "connector.reference-electrode", ignoreUnknownFields = true)
public class ReferenceElectrodeProperties {

    /**
     * 是否开启
     */
    private boolean enable = false;

    /**
     * 任务调度表达式
     */
    private String cron;

    /**
     * 采集仪配置
     * key:采集仪编码
     * value:ModBus配置
     */
    private Map<String, ModbusProperties> device;

    @Data
    public static class ModbusProperties {

        /**
         * HOST
         */
        private String host;

        /**
         * 端口
         */
        private Integer port = 502;

        /**
         * 通道地址
         */
        Map<Integer, Integer> channels;
    }
}
