package com.hdec.data.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/7/26
 */
@Data
@ToString
@NoArgsConstructor
public class ContentVo {

    /** 标题名称 */
    private String name;

    /** 标题等级 */
    private Integer level;

    private String seqNum;

    /** 标题内容 */
    private String content;

    private String seaName;

    public ContentVo(String name, Integer level, String seqNum, String content) {
        this.name = name;
        this.level = level;
        this.seqNum = seqNum;
        this.content = content;
    }

    public ContentVo(String name, Integer level, String seqNum, String content, String seaName) {
        this.name = name;
        this.level = level;
        this.seqNum = seqNum;
        this.content = content;
        this.seaName = seaName;
    }
}
