package com.hdec.data.util;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class RateLimitedQueue<T> {
    private final Queue<T> queue; // 内部使用的队列
    private final int maxSize; // 队列的最大容量
    private final long maxInsertIntervalMs; // 每次插入的最小间隔时间（毫秒）
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    private volatile boolean running = true;

    public RateLimitedQueue(int maxSize, long maxInsertIntervalMs) {
        if (maxSize <= 0 || maxInsertIntervalMs <= 0) {
            throw new IllegalArgumentException("maxSize and maxInsertIntervalMs must be positive");
        }
        this.queue = new ConcurrentLinkedQueue<>(); // 使用非阻塞队列
        this.maxSize = maxSize;
        this.maxInsertIntervalMs = maxInsertIntervalMs;

        // 启动后台任务，控制数据插入速率
        startRateLimiter();
    }

    /**
     * 尝试向队列中添加数据。如果队列已满，则直接丢弃数据并返回false。
     *
     * @param item 要添加的数据项
     * @return 如果成功添加到缓冲区，则返回true；否则返回false。
     */
    public boolean offer(T item) {
        if (queue.size() < maxSize) {
            return queue.offer(item); // 不阻塞地尝试添加
        } else {
            return false; // 队列已满，立即返回
        }
    }

    /**
     * 返回队列中的所有数据作为一个不可变的List。
     *
     * @return 包含队列中所有数据的List
     */
    public List<T> getAsList() {
        synchronized (queue) {
            return Collections.unmodifiableList(new ArrayList<>(queue));
        }
    }

    /**
     * 关闭队列并停止后台任务。
     */
    public void shutdown() {
        running = false;
        scheduler.shutdown();
    }

    private void startRateLimiter() {
        scheduler.scheduleAtFixedRate(() -> {
            if (!running) {
                return;
            }
            while (!queue.isEmpty()) {
                T item = queue.poll();
                if (item == null) break; // 队列为空时退出
                processItem(item); // 处理队列中的数据
            }
        }, 0, maxInsertIntervalMs, TimeUnit.MILLISECONDS);
    }

    /**
     * 处理从队列中取出的每个元素。可以根据需求重写此方法。
     *
     * @param item 队列中的元素
     */
    protected void processItem(T item) {
        System.out.println("Processing item: " + item);
    }

}