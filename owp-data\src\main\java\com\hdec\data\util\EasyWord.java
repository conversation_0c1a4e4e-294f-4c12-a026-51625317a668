package com.hdec.data.util;

import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.policy.AbstractRenderPolicy;
import com.deepoove.poi.render.RenderContext;
import com.hdec.common.util.CommonUtil;
import com.hdec.common.util.ReportUtil;
import com.hdec.common.util.TimeUtil;
import com.hdec.data.domain.ContentVo;
import com.hdec.data.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.ddr.poi.html.ElementRenderer;
import org.ddr.poi.html.HtmlRenderConfig;
import org.ddr.poi.html.HtmlRenderPolicy;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTFonts;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTSimpleField;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STOnOff;
import org.springframework.util.ObjectUtils;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * @date 2023/8/1
 */
@Slf4j
public class EasyWord {

    private static Map<String, Integer> map = new HashMap<>();

    /**
     * HTML转Word(普通)
     */
    public static void html2NormalWord(String content, String keyPath, String writePath) {
        content = removeSpecialChar(content);
        content = addGlobalStyle(content, "仿宋_GB2312", "12pt", "1.5");
        content = addNewRoman(content);

        try {
            HtmlRenderConfig renderConfig = new HtmlRenderConfig();
            renderConfig.setShowDefaultTableBorderInTableCell(true);
            renderConfig.setCustomRenderers(
                    new ArrayList<ElementRenderer>() {{
                        add(new TopicRender());
                        add(new PicRender());
                    }}
            );

            HtmlRenderPolicy htmlRenderPolicy = new HtmlRenderPolicy(renderConfig);
            Configure configure = Configure.builder()
                    .bind("key", htmlRenderPolicy)
                    .build();

            XWPFTemplate template = XWPFTemplate.compile(keyPath, configure);
            String finalContent = content;
            template.render(
                    new HashMap<String, Object>() {{
                        put("key", finalContent);
                    }}
            );
            template.writeToFile(writePath);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 去掉特殊符号
     */
    private static String removeSpecialChar(String content) {
        if (ObjectUtils.isEmpty(content)) {
            return content;
        }

        // 去掉零宽字符
        content = CommonUtil.removeZeroWidthChar(content);

        // 去掉特殊字体两边的引号
        content = content.replace("'Times New Roman'", "Times New Roman");
        content = content.replace("Times New Roman, serif", "Times New Roman");

        // div标签转span
        org.jsoup.nodes.Document document = Jsoup.parse(content);
        document.select("div").tagName("span");
        return document.body().html();
    }

    /**
     * 添加全局样式
     */
    private static String addGlobalStyle(String content, String fontFamily, String fontSize) {
        if (ObjectUtils.isEmpty(content)) {
            return content;
        }

        return addGlobalStyle(content, fontFamily, fontSize, "1.5");
    }

    /**
     * 添加全局样式
     */
    private static String addGlobalStyle(String content, String fontFamily, String fontSize, String lineHeight) {
        if (ObjectUtils.isEmpty(content)) {
            return content;
        }

        String globalStyle = "font-family: " + fontFamily + "; font-size: " + fontSize + "; line-height: " + lineHeight + ";";
        return "<span style=\"" + globalStyle + "\">" + content + "</span>";
    }

    public static void html2word(String coverContent, List<ContentVo> navContents, String keyPath, String writePath, String fieldName, Date headerTime) {
        coverContent = removeSpecialChar(coverContent);
        coverContent = addGlobalStyle(coverContent, "仿宋_GB2312", "12pt", "1.5");
        coverContent = addNewRoman(coverContent);

        StringBuilder contents = new StringBuilder();
        for (ContentVo navContent : navContents) {
            setTitle(contents, navContent.getLevel(), navContent.getSeqNum(), navContent.getName());
            replaceSeqNum(navContent, map);
            map.clear();

            String content = navContent.getContent().replace("\n", "");

            Document document = Jsoup.parse(content);
            Elements multi = document.getElementsByAttributeValue("data-type", "multi");
            for (Element element : multi) {
                if ("@{风机编号}".equals(element.text())) {
                    if (!ObjectUtils.isEmpty(navContent.getSeaName())) {
                        element.text(navContent.getSeaName());
                    } else {
                        element.html("<span style=\"color:red;\">@{风机编号}</span>");
                    }
                }
            }

            contents.append(document.body().html());
        }

        String content = replaceRefChartNum(contents.toString());

        content = removeSpecialChar(content);

        content = mergerRefNum(content);
        content = addNewRoman(content);
        content = addVertical(content);

        Map<String, Object> baseStyles = new HashMap<>();
        baseStyles.put("font-family", "宋体");
        baseStyles.put("font-size", "12pt");
        baseStyles.put("line-height", 1.5);
        /* 添加默认两端对齐属性（如果设置非两端对齐 combineStyle() 中existStyles会覆盖该处newStyles） */
        baseStyles.put("text-align", "justify");
        Document document = Jsoup.parse(content);
        Html2wordService.addStyleIfNotExist(document, "p", baseStyles);
        String lastContent = document.body().html();

        System.out.println("------------cover--------");
        System.out.println(coverContent);
        System.out.println("------------cover--------");

        System.out.println("------------content--------");
        System.out.println(lastContent);
        System.out.println("------------content--------");

        try {
            HtmlRenderConfig renderConfig = new HtmlRenderConfig();
            renderConfig.setShowDefaultTableBorderInTableCell(true);

            ArrayList<String> pic_strings = new ArrayList<>();
            ArrayList<String> table_strings=new ArrayList<>();
            AnnotationRender annotationRender = new AnnotationRender(pic_strings,table_strings);

            renderConfig.setCustomRenderers(
                    new ArrayList<ElementRenderer>() {{
                        add(new TopicRender());
                        add(new PicRender());
                        add(new PagingRender());
                        add(new TheTableRender());
//                        add(annotationRender);
                    }}
            );
            HtmlRenderPolicy htmlRenderPolicy = new HtmlRenderPolicy(renderConfig);
            Configure configure = Configure.builder()
                    .bind("cover", htmlRenderPolicy)
                    .bind("content", htmlRenderPolicy)
//                    .bind("pagestart", new HtmlRenderPolicy(new HtmlRenderConfig() {{
//                        setCustomRenderers(
//                                Arrays.asList(new StartPageRender())
//                        );
//                    }}))//分页开始符绑定
                    .build();

            XWPFTemplate xwpfTemplate = XWPFTemplate.compile(keyPath, configure);
            String finalCoverContent = coverContent;
            String finalLastContent = lastContent;
            xwpfTemplate.render(
                    new HashMap<String, Object>() {{
                        put("cover", finalCoverContent);
                        put("content", finalLastContent);
                        put("leftheader", fieldName);
                        put("rightheader", "安全监测月报（" + TimeUtil.format2Month(headerTime) + "）");
//                        put("pagestart", "<pagestart/>");/* 将 {{pagestart}} --> <pagestart/> 用于后续html识别 */
                    }}
            );
            xwpfTemplate.writeToFile(writePath);

            String outputPath = new File(writePath).getParent() + "/temp.docx";
            String cmd = "unoconv -f docx -o " + outputPath + " " + writePath;
            System.out.println(cmd);
            executeCMD(cmd);

            new File(outputPath).renameTo(new File(writePath));

            insertFontInWord(writePath);
//            createAnnotation(writePath, pic_strings, table_strings);
            log.info("保存完成...");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 向word中提供兼容性字体--原理是使用xml进行操作
     */
    public static void insertFontInWord(String path) throws IOException {

        File file=new File(path);//获取word原始文件
        File toRenamefile = new File(path.replace(".docx","1.zip"));//word原始文件转zip
        file.renameTo(toRenamefile);
        File targetFile = new File(path.replace(".docx",".zip"));//修改后的zip
        File targetFileDocx = new File(path);//修改后的zip转word
        ZipInputStream zis = new ZipInputStream(new FileInputStream(toRenamefile));//需要修改的文件
        ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(targetFile));//需要写入的文件

        //获取zip内的文件
        ZipEntry entry;
        while ((entry = zis.getNextEntry()) != null) {
            //输出流
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int bytesRead;
            while((bytesRead = zis.read(buffer)) != -1) {
                bos.write(buffer, 0, bytesRead);
            }
            if (entry.getName().equals("word/fontTable.xml")) {//判断文件类型
                String fileContent = bos.toString(StandardCharsets.UTF_8.toString());//转换成UTF-8 -- 只有这个需要转其他都不转
                String pattern = "<w:font w:name=\"仿宋_gb2312\">.*?</w:font>";
                Pattern r = Pattern.compile(pattern);
                Matcher m = r.matcher(fileContent);
                String target="";
                if (m.find()) {
                    target=m.group();
                    fileContent = fileContent.replace(target
                            ,"<w:font w:name=\"仿宋_gb2312\">\n" +
                                    "          <w:altName w:val=\"仿宋\" />\n" +
                                    "          <w:panose1 w:val=\"02010609060101010101\" />\n" +
                                    "          <w:charset w:val=\"86\" />\n" +
                                    "          <w:family w:val=\"auto\" />\n" +
                                    "          <w:pitch w:val=\"default\" />\n" +
                                    "        </w:font>");
                }else{
                    fileContent = fileContent.replace("</w:fonts>"
                            ,"<w:font w:name=\"仿宋_gb2312\">\n" +
                                    "          <w:altName w:val=\"仿宋\" />\n" +
                                    "          <w:panose1 w:val=\"02010609060101010101\" />\n" +
                                    "          <w:charset w:val=\"86\" />\n" +
                                    "          <w:family w:val=\"auto\" />\n" +
                                    "          <w:pitch w:val=\"default\" />\n" +
                                    "        </w:font></w:fonts>");
                }
                ZipEntry newEntry = new ZipEntry(entry.getName());
                zos.putNextEntry(newEntry);
                zos.write(fileContent.getBytes(StandardCharsets.UTF_8));
                zos.closeEntry();
            }else{
                ZipEntry newEntry = new ZipEntry(entry.getName());
                zos.putNextEntry(newEntry);
                zos.write(bos.toByteArray());
                zos.closeEntry();
            }
        }
        //关闭流
        zos.close();
        zis.close();
        targetFile.renameTo(targetFileDocx);//zip转docx
        toRenamefile.delete(); //删除原始zip文件
    }


    /** 生成题注 */
    public static void createAnnotation(String path,ArrayList<String> pics,ArrayList<String> tabs) {
        try {
            System.out.println("1");
            //区块对渲染插件
            Configure config = Configure.builder().bind("tabAnnotation",new AbstractRenderPolicy<String>() {
                @Override
                public void doRender(RenderContext<String> renderContext) throws Exception {
                    //获取题注的内容
                    String nowString = tabs.remove(0);
                    //按照 “:” 进行拆分
                    String[] splitS = nowString.split(":");
                    //题注内容
                    String[] split = splitS[0].split("-");
                    XWPFParagraph paragraph =  (XWPFParagraph)renderContext.getRun().getParent();
                    if(split.length==2){
                        addText(paragraph,"表");
                        addField(paragraph,"STYLEREF 1 \\s",split[0]);
                        addText(paragraph,"-");
                        addField(paragraph," SEQ 表 \\* ARABIC  \\s 1 ",split[1]);
                        if(splitS.length>1){
                            String[] subArray = Arrays.copyOfRange(splitS, 1, splitS.length);
                            StringBuilder content= new StringBuilder();
                            for (int i = 0; i < subArray.length; i++) {
                                content.append(subArray[i]);
                            }
                            addText(paragraph, content.toString());
                        }
                    }else{
                        System.out.println("格式错误");
                    }


                }
                @Override// 清空标签
                protected void afterRender(RenderContext<String> context) {
                    clearPlaceholder(context, true);
                }
            }).bind("picAnnotation", new AbstractRenderPolicy<String>() {
                @Override
                public void doRender(RenderContext<String> renderContext) throws Exception {
                    //获取题注的内容
                    String nowString = pics.remove(0);
                    //按照 “:” 进行拆分
                    String[] splitS = nowString.split(":");
                    //题注内容
                    String[] split = splitS[0].split("-");
                    XWPFParagraph paragraph =  (XWPFParagraph)renderContext.getRun().getParent();//获取到段落
                    if(split.length==2){

                        addText(paragraph,"图");
                        addField(paragraph,"STYLEREF 1 \\s",split[0]);
                        addText(paragraph,"-");
                        addField(paragraph," SEQ 图 \\* ARABIC  \\s 1 ",split[1]);
                        if(splitS.length>1){
                            String[] subArray = Arrays.copyOfRange(splitS, 1, splitS.length);
                            StringBuilder content= new StringBuilder();
                            for (int i = 0; i < subArray.length; i++) {
                                content.append(subArray[i]);
                            }
                            addText(paragraph, content.toString());
                        }
                    }else{
                        System.out.println("格式错误");
                    }
                }
                @Override// 清空标签
                protected void afterRender(RenderContext<String> context) {
                    clearPlaceholder(context, true);
                }
            }).build();
            XWPFTemplate compile = XWPFTemplate.compile(path,config);
            compile.render(
                    new LinkedHashMap<String, Object>() {{
                    }}
            );
            compile.writeToFile(path);

        }catch (Exception err){
            System.err.println("题注生成失败");
        }
    }

    private static void printArr(String[] splitS) {
        for (String split : splitS) {
            System.out.println(split);
        }
        System.out.println();
    }

    /** 向断落添加域代码 */
    private static void addField(XWPFParagraph paragraph, String fieldName,String Placeholder) {
        CTSimpleField ctSimpleField = paragraph.getCTP().addNewFldSimple();
        ctSimpleField.setInstr(fieldName);
        ctSimpleField.setDirty(STOnOff.FALSE);//设置是否打开时更新
        ctSimpleField.addNewR().addNewT().setStringValue(Placeholder);
    }
    /** 向段落增加文字 */
    public  static  void  addText(XWPFParagraph paragraph, String text){
        XWPFRun run = paragraph.createRun();
        CTFonts ctFonts = run.getCTR().addNewRPr().addNewRFonts();
        ctFonts.setEastAsia("SimHei");
        ctFonts.setAscii("Times New Roman");
        run.setText(text);
    }

    public static void executeCMD(String cmd) {
        Process process = null;
        int exitVal = 0;
        try {
            process = Runtime.getRuntime().exec(cmd);
            exitVal = process.waitFor();
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }

        if (exitVal != 0) {
            throw new RuntimeException("cmd任务执行失败:\n" + cmd);
        }
    }

    private static String addNewRoman(String content) {
//        Document document = Jsoup.parse(content);
//        Elements elements = document.getAllElements();
//
//        for (Element element : elements) {
//            if (ObjectUtils.isEmpty(element.children())) {
//                element.html(ReportUtil.addNewRome2EngNumber(element.html()));
//            }
//        }
//        return document.body().html();
        return content;
    }

    private static String addVertical(String content) {
        Document document = Jsoup.parse(content);
        Elements elements = document.getElementsByAttributeValue("valign", "center");
        for (Element element : elements) {
            String style = element.attr("style");
            style = style.replace("padding: 0pt", "padding: 3pt");
            element.attr("style", style + " vertical-align: middle;");
        }
        return document.body().html();
    }

    /**
     * 替换引用图表编号
     */
    private static String replaceRefChartNum(String contents) {
        org.jsoup.nodes.Document staticDocument = Jsoup.parse(contents);
        org.jsoup.nodes.Document document = Jsoup.parse(contents);
        List<Element> refNumElements = JsoupUtil.getElementsByAttr(document, "data-type", "refNum");
        for (Element refNumElement : refNumElements) {
            String dataRef = refNumElement.attr("data-ref");
            if (ObjectUtils.isEmpty(dataRef)) {
                refNumElement.html("<span style=\"color:red;\">" + refNumElement.html() + "</span>");
                continue;
            }

            int index = 0;
            String dataRefIndex = refNumElement.attr("data-ref-index");
            try {
                if (!dataRefIndex.isEmpty()) {
                    index = Integer.parseInt(dataRefIndex);
                }
            } catch (Exception e) {
                log.error("获取引用【{}】中大纲索引位置【{}】异常:{}", dataRef, dataRefIndex, e.getMessage());
            }

            String chartNum = findByDataRef(staticDocument, dataRef, index);
            System.out.println(dataRef + "和" + index + "找到了" + chartNum);
            if (ObjectUtils.isEmpty(chartNum)) {
                refNumElement.html("<span style=\"color:red;\">" + refNumElement.html() + "</span>");
                continue;
            }
            refNumElement.text(chartNum);
        }
        return document.body().html();
    }

    private static String findByDataRef(org.jsoup.nodes.Document staticDocument, String dataRef, int index) {
        Elements spanElements = staticDocument.getElementsByTag("span");
        if (ObjectUtils.isEmpty(spanElements)) {
            return null;
        }
        List<String> dataList = new ArrayList<>();
        for (Element spanElement : spanElements) {
            String id = spanElement.attr("id");
            if (ObjectUtils.isEmpty(id)) {
                continue;
            }

            if (id.equals(dataRef)) {
                dataList.add(spanElement.text());
            }
        }
        return dataList.isEmpty() ? null : dataList.size() > index ? dataList.get(index) : dataList.get(0);
//        if (dataList.isEmpty()) {
//            return null;
//        }
//        if (dataList.size() == 1) {
//            return dataList.get(0);
//        }
    }

    private static void replaceSeqNum(ContentVo navContent, Map<String, Integer> map) {
        org.jsoup.nodes.Document document = Jsoup.parse(navContent.getContent());
        Elements spanElements = document.getElementsByAttribute("data-type");
        for (Element spanElement : spanElements) {
            if (!"chartNum".equals(spanElement.attr("data-type"))) {
                continue;
            }
            String spanText = spanElement.text();
            Pattern p = Pattern.compile("@\\{图编号}|@\\{表编号}");
            Matcher m = p.matcher(spanText);
            StringBuffer res = new StringBuffer();
            while (m.find()) {
                String matchedStr = m.group();
                String replaceText;
                if ("@{图编号}".equals(matchedStr)) {
                    // 图编号
                    Integer num = map.get("chart-" + navContent.getSeqNum());
                    if (num == null) {
                        replaceText = ReportUtil.addNewRome2EngNumber("图" + navContent.getSeqNum() + "-1");
//                        replaceText = "<annotation type=\"image\">"+navContent.getSeqNum()+"-1:</annotation>";
                        map.put("chart-" + navContent.getSeqNum(), 2);
                    } else {
                        replaceText = ReportUtil.addNewRome2EngNumber("图" + navContent.getSeqNum() + "-" + num);
//                        replaceText = "<annotation type=\"image\">"+navContent.getSeqNum()+"-"+num+":</annotation>";
                        map.put("chart-" + navContent.getSeqNum(), num + 1);
                    }
                } else {
                    // 表编号
                    Integer num = map.get("table-" + navContent.getSeqNum());
                    if (num == null) {
                        replaceText = ReportUtil.addNewRome2EngNumber("表" + navContent.getSeqNum() + "-1");
//                        replaceText = "<annotation type=\"table\">"+navContent.getSeqNum()+"-1:</annotation>";
                        map.put("table-" + navContent.getSeqNum(), 2);
                    } else {
                        replaceText = ReportUtil.addNewRome2EngNumber("表" + navContent.getSeqNum() + "-" + num);
//                        replaceText = "<annotation type=\"table\">"+navContent.getSeqNum()+"-"+num+":</annotation>";
                        map.put("table-" + navContent.getSeqNum(), num + 1);
                    }
                }
                m.appendReplacement(res, replaceText);
            }
            m.appendTail(res);
            if (!ObjectUtils.isEmpty(res.toString())) {
                spanElement.html(res.toString());
            }
        }
        navContent.setContent(document.body().html());
    }

    private static void setTitle(StringBuilder sb, Integer level, String seqNum, String name) {
//        if (level <= 2) {
            sb.append("<h" + level + ">" + seqNum + " " + name + "</h" + level + ">");
//        }
    }

    /**
     * 合并图表编号
     */
    private static String mergerRefNum(String content) {
        org.jsoup.nodes.Document document = Jsoup.parse(content);
        String htmlStr = document.html();
        Elements ps = document.getElementsByTag("p");
        for (Element p : ps) {
            List<Element> refNumElements = p.getElementsByAttributeValue("data-type", "refNum");
            if (ObjectUtils.isEmpty(refNumElements)) {
                continue;
            }

            tryMergerRefNum(refNumElements, htmlStr);
        }
        return document.body().html();
    }

    /**
     * 尝试合并图表编号
     */
    private static void tryMergerRefNum(List<Element> refNumElements, String htmlStr) {
        if (refNumElements.size() <= 1) {
            // 无需合并
            return;
        }
        Element start = refNumElements.get(0);
        Element pre = refNumElements.get(0);
        boolean flag = false;
        for (int i = 1; i < refNumElements.size(); i++) {
            Element cur = refNumElements.get(i);
            boolean canMerge = false;
            try {
                canMerge = tryMerge(pre, cur, htmlStr);
            } catch (Exception e) {
            }
            if (canMerge) {
                pre = cur;
                flag = true;
            } else {
                if (flag) {
                    String mergeText = getMergeText(start, pre);

                    /* 文本放于start，后面的span删除 */
                    start.text(mergeText);
                    String parentHtml = start.parent().html();
                    String replaceStr = parentHtml.substring(parentHtml.indexOf(start.toString()) + start.toString().length(), parentHtml.indexOf(pre.toString()) + pre.toString().length());
                    start.parent().html(parentHtml.replace(replaceStr, ""));
                    flag = false;
                }
                pre = cur;
                start = cur;
            }
        }
        if (flag) {
            String mergeText = getMergeText(start, pre);
            start.text(mergeText);
            String parentHtml = start.parent().html();
            String replaceStr = parentHtml.substring(parentHtml.indexOf(start.toString()) + start.toString().length(), parentHtml.indexOf(pre.toString()) + pre.toString().length());
            start.parent().html(parentHtml.replace(replaceStr, ""));
        }
    }

    /**
     * 获取合并后图表编号
     */
    private static String getMergeText(Element pre, Element cur) {
        String[] curArr = cur.text().split("-");

        StringBuilder sb = new StringBuilder(pre.text());
        sb.append("~");
        sb.append(curArr[1]);
        return sb.toString();
    }

    /**
     * 尝试合并两个图表编号
     *
     * @param pre 图1-1
     * @param cur 图1-2
     */
    private static boolean tryMerge(Element pre, Element cur, String htmlStr) {
        /* 两节点中间字符数需小于等于1个 */
        int i = htmlStr.indexOf(pre.toString()) + pre.toString().length();
        int j = htmlStr.indexOf(cur.toString());
        if (i < 0 || j < 0) {
            return false;
        }
        if (htmlStr.substring(i, j).trim().length() > 1) {
            return false;
        }

        String[] preArr = pre.text().split("-");
        String[] curArr = cur.text().split("-");

        /* 长度不一致无法合并 */
        if (preArr.length != 2 && curArr.length != 2) {
            return false;
        }

        /* 开头不一致无法合并 */
        if (!preArr[0].equals(curArr[0])) {
            return false;
        }

        /* 序号不连续不一致无法合并 */
        try {
            int preNum = Integer.parseInt(preArr[1]);
            int curNum = Integer.parseInt(curArr[1]);
            return preNum + 1 == curNum;
        } catch (Exception e) {
            return false;
        }
    }

}
