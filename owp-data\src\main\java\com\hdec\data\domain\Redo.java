package com.hdec.data.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * Redo实体类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class Redo {

    /** 自增主键 */
    private Integer id;

    /** 哪一天 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone="GMT+8")
    private Date day;

    /** 测点ID */
    private Integer pointId;

    /** 频率 */
    private String rate;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createTime;

    /** 风场编码 */
    private String fieldNum;

    /** 是否在执行中（0：未在执行中  1：在执行中） */
    private Boolean exeFlag = false;

    public Redo(Date day, Integer pointId, String rate, String fieldNum) {
        this.day = day;
        this.pointId = pointId;
        this.rate = rate;
        this.fieldNum = fieldNum;
    }

}
