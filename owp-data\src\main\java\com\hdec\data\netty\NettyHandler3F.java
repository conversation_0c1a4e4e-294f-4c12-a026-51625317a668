package com.hdec.data.netty;

import com.hdec.common.domain.msg.Inst;
import com.hdec.common.domain.msg.Message;
import com.hdec.common.util.TimeUtil;
import com.hdec.data.cache.Cache;
import com.hdec.data.service.DataAccessService;
import com.hdec.data.service.KafkaService;
import com.hdec.data.util.HexUtil;
import com.hdec.data.util.Utils;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

/**
 * 3F客户端消息处理类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class NettyHandler3F extends ChannelInboundHandlerAdapter {

    private static NettyHandler3F handler;

    /** 消息开始 */
    private final static String MSG_START = "2F2F2F555555555555";

    /** 数据结尾 */
    private final static String DATA_END = "BBBBBBBB";

    @Resource
    private KafkaService kafkaService;

    @Resource
    private DataAccessService accessService;

    @PostConstruct
    public void init() {
        handler = this;
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) {
        /* 读取十六进制消息 */
        byte[] bytes = Utils.readFromByteBuf((ByteBuf) msg);
        String hex = HexUtil.bytesToHex(bytes);

        /* 处理设备编号 */
        String channelId = ctx.channel().id().asLongText();
        if (!Cache.channelIdDeviceNoMap.containsKey(channelId)) {
            handleDeviceNo(hex, channelId);
            return;
        }

        /* 解析数据 */
        List<Inst> instList = null;
        try {
            instList = parseMessage(hex, channelId);
        } catch (Exception e) {
            log.error("报文{}解析出错：", hex, e);
        }
        if (ObjectUtils.isEmpty(instList)) {
            return;
        }

        /* 处理数据 */
        log.info("收到数据，大小：{}", instList.size());
        instList.sort(Comparator.comparing(Inst::getTime));
        handler.accessService.input(instList,"dynamic");
    }

    /**
     * 处理设备编号
     */
    private void handleDeviceNo(String hex, String channelId) {
        int index = hex.indexOf(MSG_START);
        if (index == -1) {
            return;
        }

        /* 获取设备编号，并去掉其中的空格、换行符 */
        String deviceNo = new String(HexUtil.hexToBytes(hex.substring(0, index - 2), true));
        deviceNo = deviceNo.replace(" ", "").replaceAll("\\r\\n|\\r|\\n", "");
        Cache.channelIdDeviceNoMap.put(channelId, deviceNo);
    }

    /**
     * 解析报文
     */
    public static List<Inst> parseMessage(String hex, String channelId) {
        String deviceNo = Cache.channelIdDeviceNoMap.get(channelId);
        if (ObjectUtils.isEmpty(deviceNo)) {
            return Collections.emptyList();
        }

        /* 解析报文 */
        int byteLen = hex.length() / 2;
        Message message = new Message();

        /* 通道数、采样率 */
        message.setChannel(HexUtil.hex2SignInt(hex, 0, 1));
        message.setSamplingRate(HexUtil.hex2SignInt(hex, 10, 12));

        /* 电压值 */
        List<Double> voltages = new ArrayList<>();
        String dataHex = getDataHex(hex);
        for (int i = 0; i < dataHex.length(); i += 6) {
            voltages.add(HexUtil.hex2SignInt3(dataHex.substring(i, i + 6)) / 8388608.0 * 10);
        }
        message.setVoltages(voltages);

        /* 时间 */
        message.setTime(hex.substring(hex.length() - 17 * 2, hex.length() - 10 * 2));
        message.setMilli(HexUtil.hex2SignInt(hex, byteLen - 8, byteLen - 6));

        /* 封装 */
        List<Inst> instList = new ArrayList<>();

        // 获取截止时间和步长
        long lastMilli = getLastMilli(message.getTime(), message.getMilli());
        long stepMilli = 1000 / (message.getSamplingRate() + 1);

        int groups = voltages.size() / message.getChannel();
        for (int i = groups - 1; i >= 0; i--) {
            /* 设备编号、时间 */
            Inst inst = new Inst(deviceNo, new Date(lastMilli));

            /* 通道数据 */
            Map<Integer, Float[]> channelData = new HashMap<>(message.getChannel());
            for (int j = 0; j < message.getChannel(); j++) {
                float value = Float.parseFloat(String.valueOf(voltages.get(i * message.getChannel() + j)));
                channelData.put(j, new Float[]{value});
            }
            inst.setChannelData(channelData);

            lastMilli -= stepMilli;
            instList.add(inst);
        }
        return instList;
    }

    /**
     * 获取数据部分报文
     */
    private static String getDataHex(String hex) {
        StringBuilder sb = new StringBuilder();
        String[] arr = hex.split(MSG_START);
        for (String s : arr) {
            if (s.length() > 20) {
                String sub = s.substring(12);
                sb.append(sub, 0, sub.lastIndexOf(DATA_END));
            }
        }
        return sb.toString();
    }

    /**
     * 由时间和毫秒获取截止时间
     */
    private static long getLastMilli(String time, Integer milli) {
        return TimeUtil.parse2Second2(time).getTime() + milli;
    }

//    /**
//     * 解析报文
//     *
//     * @param hex 报文
//     */
//    public static Message oldParseMessage(String hex) {
//        int byteLen = hex.length() / 2;
//        Message message = new Message();
//
//        /* 通道数、采样率、包序号 */
//        message.setChannel(HexUtil.hex2SignInt(hex, 0, 1));
//        message.setSamplingRate(HexUtil.hex2SignInt(hex, 10, 12));
//        message.setNumber(HexUtil.hex2SignInt(hex, 12, 16));
//
//        /* 电压值 */
//        List<Double> voltages = new ArrayList<>();
//        for (int i = 32; i < hex.lastIndexOf("BBBBBBBB"); i += 6) {
//            voltages.add(HexUtil.hex2SignInt3(hex.substring(i, i + 6)) / 8388608.0 * 10);
//        }
//        message.setVoltages(voltages);
//
//        /* 经纬度 */
//        message.setLng(HexUtil.hex2SignLong(hex, byteLen - 27, byteLen - 23) / 100000.0);
//        message.setLat(HexUtil.hex2SignLong(hex, byteLen - 22, byteLen - 18) / 100000.0);
//
//        /* 时间、高度、毫秒 */
//        message.setTime(hex.substring(hex.length() - 17 * 2, hex.length() - 10 * 2));
//        message.setAltitude(HexUtil.hex2SignInt(hex, byteLen - 10, byteLen - 8) / 10.0);
//        message.setMilli(HexUtil.hex2SignInt(hex, byteLen - 8, byteLen - 6));
//        return message;
//    }

}