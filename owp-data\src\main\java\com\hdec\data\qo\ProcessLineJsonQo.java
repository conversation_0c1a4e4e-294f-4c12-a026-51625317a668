package com.hdec.data.qo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class ProcessLineJsonQo {

    /** 海上设施类型 */
    private String seaType;

    /** 单图中显示的最大测点数 */
    private Integer pointNum;

    /** 所占宽度 */
    private String occupyWidth;

    /** 图片规格 */
    private String imgType;

    /** 线条标记 */
    private Boolean isMark;

    /** 每小时采样点数量 */
    private Integer numPerHour;

    private Integer interval;

    /** 仪器ID */
    private Integer instId;

    /** 分量ID(左侧) */
    private Integer leftAttrId;

    /** 分量别名(左侧) */
    private String leftAttrAlias;

    /** 分量方向(左侧) */
    private String[] leftDirects;

    /** 分量ID(右侧) */
    private Integer rightAttrId;

    /** 分量别名(右侧) */
    private String rightAttrAlias;

    /** 分量方向(右侧) */
    private String[] rightDirects;

    /** 方向 */
    private String[] directs;

    /** 左侧分量是否随值调整 */
    private Boolean leftIsAuto;

    /** 左侧分量最小值 */
    private Double leftMin;

    /** 左侧分量最大值 */
    private Double leftMax;

    /** 右侧分量是否随值调整 */
    private Boolean rightIsAuto;

    /** 右侧分量最小值 */
    private Double rightMin;

    /** 右侧分量最大值 */
    private Double rightMax;

    private String direct;

    private String seaId;
    private String seaName;

    private Integer pointId;
    private String pointNo;

    private List<Integer> pointIds;
    private List<String> pointNos;

    private Integer attrId;

    /**
     * 类型（month）
     */
    private String type;
    /**
     * 开始时间
     * -仅用于月历史特征值过程线
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startDate;

    /**
     * 低限值
     */
    private Double limitLow;

    /**
     * 高限值
     */
    private Double limitHigh;

    /**
     * 使用方向别名
     */
    private Boolean useDirectionAlias;

    /**
     * 分量类型[目前仅多分量使用-multi]
     */
    private String  attrType;

    /**
     * 多项分量[仅多分量时有效]
     */
    private List<Integer> multiAttrs;

    private String target;
    private String attrName;


    /**
     * 分量统计方式(左侧)
     */
    private List<String> leftAttrStatItems;

    /**
     * 分量统计方式(右侧)
     */
    private List<String> rightAttrStatItems;

}
