package com.hdec.data.mapper;

import com.hdec.data.domain.template.Mould;
import com.hdec.data.domain.template.ReportCover;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 封面Dao层
 *
 * <AUTHOR>
 */
@Mapper
public interface ReportCoverMapper {

    /**
     * 封面列表
     */
    List<ReportCover> list(@Param("fieldNum") String fieldNum,
                           @Param("name") String name,
                           @Param("type") Integer type,
                           @Param("startTime") String startTime,
                           @Param("endTime") String endTime);
    /**
     * 按名称查找封面
     */
    ReportCover selectByName(@Param("fieldNum") String fieldNum,
                             @Param("name") String name,
                             @Param("id") Integer id);

    /**
     * 更新封面
     */
    void update(@Param("cover") ReportCover cover);

    /**
     * 新增封面
     */
    void add(@Param("cover") ReportCover cover);

    /**
     * 按ID获取封面
     */
    ReportCover getById(@Param("id") Integer id);

    /**
     * 批量删除
     */
    void delete(@Param("ids") Integer[] ids);
}
