package com.hdec.data.config;

import com.github.benmanes.caffeine.cache.Cache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * 本地缓存业务类
 */
@Component
public class LocalCache {

    @Autowired
    private Cache<String, Object> caffeineCache;

    public void set(String key, Object value, long duration, TimeUnit unit) {
        caffeineCache.policy().expireVariably().ifPresent(e -> {
            e.put(key, value, duration, unit);
        });
    }

    public <T> T get(String key) {
        return (T) caffeineCache.getIfPresent(key);
    }

    public void delete(String key) {
        caffeineCache.invalidate(key);
    }

    public long curSize() {
        return caffeineCache.estimatedSize();
    }

}