package com.hdec.data.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;
import java.util.concurrent.atomic.AtomicLong;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class AlarmWindowVo {

    /**
     * 测点
     */
    private Integer point;
    /**
     * 分量
     */
    private Integer attr;
    /**
     * 方向
     */
    private Integer direction;
    /**
     * 值
     */
    private Double value;

    /**
     * 时间
     */
    private Date time;

    /**
     * 失效时间
     */
    private Date expire;

    private AtomicLong count = new AtomicLong(1);

    public AlarmWindowVo(Integer point, Integer attr, Integer direction, Double value, Date time, Date expire) {
        this.point = point;
        this.attr = attr;
        this.direction = direction;
        this.value = value;
        this.time = time;
        this.expire = expire;
    }

    public void addCount() {
        count.incrementAndGet();
    }

    public synchronized void addCountAndMax(Double value, Date time) {
        if (value > this.value) {

            this.value = value;
            this.time = time;
        }
        count.incrementAndGet();
    }
}
