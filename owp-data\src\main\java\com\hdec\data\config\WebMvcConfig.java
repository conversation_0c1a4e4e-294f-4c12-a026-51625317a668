package com.hdec.data.config;

import com.hdec.common.util.CommonUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 资源映射配置类
 *
 * <AUTHOR>
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    /** 访问映射 */
    @Value("${file.visitMapping}")
    private String visitMapping;

    /** 文件本地路径 - Windows */
    @Value("${file.windowPath}")
    private String windowPath;

    /** 文件本地路径 - Linux */
    @Value("${file.linuxPath}")
    private String linuxPath;

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        if (CommonUtil.isLinux()) {
            registry.addResourceHandler("/" + visitMapping + "/**").addResourceLocations("file:" + linuxPath + "/");
        } else {
            registry.addResourceHandler("/" + visitMapping + "/**").addResourceLocations("file:" + windowPath + "/");
        }
    }

}
