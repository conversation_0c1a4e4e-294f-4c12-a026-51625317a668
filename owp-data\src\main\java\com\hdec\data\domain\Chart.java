package com.hdec.data.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/7
 */
@Data
@ToString
@NoArgsConstructor
public class Chart {

    /** 自增主键 */
    private Integer id;

    /** 监测项目 */
    private String itemName;

    /** 测点和方向ID */
    @NotBlank(message = "测点和方向不允许为空")
    private String ids;

    /** 分量参数 */
    @NotNull(message = "分量不允许为空")
    List<ChartAttr> chartAttrs;

    private String attrNames;

    /** 开始时间 */
    @NotBlank(message = "开始时间不允许为空")
    private String startTime;

    /** 结束时间 */
    @NotBlank(message = "结束时间不允许为空")
    private String endTime;

    /** 图片规格 */
    @NotBlank(message = "图片规格不允许为空")
    private String imgType;

    /** 线条标记 */
    @NotNull(message = "线条标记不允许为空")
    private Boolean isMark;

    /** 每天采样点数量 */
    private Integer numPerHour;

    /** 时间间隔（天） */
    private Integer interval;

    /** 测点个数 */
    @NotNull(message = "测点个数不允许为空")
    private Integer pointNum;

    /** 是否是单纵坐标 */
    private Boolean isSingleCoord;

    private String url;

    /** 生成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createTime;

    /** 状态 */
    private String status;

    /** 风场编码 */
    private String fieldNum;

    /** 低限值 */
    private Double limitLow;

    /** 高限值 */
    private Double limitHigh;

}
