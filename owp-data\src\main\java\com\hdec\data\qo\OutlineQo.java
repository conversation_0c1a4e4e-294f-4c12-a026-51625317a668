package com.hdec.data.qo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 大纲查询类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class OutlineQo {

    /** 第几页 */
    private Integer pageNum;

    /** 页条数 */
    private Integer pageSize;

    /** 大纲名称 */
    private String name;

    /** 大纲类型 */
    private String type;

    /** 更新时间 */
    private String startTime;

    /** 更新时间 */
    private String endTime;

}
