package com.hdec.data.service;


import com.alibaba.fastjson.JSONObject;
import com.hdec.common.constant.Constant;
import com.hdec.common.domain.AttrCommon;
import com.hdec.common.domain.FormulaCommon;
import com.hdec.common.formula.*;
import com.hdec.common.util.FormulaDependencyUtils;
import com.hdec.common.util.TimeUtil;
import com.hdec.common.vo.Direct;
import com.hdec.common.vo.InstDirectAttr;
import com.hdec.data.cache.LocalCacheManager;
import com.hdec.data.feign.MonitorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FormulaCalcService {

    @Resource
    private LocalCacheManager localCacheManager;

    @Resource
    private FormulaCalcDataSourceExecutor dataSourceExecutor;

    @Resource
    private FormulaReferenceParser referenceParser;

    @Resource
    private TdBaseService tdBaseService;

    @Resource
    private MonitorService monitorService;

    @Value("${data.access.log.enable:false}")
    private boolean logEnable;

    /**
     * 分析依赖计算
     *
     * @param point     测点
     * @param rate      频率
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return {@link List }<{@link AttrVal }>
     */
    public List<AttrVal> computeDep(Integer point, String rate, Date startTime, Date endTime) {
        if (Objects.equals(rate, Constant.RATE_HIGH)) {
            /*  高频数据不计算  */
            log.info("根据频率计算公式-高频数据不计算");
            return Collections.emptyList();
        }
        /*  获取测点分量  */
        InstDirectAttr instDirectAttr = monitorService.getInstAttrsByRate(point, rate);
        List<AttrCommon> rateAttrs = instDirectAttr.getAttrs();
        /*  获取仪器方向  */
        Integer[] instDirects = instDirectAttr.getDirects().stream()
                .map(Direct::getDirectId)
                .toArray(Integer[]::new);
        /*  组装公式  */
        List<FormulaCommon> formulas = new ArrayList<>();
        for (AttrCommon attr : rateAttrs) {
            Integer attrId = attr.getId();
            Integer[] directs = attr.getIsPolar() ? new Integer[]{0} : instDirects;
            for (Integer direct : directs) {
                List<FormulaCommon> adfs = getAttrDependCalcFormulas(point, attrId, direct);
                adfs.forEach(f -> {
                    if (!formulas.contains(f)) {
                        formulas.add(f);
                    }
                });
            }
        }
        /*  计算公式  */
        List<AttrVal> values = new ArrayList<>();
        for (FormulaCommon formulaInfo : formulas) {
            List<AttrVal> res = compute(formulaInfo, startTime, endTime);
            values.addAll(res);
        }
        return values;
    }

    /**
     * 公式计算
     *
     * @param formula   formula
     * @param startTime start time
     * @param endTime   end time
     * @return {@link List }<{@link AttrVal }>
     */
    public List<AttrVal> compute(FormulaCommon formula, Date startTime, Date endTime) {
        Integer inst = formula.getInstId();
        Integer point = formula.getPointId();
        Integer attr = formula.getAttrId();
        Integer direct = formula.getDirect();
        String rate = formula.getRate();
        AviatorEnv env = new AviatorEnv();
        /*  设置开始结束时间  */
        env.setStartTime(startTime);
        env.setEndTime(endTime);
        /*  设置结果分量  */
        env.setAttrResult(new AttrVar(inst.toString(),
                point.toString(), attr.toString(), direct.toString(), rate));
        /*  清除特殊标记字符  */
        String formulaStr = FormulaUtil.clearCustomSymbol(formula.getFormula());

        AviatorFormula aviatorFormula = new AviatorFormula(formulaStr, inst.toString(),
                point.toString(), attr.toString(), direct.toString(), rate);
        Object execute = FormulaEvaluator.execute(aviatorFormula, env, referenceParser, dataSourceExecutor);
        return execute == null ? Collections.emptyList() : AviatorUtil.safeCast(execute, AttrVal.class);
    }

    /**
     * 批量计算
     *
     * @param attrValues   直接数据
     * @param memoryValues 内存数据
     * @return {@link List }<{@link AttrVal }>
     */
    public List<AttrVal> accessBatchCompute(List<AttrVal> attrValues, List<AttrVal> memoryValues) {
        List<AttrVal> result = new ArrayList<>();
        /*  先按时间分组  */
        Map<Date, List<AttrVal>> timeMap = attrValues.stream().collect(Collectors.groupingBy(AttrVal::getTs));
        /*  按测点方向分组,给获取分量参数用  */
        Map<String, List<AttrVal>> directAttrVarMap = attrValues.stream()
                .collect(Collectors.groupingBy(e -> e.getPoint() + "_" + e.getAttr() + "_" + e.getDirect()));
        Map<String, List<AttrVal>> memoryValueMap = memoryValues.stream()
                .collect(Collectors.groupingBy(e -> e.getPoint() + "_" + e.getAttr() + "_" + e.getDirect()));
        timeMap.forEach((ts, values) -> {
            /*  按测点分量分组  */
            Map<String, List<AttrVal>> attrMap = values.stream().collect(
                    Collectors.groupingBy(e -> e.getInst() + "_" + e.getPoint() + "_" + e.getAttr()));
            attrMap.forEach((key, vs) -> {
                String[] split = key.split("_");
                Integer point = Integer.valueOf(split[1]);
                Integer attr = Integer.valueOf(split[2]);
                List<String> directs = vs.stream().map(AttrVal::getDirect)
                        .filter(Objects::nonNull).distinct().collect(Collectors.toList());
                String rate = vs.stream().map(AttrVal::getRate)
                        .filter(Objects::nonNull).findFirst().orElse(null);
                if (rate == null) {
                    return;
                }
                /*  查询公式  */
                for (String direct : directs) {
                    List<FormulaCommon> formulas = localCacheManager.getUpDepsFormulaCache(point, attr, Integer.parseInt(direct));
                    if (formulas == null || formulas.isEmpty()) {
                        continue;
                    }
                    formulas = formulas.stream().filter(e -> Objects.equals(e.getRate(), rate)).collect(Collectors.toList());
                    /*  公式计算  */
                    for (FormulaCommon formula : formulas) {
                        /*  计算  */
                        AttrVal val = compute(formula, ts, directAttrVarMap, memoryValueMap);
                        /*  添加到结果  */
                        if (val != null && val.getVal() != null && !val.getVal().isNaN()) {
                            result.add(val);
                            /*  放入分量参数  */
                            String varKey = val.getPoint() + "_" + val.getAttr() + "_" + val.getDirect();
                            List<AttrVal> directVar = directAttrVarMap.get(varKey);
                            directVar = directVar == null ? new ArrayList<>() : directVar;
                            directVar.add(val);
                            directAttrVarMap.put(varKey, directVar);
                        }
                    }
                }
            });
        });
        return result;
    }

    /**
     * 计算
     *
     * @param formula      公式
     * @param ts           时间
     * @param directValues 直接数据
     * @param memoryValues 队列缓存数据
     * @return {@link AttrVal }
     */
    public AttrVal compute(FormulaCommon formula, Date ts, Map<String, List<AttrVal>> directValues,
                           Map<String, List<AttrVal>> memoryValues) {
        Integer inst = formula.getInstId();
        String rate = formula.getRate();
        Integer direct = formula.getDirect();
        Map<String, Object> env = new HashMap<>();
        /*  设置结果分量  */
        AttrVar attrVar = new AttrVar(inst.toString(), formula.getPointId().toString(),
                formula.getAttrId().toString(), formula.getDirect().toString(), rate);
        env.put(AviatorEnvKey.ATTR_RESULT, attrVar);
        /*  清除特殊标记字符  */
        String expression = FormulaUtil.clearCustomSymbol(formula.getFormula());
        /*  处理参数  */
        List<String> variables = FormulaEvaluator.compile(expression);
        /*  处理测点参数  */
        boolean isNeedCalc = fitPointParamsVariables(env, variables, direct);
        if (!isNeedCalc) {
            return null;
        }
        /*  处理分量参数  */
        boolean isCalc = fitAttrVariables(expression, env, variables, direct, ts, directValues, memoryValues);
        if (isCalc) {
            /*  计算   */
            Double result = (Double) FormulaEvaluator.execute(expression, env);
            return new AttrVal(ts, attrVar.getInst(), attrVar.getPoint(), attrVar.getAttr(),
                    attrVar.getDirect(), attrVar.getRate(), result);
        } else {
            return null;
        }
    }

    /**
     * 获取分量极其依赖项计算公式
     *
     * @param point  测点
     * @param attr   分量
     * @param direct 方向
     * @return {@link List }<{@link FormulaCommon }>
     */
    public List<FormulaCommon> getAttrDependCalcFormulas(Integer point, Integer attr, Integer direct) {
        List<FormulaCommon> formulas = localCacheManager.getFormulaCache();
        String key = point + ":" + attr + ":" + direct;
        return FormulaDependencyUtils.resolveDependencyChain(key, formulas);
    }

    /**
     * 组装公式参数
     *
     * @param env           环境变量
     * @param variables     参数
     * @param defaultDirect 默认方向
     * @return boolean
     */
    public boolean fitPointParamsVariables(Map<String, Object> env, List<String> variables, Integer defaultDirect) {
        variables = variables.stream().filter(e -> e.startsWith("p_")).collect(Collectors.toList());
        boolean isNeedCalc = true;
        for (String variable : variables) {
            /*  测点参数  */
            String[] split = variable.split("_");
            if (split.length < 3) {
                if (logEnable) {
                    log.error("测点参数[{}]格式错误", variable);
                }
                isNeedCalc = false;
                break;
            }
            String d = split.length > 3 ? split[3] : defaultDirect.toString();
            Double param = localCacheManager.getPointParamCache(split[1], split[2], d);
            if (param == null) {
                if (logEnable) {
                    log.error("测点[{}]参数[{}]方向[{}]不存在", split[1], split[2], d);
                }
                isNeedCalc = false;
                break;
            }
            env.put(variable, param);
        }
        return isNeedCalc;
    }

    /**
     * 填充测点分量值
     *
     * @param formula      公式
     * @param env          参数
     * @param variables    变量
     * @param direct       方向
     * @param ts           时间
     * @param directValues 直接数据
     * @param memoryValues 内存数据
     * @return boolean
     */
    public boolean fitAttrVariables(String formula, Map<String, Object> env, List<String> variables,
                                    Integer direct, Date ts, Map<String, List<AttrVal>> directValues, Map<String, List<AttrVal>> memoryValues) {
        variables = variables.stream().filter(e -> e.startsWith("a_")).collect(Collectors.toList());
        boolean isNeedCalc = true;
        /*  按测点分量分组  */
        for (String variable : variables) {
            String[] split = variable.split("_");
            String point = split[1];
            String attr = split[2];
            Integer d = split.length > 3 ? Integer.valueOf(split[3]) : direct;
            String matchType = split.length > 4 ? split[4] : null;
            Integer timeScope = split.length > 5 ? Integer.valueOf(split[5]) : null;

            /*  从直接数据中取  */
            List<AttrVal> vs = directValues.get(point + "_" + attr + "_" + d);
            if (vs != null && !vs.isEmpty()) {
                AttrVal val = selectAttrVarOnMemory(point, attr, d.toString(), ts, matchType, timeScope, vs);
                if (val != null && val.getVal() != null && !val.getVal().isNaN()) {
                    env.put(variable, val.getVal());
                    continue;
                }
            }

            if (matchType == null) {
                /*  直接分量未获取到  */
                isNeedCalc = false;
                break;
            } else {
                /*  从缓存中的数据读取 */
                vs = memoryValues.get(point + "_" + attr + "_" + d);
                if (vs != null && !vs.isEmpty()) {
                    AttrVal val = selectAttrVarOnMemory(point, attr, d.toString(), ts, matchType, timeScope, vs);
                    if (val != null && val.getVal() != null && !val.getVal().isNaN()) {
                        env.put(variable, val.getVal());
                        continue;
                    }
                }
                /*  从TD读取  */
                Double v = selectAttrVarOnTd(point, attr, d.toString(), ts, matchType, timeScope);
                if (v != null && !v.isNaN()) {
                    env.put(variable, v);
                    continue;
                }
            }
            if (logEnable) {
                log.error("接入量直接计算分量-引用分量[{}]无值-公式[{}]", variable, formula);
            }
            isNeedCalc = false;
            break;
        }
        return isNeedCalc;
    }

    /**
     * 内存中查询
     *
     * @param point     测点
     * @param attr      分量
     * @param direct    方向
     * @param ts        时间
     * @param matchType 匹配类型
     * @param timeScope 时间范围
     * @param values    值
     * @return {@link AttrVal }
     */
    public AttrVal selectAttrVarOnMemory(String point, String attr, String direct, Date ts,
                                         String matchType, Integer timeScope,
                                         List<AttrVal> values) {
        if (matchType == null) {
            return values.stream().filter(e -> Objects.equals(e.getPoint(), point)
                    && Objects.equals(e.getAttr(), attr)
                    && Objects.equals(e.getDirect(), direct)
                    && Objects.equals(e.getTs(), ts)
            ).findFirst().orElse(null);
        } else if (Objects.equals("1", matchType)) {
            /*  当前时间值  */
            return values.stream().filter(e -> Objects.equals(e.getPoint(), point)
                    && Objects.equals(e.getAttr(), attr)
                    && Objects.equals(e.getDirect(), direct)
                    && Objects.equals(e.getTs(), ts)
            ).findFirst().orElse(null);
        } else if (Objects.equals("2", matchType)) {
            /*  时间范围内的值  */
            return values.stream().filter(e -> Objects.equals(e.getPoint(), point)
                    && Objects.equals(e.getAttr(), attr)
                    && Objects.equals(e.getDirect(), direct)
                    && Math.abs(e.getTs().getTime() - ts.getTime()) <= timeScope
            ).min((o1, o2) -> {
                long t1 = Math.abs(o1.getTs().getTime() - ts.getTime());
                long t2 = Math.abs(o2.getTs().getTime() - ts.getTime());
                return (int) (t2 - t1);
            }).orElse(null);
        } else if (Objects.equals("3", matchType)) {
            /*  之前最近测值  */
            return values.stream().filter(e -> Objects.equals(e.getPoint(), point)
                    && Objects.equals(e.getAttr(), attr)
                    && Objects.equals(e.getDirect(), direct)
            ).min((o1, o2) -> {
                long t1 = Math.abs(o1.getTs().getTime() - ts.getTime());
                long t2 = Math.abs(o2.getTs().getTime() - ts.getTime());
                return (int) (t2 - t1);
            }).orElse(null);
        } else {
            return null;
        }
    }

    /**
     * 在TD中查询数据
     *
     * @param point     测点
     * @param attr      分量
     * @param direct    方向
     * @param ts        时间
     * @param matchType 匹配类型
     * @param timeScope 时间范围
     * @return {@link Double }
     */
    public Double selectAttrVarOnTd(String point, String attr, String direct, Date ts,
                                    String matchType, Integer timeScope) {
        AttrCommon attrCache = localCacheManager.getAttrCache(Integer.parseInt(attr));
        if (attrCache == null || attrCache.getInstId() == null || attrCache.getRate() == null) {
            return null;
        }
        Integer inst = attrCache.getInstId();
        String rate = attrCache.getRate();
        /*
         * select ts, point, ABS((CAST(ts as BIGINT) - 1745542800000)) as diff
         * from owp.hour_460 where ts between '2025-04-25 08:00:00' and '2025-04-25 10:00:00'
         * order by diff asc limit 1;
         * */
        String table = toTableName(rate, inst.toString(), point);
        if (Objects.equals("1", matchType)) {
            /*  当前时间值  */
            String sql = "select a_" + attr + "_" + direct + " as val from " + table +
                    " where " + "ts = '" + TimeUtil.format2Ms(ts) + "' limit 1 ;";
            Map<String, Object> map = tdBaseService.selectOne(sql, rate);
            if (map != null && map.get("val") != null) {
                Object o = map.get("val");
                return o instanceof Float ? (Float) o :
                        o instanceof Double ? (Double) o :
                                Double.valueOf(o.toString());
            }
            return null;
        } else if (Objects.equals("2", matchType)) {
            /*  时间范围内的值  */
            if (timeScope == null) {
                return null;
            }
            Date start = new Date(ts.getTime() - timeScope / 2);
            Date end = new Date(ts.getTime() + timeScope / 2);
            String sql = "select a_" + attr + "_" + direct + " as val," +
                    " ABS((CAST(ts as BIGINT) - " + ts.getTime() + ")) as diff"
                    + " from " + table
                    + " where ts between '" + TimeUtil.format2Ms(start) + "' and '" + TimeUtil.format2Ms(end) + "'"
                    + " order by diff asc limit 1;";
            Map<String, Object> map = tdBaseService.selectOne(sql, rate);
            if (map != null && map.get("val") != null) {
                Object o = map.get("val");
                return o instanceof Float ? (Float) o :
                        o instanceof Double ? (Double) o :
                                Double.valueOf(o.toString());
            }
            return null;
        } else if (Objects.equals("3", matchType)) {
            /*  之前最近测值  */
            String sql = "select a_" + attr + "_" + direct + " as val," +
                    " ABS((CAST(ts as BIGINT) - " + ts.getTime() + ")) as diff"
                    + " from " + table
                    + " order by diff asc limit 1;";
            Map<String, Object> map = tdBaseService.selectOne(sql, rate);
            if (map != null && map.get("val") != null) {
                Object o = map.get("val");
                return o instanceof Float ? (Float) o :
                        o instanceof Double ? (Double) o :
                                Double.valueOf(o.toString());
            }
            return null;
        } else {
            /*  没有匹配策略时直接返回  */
            return null;
        }
    }

    /**
     * 转表名称
     *
     * @param rate  频率
     * @param inst  仪器
     * @param point 测点
     * @return {@link String }
     */
    private static String toTableName(String rate, String inst, String point) {
        rate = Objects.equals("高频", rate) ? "high" :
                Objects.equals("分钟级", rate) ? "min" :
                        Objects.equals("小时级", rate) ? "hour" : null;
        inst = Objects.equals("", inst.trim()) ? null : inst;
        point = Objects.equals("", point.trim()) ? null : point;
        return rate == null || inst == null || point == null ? null : rate + "_" + inst + "_" + point;
    }


    public static void main(String[] args) throws Exception {
        String path = "/Users/<USER>/Desktop/formulas.json";
        String formulasStr = new String(Files.readAllBytes(Paths.get(path)));
        List<FormulaCommon> formulas = JSONObject.parseArray(formulasStr, FormulaCommon.class);
        String key = "19755:3888:1";
        List<FormulaCommon> fs = FormulaDependencyUtils.resolveDependencyChain(key, formulas)
                .stream().filter(e -> !Objects.equals(e.getRate(), Constant.RATE_HIGH))
                .collect(Collectors.toList());
        log.info("公式:{}", fs);
    }
}
