package com.hdec.data.qo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class TabJsonQo {

    /** 海上设施ID */
    private String seaId;

    /** 测点ID */
    private List<Integer> pointIds;

    /** 分量ID */
    private Integer attrId;

    /** 方向 */
    private String direct;

    /** 统计项 */
    private String statItem;

    /** 自定义变量 */
    private String customVar;


    /**
     * 周数
     * 仅用于按周统计表中记录周数
     */
    private Integer week;

    /**
     * 测点ID
     * 仅用于按周统计表中兼容变量弹出框
     */
    private Integer pointId;

    /**
     * 基准统计方式
     * 仅用于计算相对变化量中获取计算的统计方式
     */
    private String referStatItem;

    /**
     * 基准值参数ID
     * 仅用于计算相对变化量中通过此ID获取仪器参数
     */
    private Integer referParamId;

}
