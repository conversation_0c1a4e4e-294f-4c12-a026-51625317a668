package com.hdec.data.util;

import com.hdec.data.domain.ExcelSheet;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2022/8/31
 */
public class ExcelUtil {

    /**
     * 在excel准备数据时，向浏览器输出字节流
     *
     * @param response
     * @param fileName
     * @throws Exception
     */
    public static void exportByte(HttpServletResponse response, String fileName) throws Exception {
        String hex = "50 4B 03 04 14 00 08 08 08 00";
        String[] sArr = hex.split(" ");
        Object[] arr = new Object[sArr.length];
        for (int i = 0; i < sArr.length; i++) {
            arr[i] = hexToByteArray(sArr[i]);
        }

        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(fileName, "UTF-8") + ".xlsx");
        response.addHeader("Content-Length", "-1");

        OutputStream os = response.getOutputStream();

        for (int i = 0; i < arr.length; i++) {
            os.write((byte[]) arr[i]);
            os.flush();
        }
    }

    public static byte[] hexToByteArray(String inHex) {
        int hexlen = inHex.length();
        byte[] result;
        if (hexlen % 2 == 1) {
            //奇数
            hexlen++;
            result = new byte[(hexlen / 2)];
            inHex = "0" + inHex;
        } else {
            //偶数
            result = new byte[(hexlen / 2)];
        }
        int j = 0;
        for (int i = 0; i < hexlen; i += 2) {
            result[j] = hexToByte(inHex.substring(i, i + 2));
            j++;
        }
        return result;
    }

    public static byte hexToByte(String inHex) {
        return (byte) Integer.parseInt(inHex, 16);
    }


//    public static void createN(String filename, String[] title, String[] key, List<Map<String, Object>> values, HttpServletResponse response) throws IOException {
//        OutputStream os = response.getOutputStream();
//
////        String filename2 = new String(filename.getBytes(), "iso-8859-1");
////        response.setHeader("Content-disposition", "attachment; filename=" + filename2);
////        response.setContentType("application/x-download");
//
//        XSSFWorkbook workbook = new XSSFWorkbook();
//
//        // 设置style
//        CellStyle cstyle2 = workbook.createCellStyle();
//        cstyle2.setAlignment(HorizontalAlignment.CENTER);
//        cstyle2.setVerticalAlignment(VerticalAlignment.CENTER);
//        XSSFFont font2 = workbook.createFont();
//
//        font2.setFontHeightInPoints((short) 10);
//        font2.setFontName("微软雅黑");
//        font2.setBold(false);
//        cstyle2.setFont(font2);
//
//        XSSFSheet sheet = null;
//        int sheetSize = 30000;
//
//        Map map;
//        for (int i = 0; values != null && i < values.size(); i++) {
//            if (i % sheetSize == 0) {
//                System.out.println(i);
//                sheet = createSheet(workbook, title, i);
//            }
//
//            XSSFRow row = sheet.createRow((short) ((i % sheetSize) + 1));
//            map = values.get(i);
//            for (int i2 = 0; i2 < key.length; i2++) {
//                XSSFCell cell = row.createCell((short) (i2));
//                cell.setCellStyle(cstyle2);
//                if (map.get(key[i2]) == null) {
//                    cell.setCellValue(new XSSFRichTextString(""));
//                } else {
//                    cell.setCellValue(new XSSFRichTextString(map.get(key[i2]).toString()));
//                }
//            }
//        }
//
//        /* 输出到客户端 */
//        try {
//            ByteArrayOutputStream bos = new ByteArrayOutputStream();
//            workbook.write(bos);
//            byte[] srcBytes = bos.toByteArray();
//            byte[] resBytes = new byte[srcBytes.length - 10];
//            System.arraycopy(srcBytes, 10, resBytes, 0, resBytes.length);
//            os.write(resBytes);
//            os.flush();
//        } catch (IOException e) {
//            e.printStackTrace();
//        } finally {
//            os.close();
//        }
//    }

    public static void createNBig(String filename, String[] title, String[] key, List<Map<String, Object>> values, HttpServletResponse response) throws IOException, ParseException {
        OutputStream os = response.getOutputStream();

        SXSSFWorkbook workbook = new SXSSFWorkbook();

        // 设置style
        CellStyle cstyle2 = workbook.createCellStyle();
        cstyle2.setAlignment(HorizontalAlignment.CENTER);
        cstyle2.setVerticalAlignment(VerticalAlignment.CENTER);
        Font font2 = workbook.createFont();

        font2.setFontHeightInPoints((short) 10);
        font2.setFontName("微软雅黑");
        font2.setBold(false);
        cstyle2.setFont(font2);

        SXSSFSheet sheet = null;
        int sheetSize = 30000;

        Map map;
        DataFormat df = workbook.createDataFormat();
        CellStyle dateStyle = workbook.createCellStyle();
        CellStyle floatStyle = workbook.createCellStyle();
        for (int i = 0; values != null && i < values.size(); i++) {
            if (i % sheetSize == 0) {
                sheet = createSheetBig(workbook, title, i);
            }

            SXSSFRow row = sheet.createRow((short) ((i % sheetSize) + 1));
            map = values.get(i);
            for (int i2 = 0; i2 < key.length; i2++) {
                SXSSFCell cell = row.createCell((short) (i2));
                cell.setCellStyle(cstyle2);
                Object value = map.get(key[i2]);
                if (value == null) {
                    cell.setCellValue(new XSSFRichTextString(""));
                } else {
                    if (value.toString().endsWith(".000")) {
                        // 日期列设置24个字符
                        sheet.setColumnWidth(i2, 24 * 256);
                        /* 日期类型 */
                        dateStyle.cloneStyleFrom(cstyle2);
                        DataFormat format = workbook.createDataFormat();
                        dateStyle.setDataFormat(format.getFormat("yyyy-mm-dd HH:mm:ss"));

                        DateFormat fmt =new SimpleDateFormat("yyyy-mm-dd HH:mm:ss");
                        Date date = fmt.parse(value.toString().substring(0, 19));

                        cell.setCellValue(date);
                        cell.setCellStyle(dateStyle);
                    } else if (value instanceof Float) {
                        /* 小数类型 */
                        floatStyle.cloneStyleFrom(cstyle2);
                        floatStyle.setDataFormat(df.getFormat("#0.000000"));
                        cell.setCellValue((Float) value);
                        cell.setCellStyle(floatStyle);
                    } else {
                        cell.setCellValue(new XSSFRichTextString(value.toString()));
                    }
                }
            }
        }

        /* 输出到客户端 */
        try {
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            workbook.write(bos);
            byte[] srcBytes = bos.toByteArray();
            byte[] resBytes = new byte[srcBytes.length - 10];
            System.arraycopy(srcBytes, 10, resBytes, 0, resBytes.length);
            os.write(resBytes);
            os.flush();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            os.close();
        }
    }

    public static void createNBig2File(String filename, Map<String, ExcelSheet> excelSheetMap, String outPath) throws Exception {
        SXSSFWorkbook workbook = new SXSSFWorkbook();

        // 设置style
        CellStyle cstyle2 = workbook.createCellStyle();
        cstyle2.setAlignment(HorizontalAlignment.CENTER);
        cstyle2.setVerticalAlignment(VerticalAlignment.CENTER);
        Font font2 = workbook.createFont();

        font2.setFontHeightInPoints((short) 10);
        font2.setFontName("微软雅黑");
        font2.setBold(false);
        cstyle2.setFont(font2);

        SXSSFSheet sheet = null;
        int sheetSize = 100000;

        Map map;
        DataFormat df = workbook.createDataFormat();
        CellStyle dateStyle = workbook.createCellStyle();
        CellStyle floatStyle = workbook.createCellStyle();

        for (Map.Entry<String, ExcelSheet> en : excelSheetMap.entrySet()) {
            String pointNo = en.getKey();
            ExcelSheet excelSheet = en.getValue();

            int index = 0;

            String[] keys = excelSheet.getKeys();
            String[] titles = excelSheet.getTitles();
            List<Map<String, Object>> values = excelSheet.getValues();

            for (int i = 0; values != null && i < values.size(); i++) {
                if (i % sheetSize == 0) {
                    sheet = createSheetBig2File(workbook, pointNo, titles, index++);
                }

//                SXSSFRow row = sheet.createRow((short) ((i % sheetSize) + 1));
                SXSSFRow row = sheet.createRow((i % sheetSize) + 1);
                map = values.get(i);
                for (int i2 = 0; i2 < keys.length; i2++) {
                    SXSSFCell cell = row.createCell((short) (i2));
                    cell.setCellStyle(cstyle2);
                    Object value = map.get(keys[i2]);
                    if (value == null) {
                        cell.setCellValue(new XSSFRichTextString(""));
                    } else {
                        if (value.toString().endsWith(".000")) {
                            // 日期列设置24个字符
                            sheet.setColumnWidth(i2, 24 * 256);
                            /* 日期类型 */
                            dateStyle.cloneStyleFrom(cstyle2);
                            DataFormat format = workbook.createDataFormat();
                            dateStyle.setDataFormat(format.getFormat("yyyy-mm-dd HH:mm:ss"));

                            DateFormat fmt =new SimpleDateFormat("yyyy-mm-dd HH:mm:ss");
                            Date date = fmt.parse(value.toString().substring(0, 19));

                            cell.setCellValue(date);
                            cell.setCellStyle(dateStyle);
                        } else if (value instanceof Float) {
                            /* 小数类型 */
                            floatStyle.cloneStyleFrom(cstyle2);
                            floatStyle.setDataFormat(df.getFormat("#0.000000"));
                            cell.setCellValue((Float) value);
                            cell.setCellStyle(floatStyle);
                        } else {
                            cell.setCellValue(new XSSFRichTextString(value.toString()));
                        }
                    }
                }
            }
        }
        workbook.write(new FileOutputStream(outPath));
    }

    /**
     * 获取基础单元格样式
     */
    private static CellStyle getBaseCellStyle(XSSFWorkbook wb) {
        CellStyle style = wb.createCellStyle();

        /* 内容居中 */
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        /* 细边框 */
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        return style;
    }

    /**
     * 获取基础字体
     */
    private static XSSFFont getBaseFont(XSSFWorkbook wb) {
        XSSFFont font = wb.createFont();
        font.setFontName("微软雅黑");
        font.setFontHeightInPoints((short) 10);
        return font;
    }

    /**
     * 创建普通单元格样式
     */
    private static CellStyle createNormalCellStyle(XSSFWorkbook wb) {
        CellStyle baseStyle = getBaseCellStyle(wb);
        XSSFFont baseFont = getBaseFont(wb);

        baseStyle.setFont(baseFont);
        return baseStyle;
    }

    /**
     * 创建加粗单元格样式
     */
    private static CellStyle createBoldCellStyle(XSSFWorkbook wb) {
        CellStyle baseStyle = getBaseCellStyle(wb);
        XSSFFont baseFont = getBaseFont(wb);
        baseFont.setBold(true);

        baseStyle.setFont(baseFont);
        return baseStyle;
    }

    /**
     * 创建加粗、大字体且有背景色单元格样式
     */
    private static CellStyle createBoldBigBackgroundCellStyle(XSSFWorkbook wb) {
        CellStyle baseStyle = getBaseCellStyle(wb);
        XSSFFont baseFont = getBaseFont(wb);

        baseFont.setBold(true);
        baseFont.setFontHeightInPoints((short) 14);

        /* 填充背景色 */
        baseStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        XSSFColor bgColor = new XSSFColor(new java.awt.Color(240, 240, 240), new DefaultIndexedColorMap());
        ((XSSFCellStyle) baseStyle).setFillForegroundColor(bgColor);

        baseStyle.setFont(baseFont);
        return baseStyle;
    }


    /**
     * 导出普通Excel
     */
    public static void exportNormalExcel(String name, String[] titles, String[] keys, Map<String, List<Map<String, Object>>> sheetMaps,
                                         HttpServletResponse response, boolean needRemoveBytes, String dateFormat, boolean needDesc) throws IOException {
        /* 获取输出流 */
        ServletOutputStream os = response.getOutputStream();
        response.setHeader("Content-disposition", "attachment; filename=" + new String((name + ".xlsx").getBytes("UTF-8"), "ISO-8859-1"));
        response.setContentType("application/x-download");

        /* 创建sheet */
        XSSFWorkbook wb = new XSSFWorkbook();

        /* 创建内容、表头样式 */
        CellStyle descStyle = createBoldBigBackgroundCellStyle(wb);
        CellStyle titleStyle = createBoldCellStyle(wb);
        CellStyle contentStyle = createNormalCellStyle(wb);

        /* 写入多Sheet */
        sheetMaps.forEach((sheetName, valueMaps) -> {
            XSSFSheet sheet = wb.createSheet(sheetName.replace("/", ""));

            int start = 0;
            if (needDesc) {
                start = 1;
                /* 写描述行（合并前2行） */
                for (int i = 0; i < 2; i++) {
                    XSSFRow row = sheet.createRow((short) i);
                    for (int j = 0; j < titles.length; j++) {
                        XSSFCell cell = row.createCell((short) j);
                        cell.setCellStyle(descStyle);
                        if (i == 0 && j == 0) {
                            cell.setCellValue(new XSSFRichTextString(name));
                        }
                    }
                }
                sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, titles.length - 1));
            }

            /* 写表头行 */
            XSSFRow row = sheet.createRow((short) start);
            for (int i = 0; i < titles.length; i++) {
                XSSFCell cell = row.createCell((short) i);
                cell.setCellStyle(titleStyle);
                cell.setCellValue(new XSSFRichTextString(titles[i]));
            }

            /* 写内容行 */
            for (int i = 0; i < valueMaps.size(); i++) {
                row = sheet.createRow((short) (i + start + 1));
                Map<String, Object> map = valueMaps.get(i);
                for (int j = 0; j < keys.length; j++) {
                    XSSFCell cell = row.createCell((short) (j));
                    cell.setCellStyle(contentStyle);
                    Object value = map.get(keys[j]);
                    if (value == null) {
                        cell.setCellValue(new XSSFRichTextString(""));
                    } else {
                        XSSFDataFormat df = wb.createDataFormat();
                        if (value instanceof Date) {
                            /* 日期类型 */
                            CellStyle dateStyle = wb.createCellStyle();
                            dateStyle.cloneStyleFrom(contentStyle);
                            dateStyle.setDataFormat(wb.createDataFormat().getFormat(dateFormat));
                            cell.setCellValue((Date) value);
                            cell.setCellStyle(dateStyle);
                        } else if (value instanceof Integer) {
                            /* 整数类型 */
                            CellStyle numStyle = wb.createCellStyle();
                            numStyle.cloneStyleFrom(contentStyle);
                            numStyle.setDataFormat(df.getFormat("#0"));
                            cell.setCellValue((Integer) value);
                            cell.setCellStyle(numStyle);
                        } else if (value instanceof Float || value instanceof Double) {
                            /* 小数类型 */
                            CellStyle floatStyle = wb.createCellStyle();
                            floatStyle.cloneStyleFrom(contentStyle);
                            floatStyle.setDataFormat(df.getFormat("#0.000000"));
                            if (value instanceof Float) {
                                cell.setCellValue((Float) value);
                            } else {
                                cell.setCellValue((Double) value);
                            }
                            cell.setCellStyle(floatStyle);
                        } else {
                            cell.setCellValue(new XSSFRichTextString(value.toString()));
                        }
                    }
                }
            }

            /* 调整列宽自适应 */
            for (int i = 0; i < titles.length; i++) {
                sheet.autoSizeColumn(i);
                // 对太宽的列做限制
                if (sheet.getColumnWidth(i) > 6000) {
                    sheet.setColumnWidth(i, 6000);
                }
                if ("时间".equals(titles[i])) {
                    // 日期列设置24个字符
                    sheet.setColumnWidth(i, 24 * 256);
                }
            }
        });


        /* 输出到客户端 */
        if (needRemoveBytes) {
            System.out.println("去掉了10字节");
            try {
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                wb.write(bos);
                byte[] srcBytes = bos.toByteArray();
                byte[] resBytes = new byte[srcBytes.length - 10];
                System.arraycopy(srcBytes, 10, resBytes, 0, resBytes.length);
                os.write(resBytes);
                os.flush();
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                os.close();
            }
        } else {
            System.out.println("else");
            wb.write(os);
            os.flush();
            os.close();
        }
    }

    public static void writeExcelPerSheet(String filename, String[] title, String[] key, Map<String, List<Map<String, Object>>> valMap, HttpServletResponse response) throws IOException {
        OutputStream os = response.getOutputStream();

        String filename2 = new String(filename.getBytes(), "iso-8859-1");
        response.setHeader("Content-disposition", "attachment; filename=" + filename2);
        response.setContentType("application/x-download");

        XSSFWorkbook workbook = new XSSFWorkbook();

        // 设置style
        CellStyle cstyle2 = workbook.createCellStyle();
        cstyle2.setAlignment(HorizontalAlignment.CENTER);
        cstyle2.setVerticalAlignment(VerticalAlignment.CENTER);
        XSSFFont font2 = workbook.createFont();

        font2.setFontHeightInPoints((short) 10);
        font2.setFontName("微软雅黑");
        font2.setBold(false);
        cstyle2.setFont(font2);

        valMap.forEach((tableName, values) -> {
            XSSFSheet sheet = createSheet1(workbook, title, tableName.replace("/", ""));
            XSSFRow row = sheet.createRow(0);
            XSSFCell cell = row.createCell(0);
            cell.setCellStyle(cstyle2);
            cell.setCellValue(new XSSFRichTextString(tableName));
            CellRangeAddress region = new CellRangeAddress(0, 1, 0, key.length - 1); // 初始行，终止行，初始列，终止列
            sheet.addMergedRegion(region);

            System.err.println("values.size():" + values.size());

            for (int i = 0; values != null && i < values.size(); i++) {
                row = sheet.createRow((short) (i + 2));
                Map map = values.get(i);
                for (int i2 = 0; i2 < key.length; i2++) {
                    cell = row.createCell((short) (i2));
                    cell.setCellStyle(cstyle2);
                    Object value = map.get(key[i2]);
                    if (value == null) {
                        cell.setCellValue(new XSSFRichTextString(""));
                    } else {
                        cell.setCellValue(new XSSFRichTextString(value.toString()));

                        if (value instanceof Date) {
                            /* 日期类型 */
                            CellStyle dateStyle = workbook.createCellStyle();
                            dateStyle.cloneStyleFrom(cstyle2);
                            DataFormat format = workbook.createDataFormat();
                            dateStyle.setDataFormat(format.getFormat("yyyy-mm-dd"));
                            cell.setCellValue((Date) value);
                            cell.setCellStyle(dateStyle);
                        } else {
                            cell.setCellValue(new XSSFRichTextString(value.toString()));
                        }

                    }
                }
            }
        });

        /* 输出到客户端 */
        try {
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            workbook.write(bos);
            byte[] srcBytes = bos.toByteArray();
            byte[] resBytes = new byte[srcBytes.length - 10];
            System.arraycopy(srcBytes, 10, resBytes, 0, resBytes.length);
            os.write(resBytes);
            os.flush();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            os.close();
        }

        workbook.write(os);
        os.flush();
        os.close();
    }

    private static SXSSFSheet createSheetBig2File(SXSSFWorkbook workbook, String pointNo, String[] title, int index) {
        String sheetName;
        if (index == 0) {
            sheetName = pointNo;
        } else {
            sheetName = pointNo + "(" + index + ")";
        }
        SXSSFSheet sheet = workbook.createSheet(sheetName);

        // 设置style
        CellStyle cstyle = workbook.createCellStyle();
        cstyle.setAlignment(HorizontalAlignment.CENTER);
        cstyle.setVerticalAlignment(VerticalAlignment.CENTER);
        Font font = workbook.createFont();

        font.setFontHeightInPoints((short) 10);
        font.setFontName("微软雅黑");
        font.setBold(true);
        cstyle.setFont(font);

        SXSSFRow row;
        SXSSFCell cell;
        row = sheet.createRow((short) 0);
        for (int i = 0; title != null && i < title.length; i++) {
            // 调节时间列宽
            sheet.setColumnWidth(1, 26 * 256);

            cell = row.createCell((short) i);
            cell.setCellStyle(cstyle);
            cell.setCellValue(new XSSFRichTextString(title[i]));
        }
        return sheet;
    }


    private static SXSSFSheet createSheetBig(SXSSFWorkbook workbook, String[] title, int x) {
        SXSSFSheet sheet = workbook.createSheet("start-" + (x + 1));

        // 设置style
        CellStyle cstyle = workbook.createCellStyle();
        cstyle.setAlignment(HorizontalAlignment.CENTER);
        cstyle.setVerticalAlignment(VerticalAlignment.CENTER);
        Font font = workbook.createFont();

        font.setFontHeightInPoints((short) 10);
        font.setFontName("微软雅黑");
        font.setBold(true);
        cstyle.setFont(font);

        SXSSFRow row;
        SXSSFCell cell;
        row = sheet.createRow((short) 0);
        for (int i = 0; title != null && i < title.length; i++) {
            // 对太宽的列做限制
            sheet.setColumnWidth(2, 20*256);
//            if (sheet.getColumnWidth(i) > 6000) {
//                sheet.setColumnWidth(i, 6000);
//            }

            cell = row.createCell((short) i);
            cell.setCellStyle(cstyle);
            cell.setCellValue(new XSSFRichTextString(title[i]));
        }

        return sheet;
    }

    private static XSSFSheet createSheet(XSSFWorkbook workbook, String[] title, int x) {
        XSSFSheet sheet = workbook.createSheet("start-" + (x + 1));

        // 设置style
        CellStyle cstyle = workbook.createCellStyle();
        cstyle.setAlignment(HorizontalAlignment.CENTER);
        cstyle.setVerticalAlignment(VerticalAlignment.CENTER);
        XSSFFont font = workbook.createFont();

        font.setFontHeightInPoints((short) 10);
        font.setFontName("微软雅黑");
        font.setBold(true);
        cstyle.setFont(font);

        XSSFRow row;
        XSSFCell cell;
        row = sheet.createRow((short) 0);
        for (int i = 0; title != null && i < title.length; i++) {
            //根据一列数据中的最长的字符串长度设置宽度
            sheet.autoSizeColumn(i);
            sheet.setColumnWidth(i, sheet.getColumnWidth(i) * 18 / 10);

            cell = row.createCell((short) i);
            cell.setCellStyle(cstyle);
            cell.setCellValue(new XSSFRichTextString(title[i]));
        }

        return sheet;
    }

    private static XSSFSheet createSheet1(XSSFWorkbook workbook, String[] title, String name) {
        XSSFSheet sheet = workbook.createSheet(name);

        // 设置style
        CellStyle cstyle = workbook.createCellStyle();
        cstyle.setAlignment(HorizontalAlignment.CENTER);
        cstyle.setVerticalAlignment(VerticalAlignment.CENTER);
        XSSFFont font = workbook.createFont();

        font.setFontHeightInPoints((short) 10);
        font.setFontName("微软雅黑");
        font.setBold(true);
        cstyle.setFont(font);

        XSSFRow row;
        XSSFCell cell;
        row = sheet.createRow((short) 0);
        for (int i = 0; title != null && i < title.length; i++) {
            //根据一列数据中的最长的字符串长度设置宽度
            sheet.autoSizeColumn(i);
            sheet.setColumnWidth(i, sheet.getColumnWidth(i) * 18 / 10);

            cell = row.createCell((short) i);
            cell.setCellStyle(cstyle);
            cell.setCellValue(new XSSFRichTextString(title[i]));
        }

        return sheet;
    }

}
