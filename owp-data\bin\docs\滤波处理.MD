# 滤波处理
## Python 脚本
```python
import logging

from gevent import pywsgi
import numpy as np
from flask import Flask, jsonify, request
from scipy.fft import fft
from scipy.signal import butter, filtfilt

app = Flask(__name__)

# 配置日志记录
logging.basicConfig(level=logging.ERROR)


@app.route("/api/frequency/basic/process", methods=["POST"])
def basic_frequency_process():
    try:
        # 1. 验证请求数据
        json_data = request.get_json()
        if json_data is None:
            return jsonify({"code": 400, "msg": "No JSON data provided"}), 400

        # 2. 验证必需字段
        required_fields = ["data", "low", "high", "fs"]
        for field in required_fields:
            if field not in json_data:
                return (
                    jsonify({"code": 400, "msg": f"Missing required field: {field}"}),
                    400,
                )

        # 3. 验证数据类型
        if not isinstance(json_data["data"], list):
            return jsonify({"code": 400, "msg": "Field 'data' must be a list"}), 400

        # 4. 处理数据
        original_array = np.array(json_data["data"], dtype=float)

        # 5. 计算平均值
        average = np.mean(original_array)
        second_column = original_array - average

        # 6. 巴特沃斯带通滤波
        def butter_bandpass(lowcut, highcut, fs, order=5):
            nyquist = 0.5 * fs
            low = lowcut / nyquist
            high = highcut / nyquist
            b, a = butter(order, [low, high], btype="bandpass")
            return b, a

        def bandpass_filter(data, lowcut, highcut, fs, order=4):
            b, a = butter_bandpass(lowcut, highcut, fs, order=order)
            y = filtfilt(b, a, data)
            return y

        # 7. 应用滤波并计算FFT
        filtered_data = bandpass_filter(
            second_column, json_data["low"], json_data["high"], json_data["fs"]
        )

        # 8. 执行FFT并找到峰值频率
        fft_filtered_data = fft(filtered_data)
        freqs = np.fft.fftfreq(len(fft_filtered_data), d=1 / json_data["fs"])
        magnitude = np.abs(fft_filtered_data)[: len(fft_filtered_data) // 2]
        peak_index = np.argmax(magnitude)
        peak_freq = abs(freqs[peak_index])

        return jsonify({"code": 200, "data": peak_freq})

    except ValueError as ve:
        logging.error(f"Value error: {str(ve)}")
        return jsonify({"code": 400, "msg": f"Invalid data format: {str(ve)}"}), 400

    except TypeError as te:
        logging.error(f"Type error: {str(te)}")
        return jsonify({"code": 400, "msg": f"Type mismatch: {str(te)}"}), 400

    except Exception as e:
        logging.exception("Unhandled exception occurred")
        return jsonify({"code": 500, "msg": f"Internal server error: {str(e)}"}), 500


if __name__ == "__main__":
    server = pywsgi.WSGIServer(("127.0.0.1", 5000), app)
    server.serve_forever()

```

## 打包
``` bash
# 创建环境
conda create -n filter python=2.9

# 激活环境
conda activate filter

# 安装依赖
pip install flask numpy scipy pyinstaller

# 打包（Linux/Windows）
pyinstaller --onefile dsp.py --hidden-import=gevent.pywsgi --hidden-import=scipy.fft --hidden-import=scipy.signal --hidden-import=numpy.core --hidden-import=numpy.fft
pyinstaller.exe --onefile dsp.py --hidden-import=gevent.pywsgi --hidden-import=scipy.fft --hidden-import=scipy.signal --hidden-import=numpy.core --hidden-import=numpy.fft
```
