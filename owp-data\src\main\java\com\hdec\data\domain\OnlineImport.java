package com.hdec.data.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 在线数据录入Vo类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class OnlineImport {

    /** 测点ID */
    private Integer pointId;

    /** 属性ID */
    private Integer attrId;

    /** 方向 */
    private Integer direction;

    /** 时间 */
    private String time;

    /** 属性值 */
    private Float attrVal;

    public OnlineImport(Integer pointId, Integer attrId, Integer direction, String time, Float attrVal) {
        this.pointId = pointId;
        this.attrId = attrId;
        this.direction = direction;
        this.time = time;
        this.attrVal = attrVal;
    }
}
