package com.hdec.data.mapper;

import com.hdec.data.domain.Outline;
import com.hdec.data.domain.template.Mould;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 模板Dao层
 *
 * <AUTHOR>
 */
@Mapper
public interface ReportMouldMapper {

    /**
     * 模板列表
     */
    List<Mould> list(@Param("fieldNum") String fieldNum,
                     @Param("name") String name,
                     @Param("type") String type,
                     @Param("label") String label);
    /**
     * 风场下所有模板
     */
    List<Mould> listByField(@Param("fieldNum") String fieldNum);

    /**
     * 按名称查找模板
     */
    Mould selectByName(@Param("id") Integer id, @Param("fieldNum") String fieldNum, @Param("name") String name);

    /**
     * 更新模板
     */
    void update(@Param("mould") Mould mould);

    /**
     * 新增模板
     */
    void add(@Param("mould") Mould mould);

    /**
     * 批量新增模板
     */
    void adds(@Param("moulds") List<Mould> moulds);

    /**
     * 按ID获取模板
     */
    Mould getById(@Param("id") Integer id);

    /**
     * 由模板ID获取绑定的大纲ID
     */
    List<Integer> getOutlineIdByMouldId(@Param("mouldId") Integer MouldId);

    /**
     * 获取使用了该模板的大纲列表
     */
    List<Outline> getOutlineByMouldIds(@Param("mouldIds") Set<Integer> mouldIds);

    /**
     * 批量删除
     */
    void delete(@Param("ids") Integer[] ids);

    /**
     * 获取所有模板标签
     */
    List<String> getAllMouldLabels(@Param("fieldNum") String fieldNum);

    /**
     * 批量获取模板
     */
    List<Mould> getByIds(@Param("mouldIds") Integer[] mouldIds);

}
