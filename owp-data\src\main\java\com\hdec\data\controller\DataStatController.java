package com.hdec.data.controller;

import com.alibaba.fastjson.JSON;
import com.hdec.common.constant.Constant;
import com.hdec.common.domain.R;
import com.hdec.common.util.CommonUtil;
import com.hdec.data.qo.ExistDataQo;
import com.hdec.data.qo.StatQo;
import com.hdec.data.service.DataStatService;
import com.hdec.data.util.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 数据统计控制器
 *
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "测值统计")
@Validated
@RestController
@RequestMapping("api/data/stat")
public class DataStatController {

    @Autowired
    private DataStatService statService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 特征值统计查询(表)
     */
    @ApiOperation("特征值统计查询(表)")
    @PostMapping("query/table")
    public R queryTable(@RequestHeader("fieldNum") String fieldNum, @RequestBody StatQo qo) {
        /* 统计表不存在就创建 */
        statService.createStatTableIfNotExist(fieldNum, qo.getPointIds().get(0), qo.getAttrId());

        /* 获取统计数据 */
        Object statVo = statService.getStatData(fieldNum, qo);
        return R.success(statVo);
    }

    /**
     * 导出Excel(提交参数)
     */
    @ApiOperation("导出Excel(提交参数)")
    @PostMapping("export")
    public R exportSubmitParam(@RequestHeader("fieldNum") String fieldNum, @RequestBody StatQo qo) {
        qo.setFieldNum(fieldNum);

        /* 参数保存至缓存，并设置失效时间 */
        String uuid = CommonUtil.uuid();
        redisTemplate.opsForValue().set(Constant.CACHE_EXPORT_PRE + uuid, JSON.toJSONString(qo), Constant.CACHE_EXPORT_EXPIRE_HOURS, TimeUnit.HOURS);
        return R.success(uuid);
    }

    /**
     * 导出Excel(启动下载)
     */
    @ApiOperation("导出Excel(启动下载)")
    @GetMapping("export/{uuid}")
    public void exportStartDownload(@PathVariable String uuid, HttpServletResponse response) throws Exception {
        /* 从缓存中取出参数 */
        String paramJson = (String) redisTemplate.opsForValue().get(Constant.CACHE_EXPORT_PRE + uuid);
        if (ObjectUtils.isEmpty(paramJson)) {
            response.setContentType("text/html;charset=utf-8");
            response.getWriter().println("链接已失效（" + Constant.CACHE_EXPORT_EXPIRE_HOURS + "小时），请重新导出");
            return;
        }

        ExcelUtil.exportByte(response, "测值统计");
        statService.exportExcel(JSON.parseObject(paramJson, StatQo.class), response);
    }

    /**
     * 存量数据甘特图
     */
    @ApiOperation("存量数据甘特图")
    @PostMapping("existDataChart")
    public R existDataChart(@RequestHeader("fieldNum") String fieldNum, @RequestBody ExistDataQo qo) {
        Map<String, Object> map = statService.existDataChart(fieldNum, qo);
        return R.success(map);
    }

}
