package com.hdec.data.feign;

import com.hdec.common.domain.*;
import com.hdec.common.qo.InstPathQo;
import com.hdec.common.vo.AlarmAdvancedRuleVo;
import com.hdec.common.vo.InstDirectAttr;
import com.hdec.common.vo.PointDirectVo;
import com.hdec.common.vo.TabVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * RPC - 监测服务
 *
 * <AUTHOR>
 */
@FeignClient(name = "owp-monitor")
public interface MonitorService {

    @GetMapping(value = "api/monitor/attr/list/online/{pointId}")
    List<AttrCommon> listAttrOnline(@PathVariable(value = "pointId") Integer pointId);

    @PostMapping(value = "/api/monitor/measurePoint/getPointNos")
    List<String> getPointNos(@RequestBody Integer[] pointIds);

    @GetMapping(value = "/api/monitor/measurePoint/getNoById/{pointId}")
    String getNoById(@PathVariable(value = "pointId") Integer pointId);

    /**
     * 由测点编号查询测点ID
     */
    @PostMapping(value = "/api/monitor/measurePoint/searchPointIdByNo")
    Integer searchPointIdByNo(@RequestParam("fieldNum") String fieldNum, @RequestParam("pointNo") String pointNo);

    @GetMapping(value = "/api/monitor/measurePoint/getInstallElevation/{pointId}")
    String getInstallElevation(@PathVariable(value = "pointId") Integer pointId);

    @GetMapping(value = "/api/monitor/measurePoint/getSameGroupPoints/{pointId}")
    List<PointCommon> getSameGroupPoints(@PathVariable(value = "pointId") Integer pointId);

    @GetMapping(value = "/api/monitor/measurePoint/getAngel/{pointId}")
    Float getAngel(@PathVariable(value = "pointId") Integer pointId);

    @GetMapping(value = "/api/monitor/alarm/fieldRules/{fieldNum}")
    Map<String, AlarmRuleVo> fieldRules(@PathVariable(value = "fieldNum") String fieldNum);

    @GetMapping(value = "/api/monitor/alarm/fieldAdvancedRules/{fieldNum}")
    Map<String, AlarmAdvancedRuleVo> fieldAdvancedRules(@PathVariable(value = "fieldNum") String fieldNum);

    @GetMapping(value = "/api/monitor/formula/fieldFormulas/{fieldNum}")
    List<FormulaCommon> fieldFormulas(@PathVariable(value = "fieldNum") String fieldNum);

    @PostMapping(value = "/api/monitor/formula/updateReApplyStatus")
    void updateReApplyStatus(@RequestBody ProcessQo qo);

    /**
     * 保存告警
     */
    @PostMapping(value = "/api/monitor/alarm/saveAlarm")
    void saveAlarm(@RequestBody Alarm alarm);

    /**
     * 告警批量入库
     */
    @PostMapping(value = "/api/monitor/alarm/saveAlarms")
    void saveAlarms(@RequestBody List<Alarm> alarms);

    /**
     * 由测点ID获取测点绑定仪器的ID、方向、属性
     */
    @GetMapping(value = "/api/monitor/measurePoint/getInstDirectAttr/{pointId}")
    InstDirectAttr getInstDirectAttr(@PathVariable("pointId") Integer pointId);

    @GetMapping(value = "/api/monitor/measurePoint/getInstDirectAttrByRate/{pointId}/{rate}")
    InstDirectAttr getInstDirectAttrByRate(@PathVariable("pointId") Integer pointId, @PathVariable("rate") String rate);

    /**
     * 由测点ID获取测点绑定仪器的属性并按频率分组
     */
    @GetMapping(value = "/api/monitor/measurePoint/getInstAttrs/{pointId}")
    InstDirectAttr getInstAttrs(@PathVariable("pointId") Integer pointId);

    /**
     * 由测点ID获取测点绑定仪器的属性并按频率分组
     */
    @GetMapping(value = "/api/monitor/measurePoint/getInstAttrsByRate/{pointId}/{rate}")
    InstDirectAttr getInstAttrsByRate(@PathVariable("pointId") Integer pointId, @PathVariable("rate") String rate);

    /**
     * 由分量ID查询其下所有测点
     */
    @GetMapping(value = "/api/monitor/measurePoint/getPointIdsByAttr/{attrId}")
    List<PointDirectVo> getPointIdsByAttr(@PathVariable("attrId") Integer attrId);

    /**
     * 由分量ID查询分量名称
     */
    @GetMapping(value = "api/monitor/attr/getProAttrById/{attrId}")
    AttrCommon getProAttrById(@PathVariable(value = "attrId") Integer attrId);

    /**
     * 获取分量相关信息
     */
    @GetMapping(value = "api/monitor/attr/getAttrInfo/{attrId}/{condition}")
    AttrInfo getAttrInfo(@PathVariable(value = "attrId") Integer attrId, @PathVariable(value = "condition") String condition);

    /**
     * 获取图形分析参数配置
     */
    @GetMapping(value = "api/monitor/config/graphAnalysis/map")
    Map<Integer, GraphAnalysis> graphAnalysisMap();

    @GetMapping(value = "api/monitor/config/pro/graphAnalysis/map/{fieldNum}")
    Map<Integer, GraphAnalysis> proGraphAnalysisMap(@PathVariable("fieldNum") String fieldNum);

    /**
     * 获取某风场下所有测点
     */
    @GetMapping(value = "/api/monitor/measurePoint/getPointsByFieldNum/{fieldNum}")
    List<PointCommon> getPointsByFieldNum(@PathVariable("fieldNum") String fieldNum);

    /**
     * 获取某仪器下所有测点
     */
    @GetMapping(value = "/api/monitor/measurePoint/getPointsByInst/{instId}")
    List<PointCommon> getPointsByInst(@PathVariable("instId") Integer instId);

    /**
     * 获取某风场下所有参数
     */
    @GetMapping(value = "/api/monitor/measurePointParam/allParamsByField/{fieldNum}")
    List<PointParamCommon> allParamsByField(@PathVariable("fieldNum") String fieldNum);

    /**
     * 获取某风场下所有分量
     */
    @GetMapping(value = "/api/monitor/attr/pro/allAttr/{fieldNum}")
    List<AttrCommon> allAttrByField(@PathVariable("fieldNum") String fieldNum);

    /**
     * 获取某风场下所有分量(带仪器信息)
     */
    @GetMapping(value = "/api/monitor/attr/pro/allAttrWithInst/{fieldNum}")
    List<AttrCommon> allAttrWithInstByField(@PathVariable("fieldNum") String fieldNum);

    /**
     * 获取某风场下所有仪器
     */
    @GetMapping(value = "/api/monitor/instrument/pro/allInst/{fieldNum}")
    List<InstCommon> allInstByField(@PathVariable("fieldNum") String fieldNum);

    /**
     * 获取某风场下所有仪器
     */
    @GetMapping(value = "api/monitor/measurePoint/preTabListRemote/{type}")
    TabVo preTabListRemote(@PathVariable("type") Integer type);

    /**
     * 获取所有高频分量
     */
    @GetMapping(value = "/api/monitor/attr/getAllHighAttrs")
    List<AttrCommon> getAllHighAttrs();

    /**
     * 获取另一个风场中的相同仪器
     */
    @GetMapping(value = "api/monitor/instrument/pro/getOtherFieldInst/{instId}/{fieldNum}")
    InstCommon getOtherFieldInst(@PathVariable("instId") Integer instId, @PathVariable("fieldNum") String fieldNum);

    /**
     * 获取另一个风场中的相同仪器
     */
    @GetMapping(value = "api/monitor/attr/pro/getOtherFieldAttr/{instId}/{attrId}")
    AttrCommon getOtherFieldAttr(@PathVariable("instId") Integer instId, @PathVariable("attrId") Integer attrId);

    @GetMapping(value = "api/monitor/attr/pro/getInstIdByAttrId/{attrId}")
    Integer getInstIdByAttrId(@PathVariable("attrId") Integer attrId);

    @GetMapping(value = "api/monitor/instrument/getMonitorIdByInstId/{attrId}")
    Integer getMonitorIdByInst(@PathVariable("attrId") Integer instId);

    /**
     * 由路径获取测点挂载情况
     */
    @PostMapping(value = "api/monitor/collectInstMount/getCollectMountsByPath")
    List<CollectInstMountCommon> getCollectMountsByPath(@RequestBody InstPathQo qo);

    @GetMapping(value = "api/monitor/instrument/pro/getInstDirectById/{instId}")
    String getInstDirectById(@PathVariable("instId") Integer instId);


    @GetMapping(value = "api/monitor/collectInstMount/getFieldCollectInst/{fieldNum}")
    List<CollectInstCommon> getFieldCollectInst(@PathVariable("fieldNum") String fieldNum);

    @PostMapping(value = "api/monitor/collectInstMount/getCollectInstMount")
    List<CollectInstMountCommon> getCollectInstMount(@RequestBody List<Integer> instIds);

    /**
     * 获取某监测项目下所有测点
     */
    @GetMapping(value = "/api/monitor/measurePoint/getPointsByMonitor/{fieldNum}/{monitorId}")
    List<PointCommon> getPointsByMonitor(@PathVariable("fieldNum") String fieldNum, @PathVariable("monitorId") Integer monitorId);


    /**
     * 获取仪器下所有参数
     */
    @GetMapping(value = "/api/monitor/parameter/pro/getAllByInst/{instId}")
    List<ParameterCommon> getParamsProByInstId(@PathVariable("instId") Integer instId);

    /**
     * 获取某风场下所有参数
     */
    @GetMapping(value = "/api/monitor/measurePointParam/getByParamId/{paramId}")
    List<PointParamCommon> getMeasurePointParamByParamId(@PathVariable("paramId") Integer paramId);


    @GetMapping(value = "/api/monitor/alarm/rule/ddc/all/{fieldNum}")
    List<AlarmDdcRuleCommon> getAlarmDdcRules(@PathVariable("fieldNum") String fieldNum);

    @GetMapping("api/monitor/formula/getAllPointFormulas")
    List<FormulaCommon> getAllPointFormulas();

    @GetMapping("api/monitor/attr/accessInfo/pro/feignGet")
    List<AttrCommon> accessInfoPro(@RequestParam("instId") Integer instId);
}
