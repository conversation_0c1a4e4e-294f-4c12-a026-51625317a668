package com.hdec.data.netty;

import cn.hutool.core.util.ByteUtil;
import com.hdec.common.domain.msg.Inst;
import com.hdec.data.util.HexUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 消息处理类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MsgService {

    /**
     * 报文解析
     */
    public static List<Inst> parse(byte[] bytes) {
        if (bytes.length < 4) {
            log.error("异常报文:{}", HexUtil.bytesToHex(bytes));
            return Collections.emptyList();
        }

        String startHex = HexUtil.bytesToHex(getBytesByIndex(bytes, 0, 2));
        if (startHex.startsWith("EB92")) {
            log.info("收到设备{}的心跳包", ByteUtil.bytesToShort(getBytesByIndex(bytes, 2, 4)));
            return Collections.emptyList();
        }

        /* 校验报文 */
        if (startHex.startsWith("EB90")) {
            List<Inst> instList = new ArrayList<>();
            /* 一帧包含2个数据包 */
            handlePacket(getBytesByIndex(bytes, 4, 516), instList);
            handlePacket(getBytesByIndex(bytes, 516, 1028), instList);
            setDeviceNoAndTime(getBytesByIndex(bytes, 6, 14), getBytesByIndex(bytes, 518, 526), getBytesByIndex(bytes, 18, 20), instList);
            return instList;
        }
        return Collections.emptyList();
    }

    /**
     * 设置设备编号和时间
     */
    private static void setDeviceNoAndTime(byte[] timeBytes1, byte[] timeBytes2, byte[] deviceBytes, List<Inst> instList) {
        short deviceNo = ByteUtil.bytesToShort(deviceBytes);
        Date[] dates = {new Date(ByteUtil.bytesToLong(timeBytes1)), new Date(ByteUtil.bytesToLong(timeBytes2))};
        for (int i = 0; i < instList.size(); i++) {
            Inst inst = instList.get(i);
            inst.setDeviceNo(inst.getNo() == 0 ? String.valueOf(deviceNo) : deviceNo + "_" + inst.getNo());
            inst.setTime(dates[i % 2]);
        }
    }

    /**
     * 处理数据包
     */
    private static void handlePacket(byte[] bytes, List<Inst> instList) {
        // 总共有8个从机
        int instCount = 8;
        for (int i = 0; i < instCount; i++) {
            Inst inst = new Inst(i);
            int start = i * 64;
            byte[] instBytes = getBytesByIndex(bytes, start, start + 64);
            handleInstMsg(instBytes, inst, instList);
        }
    }

    /**
     * 处理采集仪数据
     */
    private static void handleInstMsg(byte[] bytes, Inst inst, List<Inst> instList) {
        byte status = getBytesByIndex(bytes, 1, 2)[0];
        if (status != 0) {
            return;
        }

        Map<Integer, Float[]> channels = new HashMap<>(12);
        int channelCount = 12;
        for (int i = 0; i < channelCount; i++) {
            int start = 16 + i * 4;
            Float value = ByteUtil.bytesToInt(getBytesByIndex(bytes, start, start + 4)) / 8388608.5f;
            channels.put(i, new Float[]{value});
        }

        inst.setChannelData(channels);
        instList.add(inst);
    }

    /**
     * 按下标获取子字节数组
     */
    public static byte[] getBytesByIndex(byte[] bytes, int start, int end) {
        int len = end - start;
        byte[] target = new byte[len];
        System.arraycopy(bytes, start, target, 0, len);
        return target;
    }

}
