package com.hdec.data.mapper;

import com.hdec.data.domain.HighTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Set;

@Mapper
public interface HighTaskMapper {

    /**
     * 批量插入
     */
    void addBatch(@Param("tasks") Set<HighTask> highTasks);

    /**
     * 查询
     */
    HighTask select(@Param("task") HighTask task);


    /**
     * 重置任务状态
     */
    void resetTaskStatus();

    HighTask selectFirstTaskByLevel(@Param("type") String type);

    void update(@Param("task") HighTask task);

    void delete(@Param("id") Integer id);

    void delOldData(@Param("days") int days);
}
