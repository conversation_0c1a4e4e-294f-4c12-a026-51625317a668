package com.hdec.data.qo;

import com.hdec.common.util.TimeUtil;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class DataImportQo {

    /** 当前页 */
    private Integer pageNum;

    /** 每页条数 */
    private Integer pageSize;

    private Boolean isBatch;

    /** 导入类型 */
    private Integer status;

    /** 模糊搜索关键字 */
    private String keyword;

    /** 开始时间 */
    private String startTime;

    /** 结束时间 */
    private String endTime;

    /** 导入方式 */
    private Integer type;

    /** 导入结果 */
    private String importRes;

    public String getStartTime() {
        if (!ObjectUtils.isEmpty(startTime)) {
            return TimeUtil.completeStart(startTime);
        }
        return startTime;
    }

    public String getEndTime() {
        if (!ObjectUtils.isEmpty(endTime)) {
            return TimeUtil.completeEnd(endTime);
        }
        return endTime;
    }

}
