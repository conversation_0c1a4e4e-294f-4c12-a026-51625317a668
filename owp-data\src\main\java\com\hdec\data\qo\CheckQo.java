package com.hdec.data.qo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * 校验查询类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class CheckQo {

    /** 仪器ID */
    private Integer instId;

    /** 测点编号数组 */
    private List<Integer> pointIds;

    /** 开始时间 */
    private String startTime;

    /** 结束时间 */
    private String endTime;

}
