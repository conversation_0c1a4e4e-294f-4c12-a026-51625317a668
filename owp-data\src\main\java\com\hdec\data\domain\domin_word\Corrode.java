package com.hdec.data.domain.domin_word;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2022/12/28
 */
@Data
@ToString
@NoArgsConstructor
public class Corrode {

    /** 测点编号 */
    private String pointNo;

    /** 平均值 */
    private Double avg;

    public Corrode(String pointNo, Double avg) {
        this.pointNo = pointNo;
        this.avg = avg;
    }
}
