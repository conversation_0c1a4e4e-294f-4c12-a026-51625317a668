package com.hdec.data.util;

import com.alibaba.fastjson.serializer.ValueFilter;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * Double类型格式化（保留小数位数）
 */
@Component
public class DoubleValueFilter implements ValueFilter {

    @Override
    public Object process(Object object, String name, Object value) {
        if (value == null || !(value instanceof List)) {
            return value;
        }

        List<Double> nums = (List<Double>) value;
        List<Double> newNums = new ArrayList<>(nums.size());
        for (int i = 0; i < nums.size(); i++) {
            String num = nums.get(i).toString();
            if (num.substring(num.indexOf(".") + 1).length() <= 4) {
                newNums.add(Double.parseDouble(num));
            } else {
                newNums.add(Double.parseDouble(num.substring(0, num.indexOf(".") + 5)));
            }
        }
        return newNums;
    }

}