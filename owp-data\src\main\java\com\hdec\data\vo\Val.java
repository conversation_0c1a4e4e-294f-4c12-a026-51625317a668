package com.hdec.data.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hdec.data.service.DateSerializer;
import com.hdec.data.service.FloatSerializer;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * 时间和值实体类
 */
@Data
@ToString
@NoArgsConstructor
public class Val {

    /** 时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    @JsonSerialize(using = DateSerializer.class)
    private Date t;

    /** 值 */
    @JsonSerialize(using = FloatSerializer.class)
    private Float v;

    public Val(Date t, Float v) {
        this.t = t;
        this.v = v;
    }
}
