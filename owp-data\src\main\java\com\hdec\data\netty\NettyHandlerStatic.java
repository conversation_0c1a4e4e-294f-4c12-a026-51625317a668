package com.hdec.data.netty;

import cn.hutool.core.util.ByteUtil;
import com.hdec.common.domain.msg.Inst;
import com.hdec.data.domain.Bean;
import com.hdec.data.service.DataAccessService;
import com.hdec.data.service.KafkaService;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * DM客户端消息处理类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class NettyHandlerStatic extends ChannelInboundHandlerAdapter {

    private static NettyHandlerStatic handler;

    @PostConstruct
    public void init() {
        handler = this;
    }

    @Autowired
    private KafkaService kafkaService;

    @Resource
    private DataAccessService accessService;

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) {
        ByteBuf byteBuf = (ByteBuf) msg;
        byte[] bytes = new byte[byteBuf.readableBytes()];
        byteBuf.readBytes(bytes);
        byteBuf.release();

        List<Inst> instList = parsePackage(bytes);
        response(ctx, getBytesByIndex(bytes, 6, 14));

//        handler.kafkaService.consume(instList, "static");
        handler.accessService.input(instList, "static");
    }

    /**
     * 解析数据包
     */
    public List<Inst> parsePackage(byte[] bytes) {
        List<Inst> instList = new ArrayList<>();

        String deviceNo = String.valueOf(ByteUtil.bytesToShort(getBytesByIndex(bytes, 2, 4)));

        // 计算总共有几包
        int packageSize = (bytes.length - 6) / 22;

        List<Bean> beans = parseChannelDataMap(bytes, packageSize);

        Map<Short, List<Bean>> subDeviceChannelData = beans.stream().collect(Collectors.groupingBy(Bean::getSubDeviceNo));
        subDeviceChannelData.forEach((subDeviceNo, channelData) -> {
            Inst inst = new Inst();
            instList.add(inst);
            inst.setDeviceNo(deviceNo + "_" + subDeviceNo);
            inst.setTime(new Date(channelData.get(0).getTs()));

            Map<Integer, Float[]> dataMap = new HashMap<>();
            for (Bean channelDatum : channelData) {
                dataMap.put(channelDatum.getChannel().intValue() - 1, channelDatum.getVs());
            }
            inst.setChannelData(dataMap);
        });
        return instList;
    }

    /**
     * 解析通道数据
     */
    private List<Bean> parseChannelDataMap(byte[] bytes, int packageSize) {
        List<Bean> beans = new ArrayList();
        for (int i = 0; i < packageSize; i++) {
            int start = i * 22;

            long ts = ByteUtil.bytesToLong(getBytesByIndex(bytes, start + 6, start + 14));
            short subDeviceNo = ByteUtil.bytesToShort(getBytesByIndex(bytes, start + 14, start + 16));
            short channel = ByteUtil.bytesToShort(getBytesByIndex(bytes, start + 16, start + 18));
            float v1 = ByteUtil.bytesToFloat(getBytesByIndex(bytes, start + 20, start + 24));
            float v2 = ByteUtil.bytesToFloat(getBytesByIndex(bytes, start + 24, start + 28));

            beans.add(new Bean(ts, subDeviceNo, channel, new Float[]{v1, v2}));
        }
        return beans;
    }

    private void response(ChannelHandlerContext ctx, byte[] resBytes) {
        ctx.writeAndFlush(Unpooled.copiedBuffer(resBytes));
    }

    /**
     * 按下标获取子字节数组
     */
    public static byte[] getBytesByIndex(byte[] bytes, int start, int end) {
        int len = end - start;
        byte[] target = new byte[len];
        System.arraycopy(bytes, start, target, 0, len);
        return target;
    }

//    log.info("时间戳：{}", ByteUtil.bytesToLong(getBytesByIndex(bytes, start + 6, start + 14)));
//    log.info("从机号：{}", ByteUtil.bytesToShort(getBytesByIndex(bytes, start + 14, start + 16)));
//    log.info("通道号：{}", ByteUtil.bytesToShort(getBytesByIndex(bytes, start + 16, start + 18)));
//    log.info("传感器类型：{}", ByteUtil.bytesToShort(getBytesByIndex(bytes, start + 18, start + 20)));
//    log.info("参数1：{}", ByteUtil.bytesToFloat(getBytesByIndex(bytes, start + 20, start + 24)));
//    log.info("参数2：{}", ByteUtil.bytesToFloat(getBytesByIndex(bytes, start + 24, start + 28)));

}
