package com.hdec.data.domain.domin_word;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * 导出报告所需信息
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class ReportInfo {

    /** 风场名称 */
    private String fieldName;

    /** 哪一年 */
    private Integer year;

    /** 第几期 */
    private Integer phaseNum;
    private Integer totalPhaseNum;

    /** 开始时间-年 */
    private Integer startYear;

    /** 开始时间-月 */
    private Integer startMonth;

    /** 开始时间-日 */
    private Integer startDay;

    /** 结束时间-年 */
    private Integer endYear;

    /** 结束时间-月 */
    private Integer endMonth;

    /** 结束时间-日 */
    private Integer endDay;

    /** 编写时间-年 */
    private Integer writeTimeYear;

    /** 编写时间-月 */
    private Integer writeTimeMonth;

    /** 编写时间-日 */
    private Integer writeTimeDay;

    /** 编写者 */
    private String editor;

    /** 校对者 */
    private String checker;

    /** 审核者 */
    private String reviewer;

    /** 公司 */
    private String company;

    /** 部门 */
    private String dept;

    private List<Map<String, List<Map<String, Object>>>> projectOverviews;
    private List<Map<String, List<Map<String, Object>>>> monitorOverviews;

    /** 典型风机 */
    List<ReportTypicalFanInfo> typicalFanInfos;

    /** 非典型风机(振动倾斜) */
    ReportTypicalFanInfo nonTypicalFanInfo;

    /** 非典型风机(腐蚀) */
    List<ReportNonTypicalFanInfo> nonTypicalFanInfos;


}
