package com.hdec.data.mapper;

import com.hdec.data.domain.template.ReportVar;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 报告变量Dao层
 *
 * <AUTHOR>
 */
@Mapper
public interface VarMapper {

    /**
     * 报告变量列表
     */
    List<ReportVar> list();

    /**
     * 查询
     */
    List<ReportVar> select(@Param("var") ReportVar var);

    /**
     * 保存
     */
    void save(@Param("var") ReportVar var);

    /**
     * 删除
     */
    void delete(@Param("ids") Integer[] ids);

    /**
     * 获取某风场下自定义变量
     */
    List<ReportVar> getCustomVars(@Param("fieldNum") String fieldNum);

    /**
     * 由自定义变量名查询其下测点ID
     */
    ReportVar getPointIdsByVarName(@Param("fieldNum") String fieldNum, @Param("varName") String varName);

    /**
     * 修改自定义变量
     */
    void update(@Param("var") ReportVar var);
}
