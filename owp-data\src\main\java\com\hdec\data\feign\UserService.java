package com.hdec.data.feign;

import com.hdec.common.config.FeignConfiguration;
import com.hdec.common.domain.OperaLog;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * RPC - 用户服务
 *
 * <AUTHOR>
 */
@FeignClient(name = "owp-user", configuration = FeignConfiguration.class)
public interface UserService {

    @GetMapping(value = "api/user/unit/saveFieldResource/{fieldNum}/{userId}")
    void saveFieldResource(@PathVariable("fieldNum") String fieldNum, @PathVariable("userId") Integer userId);

    /**
     * 保存操作日志
     */
    @PostMapping(value = "/api/user/operaLog/saveOperaLog")
    void saveOperaLog(@RequestBody OperaLog log);

    @GetMapping(value = "api/user/unit/getFieldResource/{unitId}")
    List<String> getFieldResource(@PathVariable(value = "unitId") Integer unitId);

}
