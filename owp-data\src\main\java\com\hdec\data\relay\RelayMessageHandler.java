package com.hdec.data.relay;


import com.hdec.common.domain.msg.Inst;
import com.hdec.data.netty.NettyHandlerStatic;
import com.hdec.data.service.DataAccessService;
import com.hdec.data.util.HexUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.integration.mqtt.support.MqttHeaders;
import org.springframework.lang.NonNull;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHandler;
import org.springframework.messaging.MessagingException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
@ConditionalOnProperty(value = "relay.mqtt.enable", havingValue = "true", matchIfMissing = false)
public class RelayMessageHandler implements MessageHandler {

    private static final Logger logger = LoggerFactory.getLogger(RelayMessageHandler.class);

    @Resource
    private NettyHandlerStatic nettyHandlerStatic;

    @Resource
    private DataAccessService accessService;

    @ServiceActivator(inputChannel = "relayMqttOutboundChannel")
    @Override
    public void handleMessage(@NonNull Message<?> message) throws MessagingException {
        String topic = (String) message.getHeaders().get(MqttHeaders.RECEIVED_TOPIC);
        byte[] payload = (byte[]) message.getPayload();
        if (topic != null &&
                topic.replace("sensor/", "")
                        .startsWith("static")) {
            /*  静态设备  */
            List<Inst> instList = nettyHandlerStatic.parsePackage(payload);
            accessService.input(instList, "static", false);
        } else {
            /*  未实现的设备  */
            logger.error("未实现的设备: {}-「{}\"", topic, HexUtil.bytesToHex(payload));
        }
    }
}
