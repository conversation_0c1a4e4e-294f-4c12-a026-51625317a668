create stable if not exists redo_job (
    ts timestamp,
    status int,
    create_time timestamp
) TAGS (
    point int
);


create stable if not exists daily_count (
    ts timestamp,
    data_count int
) TAGS (
    point int
);

create stable if not exists daily_stat (
    ts timestamp,
    max_val double,
    max_val_ts timestamp,
    min_val double,
    min_val_ts timestamp
    avg_val double
) TAGS (
    point int,
    attr int,
    direct int
);