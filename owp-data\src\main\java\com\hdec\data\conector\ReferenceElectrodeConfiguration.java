package com.hdec.data.conector;

import com.ghgande.j2mod.modbus.facade.ModbusTCPMaster;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.LinkedHashMap;
import java.util.Map;

@Configuration
@EnableConfigurationProperties({ReferenceElectrodeProperties.class})
@ConditionalOnProperty(value = "connector.reference-electrode.enable", havingValue = "true", matchIfMissing = false)
public class ReferenceElectrodeConfiguration {

    @Bean("referenceDevice")
    public Map<String,ModbusTCPMaster> getModbusTCPMaster(ReferenceElectrodeProperties properties) {
        Map<String, ReferenceElectrodeProperties.ModbusProperties> device = properties.getDevice();
        if (device != null && !device.isEmpty()) {
            Map<String,ModbusTCPMaster> map = new LinkedHashMap<>();
            device.forEach((key, value) -> {
                ModbusTCPMaster master = new ModbusTCPMaster(value.getHost(),value.getPort());
                master.setTimeout(1000);
                map.put(key,master);
            });
            return map;
        }else {
            throw new RuntimeException(" is null");
        }
    }
}
