<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hdec.data.mapper.DataMapper">

    <select id="isTabExists" parameterType="string" resultType="int">
        SELECT
            1
        FROM
            information_schema.TABLES
        WHERE
            TABLE_NAME = #{tabName}
        LIMIT 1
    </select>

    <update id="createTabIfNotExist">
        CREATE TABLE IF NOT EXISTS `${tabName}` (
          `id` int NOT NULL AUTO_INCREMENT,
          `direction` tinyint DEFAULT NULL COMMENT '方向',
          `time` datetime DEFAULT NULL COMMENT '时间',
          `attr_id` int DEFAULT NULL COMMENT '属性ID',
          `attr_val` float(15,6) DEFAULT NULL COMMENT '属性值',
          PRIMARY KEY (`id`)
        ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb3;
    </update>

    <insert id="save">
        INSERT INTO `${tabName}` (
            direction,
            `time`,
            attr_id,
            attr_val
        )
        VALUES (
            #{data.direction},
            #{data.time},
            #{data.attrId},
            #{data.attrVal}
        )
    </insert>

    <insert id="saveBatch">
        INSERT INTO ${tabName} (
            direction,
            `time`,
            attr_id,
            attr_val
        )
        VALUES
        <foreach collection ="data" item="d" separator =",">
            (#{d.direction}, #{d.time}, #{d.attrId}, #{d.attrVal})
        </foreach>
    </insert>

    <select id="queryTableByPoint" resultType="com.hdec.data.domain.Datum">
        SELECT
            *
        FROM
            data_${fieldNum}_${pointId}
        WHERE
            attr_id = #{orderBy}
        ORDER BY attr_val DESC
        LIMIT #{limitSize}
    </select>

    <select id="getTimes" resultType="string">
        SELECT
            `time`
        FROM
            data_${fieldNum}_${pointId}
        WHERE
            `time` BETWEEN #{startTime} AND #{endTime}
        GROUP BY
            `time`
        ORDER BY
            `time`
        LIMIT #{limitSize}
    </select>

    <select id="getValByTime" resultType="com.hdec.data.domain.Datum">
        SELECT
            *
        FROM
            data_${fieldNum}_${pointId}
        WHERE
            `time` = #{time}
    </select>

    <select id="alarmProcessLine" resultType="com.hdec.data.domain.Datum">
        SELECT
            *
        FROM
            data_${fieldNum}_${pointId}
        WHERE
            attr_id = #{attrId}
        AND direction = #{direction}
        AND `time` BETWEEN #{sTime} AND #{eTime}
        <if test="time != null">
            ORDER BY
            abs(
                UNIX_TIMESTAMP(`time`) - UNIX_TIMESTAMP(#{time})
            )
            LIMIT 30
        </if>
    </select>

    <select id="getData" resultType="com.hdec.data.domain.Datum">
        SELECT
            ${pointId} AS point_id, direction, attr_id, attr_val, `time`, verify
        FROM
            data_${fieldNum}_${pointId}
        WHERE
            `time` = #{time}
    </select>

    <select id="selectDataByAttr" resultType="com.hdec.data.domain.Datum">
        SELECT
            `time`, attr_id, direction, attr_val
        FROM
            ${tabName}
        WHERE
            `time` BETWEEN #{startTime} AND #{endTime}
        AND attr_id IN
        <foreach item="attrId" collection="attrIds" open="(" separator="," close=")">
            #{attrId}
        </foreach>
    </select>

    <update id="verify" parameterType="com.hdec.data.qo.VerifyQo">
        UPDATE data_${fieldNum}_${qo.pointId}
        SET verify =
        <if test="qo.isVerify == false">
            0
        </if>
        <if test="qo.isVerify == true">
            1
        </if>
        WHERE
            `time` = #{qo.time}
        AND direction = #{qo.direct}
    </update>

    <delete id="del" parameterType="com.hdec.data.qo.VerifyQo">
        DELETE
        FROM data_${fieldNum}_${qo.pointId}
        WHERE
            `time` = #{qo.time}
        AND direction = #{qo.direct}
    </delete>

    <update id="update" parameterType="com.hdec.data.qo.VerifyQo">
        UPDATE data_${fieldNum}_${qo.pointId}
        SET attr_val = #{qo.newVal}
        WHERE
            `time` = #{qo.time}
        AND direction = #{qo.direct}
    </update>

    <update id="returnBack" parameterType="com.hdec.data.qo.VerifyQo">
        UPDATE data_${fieldNum}_${qo.pointId}
        SET
            attr_val = old_val,
            old_val = null
        WHERE
            `time` = #{qo.time}
        AND direction = #{qo.direct}
    </update>

    <select id="queryChart" resultType="com.hdec.data.vo.DatumVo">
            SELECT
                UNIX_TIMESTAMP(`time`) AS t,
                attr_val AS a
            FROM
                data_owp00006_xxx
            WHERE
                attr_id = 2
            AND `time` BETWEEN '2020-01-01 00:01:00'
            AND '2020-03-11 00:01:00'
    </select>

    <select id="exportTaskList" resultType="com.hdec.data.domain.ExportTask">
        SELECT
            id,
            `user_id`,
            `username`,
            `time`,
            `progress`,
            `url`,
            `rate`,
            `start_time`,
            `end_time`,
            `point_no`,
            `field_num`,
            approval AS approvals
        FROM
            owp_export_task
        WHERE
            field_num = #{fieldNum}
        ORDER BY
            `time` DESC
    </select>

    <insert id="exportTaskAdd" parameterType="com.hdec.data.domain.ExportTask" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO owp_export_task (
            `user_id`,
            `username`,
            `time`,
            `progress`,
            `url`,
            `rate`,
            `start_time`,
            `end_time`,
            `point_no`,
            `approval`,
            `field_num`
        )
        VALUES (
            #{task.userId},
            #{task.username},
            NOW(),
            #{task.progress},
            #{task.url},
            #{task.rate},
            #{task.startTime},
            #{task.endTime},
            #{task.pointNo},
            #{task.approvals},
            #{task.fieldNum}
        )
    </insert>

    <update id="updateExportTaskProgress">
        UPDATE owp_export_task
        SET progress = #{progress}
        WHERE
            id = #{taskId}
    </update>

    <delete id="exportTaskDel">
        DELETE
        FROM
            owp_export_task
        WHERE
            id = #{id}
    </delete>

</mapper>