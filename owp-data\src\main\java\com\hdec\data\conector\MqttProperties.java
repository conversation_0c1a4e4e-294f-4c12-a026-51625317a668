package com.hdec.data.conector;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;
import java.util.Map;

@Data
@ConfigurationProperties(prefix = "mqtt", ignoreUnknownFields = true)
public class MqttProperties {

    private boolean enable = false;

    /**
     * 连接器
     */
    private List<Connector> connectors;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Connector {
        /**
         * MQTT broker
         */
        private String broker;

        /**
         * 用户名
         */
        private String username;

        /**
         * 客户端ID
         */
        private String clientId;

        /**
         * 密码
         */
        private String password;

        /**
         * 清除会话
         */
        private boolean cleanSession = true;

        /**
         * 连接超时时间
         */
        private int connectionTimeout = 10;

        /**
         * 保活
         */
        private int keepAliveInterval = 10;

        /**
         * 自动重连
         */
        private boolean automaticReconnect = true;

        /**
         * 订阅主题
         */
        private Map<String, String> topicMapping;
    }
}
