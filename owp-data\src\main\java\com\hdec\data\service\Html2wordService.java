package com.hdec.data.service;

import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.util.ObjectUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * Html转Word业务处理
 *
 * <AUTHOR>
 */
public class Html2wordService {

    /**
     * 为文档中的tag标签添加样式
     */
    public static void addStyleIfNotExist(Document document, String tag, Map<String, Object> newStyles) {
        Elements ps = document.getElementsByTag(tag);
        for (Element p : ps) {
            Map<String, Object> existStyles = parseStyle2Map(p.attr("style"));
            p.attr("style", combineStyle(existStyles, newStyles));
        }
    }

    /**
     * 合并样式
     */
    public static String combineStyle(Map<String, Object> existStyles, Map<String, Object> newStyles) {
        Map<String, Object> styles = new HashMap<>();

        newStyles.forEach((k, v) -> styles.put(k.trim(), v));
        existStyles.forEach((k, v) -> styles.put(k.trim(), v));

        StringBuilder sb = new StringBuilder();
        styles.forEach((k, v) -> sb.append(k +":" + v + ";"));
        return sb.toString();
    }

    /**
     * 解析style属性成Map
     */
    public static Map<String, Object> parseStyle2Map(String style) {
        Map<String, Object> map = new HashMap<>();
        if (ObjectUtils.isEmpty(style)) {
            return map;
        }

        String[] styles = style.split(";");
        if (ObjectUtils.isEmpty(styles)) {
            return map;
        }

        for (String s : styles) {
            String[] arr = s.split(":");
            if (arr.length == 2) {
                map.put(arr[0], arr[1]);
            }
        }
        return map;
    }

}
