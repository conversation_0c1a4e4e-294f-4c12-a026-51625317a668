package com.hdec.data.controller;

import com.hdec.common.domain.R;
import com.hdec.data.qo.SloshQo;
import com.hdec.data.service.SloshService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;

/**
 * 晃点图控制器
 *
 * <AUTHOR>
 */
@Api(tags = "晃点图")
@Validated
@RestController
@RequestMapping("api/data/slosh")
public class SloshController {

    @Autowired
    private SloshService sloshService;

    /**
     * 加载晃点图
     */
    @ApiOperation("加载晃点图")
    @PostMapping("loadSlosh")
    public R loadSlosh(@RequestHeader("fieldNum") String fieldNum, @RequestBody SloshQo qo) throws ParseException {
        return sloshService.loadSlosh(fieldNum, qo);
    }

}
