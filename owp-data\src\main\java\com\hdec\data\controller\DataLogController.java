package com.hdec.data.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hdec.common.domain.R;
import com.hdec.data.domain.DataLog;
import com.hdec.data.qo.DataLogQo;
import com.hdec.data.service.DataLogService;
import com.hdec.data.service.RecoveryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.util.List;

/**
 * 数据日志控制器
 *
 * <AUTHOR>
 */
@Api(tags = "数据日志")
@Validated
@RestController
@RequestMapping("api/data/log")
public class DataLogController {

    @Autowired
    private DataLogService logService;

    @Autowired
    private RecoveryService recoveryService;

    /**
     * 日志列表
     */
    @ApiOperation("日志列表")
    @PostMapping("list")
    public R list(@RequestHeader("fieldNum") String fieldNum, @RequestBody DataLogQo qo) {
        PageHelper.startPage(qo.getPageNum(), qo.getPageSize());
        List<DataLog> logs = logService.list(fieldNum, qo);
        return R.success(new PageInfo<>(logs));
    }

    /**
     * 数据恢复
     */
    @ApiOperation("数据恢复")
    @GetMapping("recovery/{id}")
    public R recovery(@RequestHeader("fieldNum") String fieldNum, @RequestHeader(value = "sessionId") String sessionId, @PathVariable Integer id) throws ParseException {
        DataLog log = logService.getById(id);

        recoveryService.recoveryDel(sessionId, log.getRate(), log.getPointId(), log.getDirection(), log.getTime(), log.getDelDataJson(), fieldNum);

        // 删除数据
        logService.delById(id);
        return R.success("恢复成功");
    }

}
