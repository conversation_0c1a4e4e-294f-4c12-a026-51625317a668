package com.hdec.data.domain.report2;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * 报告实体类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class Report {

    /** 自增主键 */
    private Integer id;

    /** 监测时段开始 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date startTime;

    /** 监测时段结束 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date endTime;

    /** 封面ID */
    private Integer coverId;

    /** 封面名称 */
    private String coverName;

    /** 大纲ID */
    private Integer outlineId;

    /** 大纲名称 */
    private String outlineName;

    /** 状态（生成中/已完成） */
    private String status;

    /** 报告路径 */
    private String docPath;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createTime;

    /** 风场编码 */
    private String fieldNum;

    /** 期数 */
    private Integer issue;

    /** 总期数 */
    private Integer totalIssue;

    public Report(String status, String docPath) {
        this.status = status;
        this.docPath = docPath;
    }

    public Report(Integer id, String status, String docPath) {
        this.id = id;
        this.status = status;
        this.docPath = docPath;
    }
}
