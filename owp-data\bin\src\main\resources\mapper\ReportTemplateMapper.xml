<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hdec.data.mapper.ReportTemplateMapper">

    <select id="list" resultType="com.hdec.data.domain.ReportTemplate">
        SELECT
            *
        FROM
            owp_report_template
        <where>
            <if test="qo.classify != null and qo.classify != ''"> AND classify = #{qo.classify}</if>
            <if test="qo.templateType != null and qo.templateType != ''"> AND template_type = #{qo.templateType}</if>
            <if test="qo.attr != null and qo.attr != ''"> AND attr = #{qo.attr}</if>
        </where>
        ORDER BY `title` DESC, `attr`, `form`
    </select>

</mapper>