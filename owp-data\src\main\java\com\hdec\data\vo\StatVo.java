package com.hdec.data.vo;

import com.hdec.data.domain.Stat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * 测试统计Vo类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class StatVo {

    /** 表名 */
    private String tableName;

    /** 是否是极坐标 */
    private Boolean isPolar;

    /** 表记录 */
    private List<Stat> stats;

    public StatVo(String tableName, Boolean isPolar, List<Stat> stats) {
        this.tableName = tableName;
        this.isPolar = isPolar;
        this.stats = stats;
    }
}
