package com.hdec.data.domain.domin_word;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * 报告-非典型风机信息
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class ReportNonTypicalFanInfo {

    /** 风机编号 */
    private String no;

    private Integer corrodeSize;
    private String corrodePointNos;
    private String corrodePointValues;
    private String corrodePicBase64;

    public Integer getCorrodeSize() {
        if (corrodeSize == null) {
            return 0;
        }
        return corrodeSize;
    }

}
