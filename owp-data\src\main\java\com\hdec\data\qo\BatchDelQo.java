package com.hdec.data.qo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@NoArgsConstructor
public class BatchDelQo {

    /** 频率 */
    private String rate;

    /** 测点编号 */
    private Integer[] pointIds;

    /** 测点编号 */
    private String[] pointNos;

    /** 开始时间 */
    private String startTime;

    /** 结束时间 */
    private String endTime;

    /** 进度 */
    private Integer process;

    public BatchDelQo(String rate, Integer[] pointIds, String[] pointNos, String startTime, String endTime) {
        this.rate = rate;
        this.pointIds = pointIds;
        this.pointNos = pointNos;
        this.startTime = startTime;
        this.endTime = endTime;
    }
}
