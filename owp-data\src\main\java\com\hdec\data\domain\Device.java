package com.hdec.data.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.List;

@Slf4j
@Data
@ToString
@NoArgsConstructor
public class Device {

    /** 设备ID */
    private String id;

    /** 采集状态 */
    private String collectStatus;

    /** 从机连接状态 */
    private String slaveStatus;

    /** 设备时间 */
    private String time;

    /** 设备地址 */
    private String addr;

    /** 上次更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date lastUpdateTime;

    private List<ChannelStatus> channelStatuses;

    /** SD卡状态 */
    private String sdStatus;

    /** SD卡剩余容量 */
    private String sdCapacity;

    /** 定时重启周期 */
    private String restartCycle;

    public Device(String id, String collectStatus, String slaveStatus, String time, String addr, Date lastUpdateTime, List<ChannelStatus> channelStatuses) {
        this.id = id;
        this.collectStatus = collectStatus;
        this.slaveStatus = slaveStatus;
        this.time = time;
        this.addr = addr;
        this.lastUpdateTime = lastUpdateTime;
        this.channelStatuses = channelStatuses;
    }

    public Device(String id, String collectStatus, String slaveStatus, String time, String addr, Date lastUpdateTime, List<ChannelStatus> channelStatuses, String sdStatus, String sdCapacity, String restartCycle) {
        this.id = id;
        this.collectStatus = collectStatus;
        this.slaveStatus = slaveStatus;
        this.time = time;
        this.addr = addr;
        this.lastUpdateTime = lastUpdateTime;
        this.channelStatuses = channelStatuses;
        this.sdStatus = sdStatus;
        this.sdCapacity = sdCapacity;
        this.restartCycle = restartCycle;
    }
}
