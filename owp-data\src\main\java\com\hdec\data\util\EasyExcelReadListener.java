package com.hdec.data.util;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.CellData;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 大数据量Excel读取工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class EasyExcelReadListener extends AnalysisEventListener<Map<Integer, String>> {

    public List<Map<String, String>> list = new ArrayList<>();

    @Override
    public void invoke(Map<Integer, String> data, AnalysisContext context) {
        if (list.size() % 10000 == 0) {
            System.out.println("list.size():"+list.size());
        }
        Map<String, String> resMap = new HashMap<>(data.size());
        data.forEach((k, v) -> {
            resMap.put(String.valueOf(k + 1), v);
        });
        list.add(resMap);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }

}
