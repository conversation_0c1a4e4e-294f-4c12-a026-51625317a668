<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hdec.data.mapper.ReportMapperV2">

    <select id="list" resultType="com.hdec.data.domain.report2.Report">
        SELECT
            a.*,
            b.`name` AS cover_name,
            c.`name` AS outline_name
        FROM
            owp_report2 a
            LEFT JOIN owp_report_cover b ON a.cover_id = b.id
            LEFT JOIN owp_report_outline c ON a.outline_id = c.id
        WHERE
            a.field_num = #{fieldNum}
        ORDER BY
            a.`create_time` DESC
    </select>

    <insert id="add" parameterType="com.hdec.data.domain.report2.Report" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO owp_report2 (start_time, end_time, cover_id, outline_id, status, doc_path, create_time, field_num)
        VALUES
        (#{report.startTime}, #{report.endTime}, #{report.coverId}, #{report.outlineId}, #{report.status}, #{report.docPath}, NOW(), #{report.fieldNum})
    </insert>

    <delete id="delete">
        DELETE
        FROM
            owp_report2
        WHERE
            id IN
            <foreach collection="ids" item="id" separator=", " open="(" close=")">
                #{id}
            </foreach>
    </delete>

    <update id="update" parameterType="com.hdec.data.domain.report2.Report">
        UPDATE owp_report2
        <set>
            <if test="report.status != null">
                status = #{report.status},
            </if>
            <if test="report.docPath != null">
                doc_path = #{report.docPath},
            </if>
        </set>
        WHERE id = #{report.id}
    </update>

    <select id="selectMonthReportIssue" resultType="string">
        SELECT
            count(*)
        FROM
            owp_report2
        WHERE
            field_num = #{fieldNum}
        <if test="year != null">
            AND YEAR(start_time) = #{year}
        </if>
    </select>

    <update id="updateStatus">
        UPDATE owp_report2
        SET status = #{process}
        WHERE
            id = #{reportId}
    </update>

</mapper>