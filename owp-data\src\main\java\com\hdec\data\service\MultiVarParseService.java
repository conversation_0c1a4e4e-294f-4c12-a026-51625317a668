package com.hdec.data.service;

import com.hdec.data.domain.StatVar;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * 复用变量解析
 *
 * <AUTHOR>
 */
@Service
public class MultiVarParseService extends VarParseBase {

    /**
     * 解析字符串变量为实体类
     */
    public StatVar parseVar2Bean(String fieldNum, String varStr) throws Exception {
        if (ObjectUtils.isEmpty(varStr)) {
            return null;
        }
        varStr = varStr.trim().substring(2, varStr.length() - 1);

        String[] items = varStr.split("/");
        if (items.length == 5) {
            List<String> fanIds = super.getSeaFacilityIds(fieldNum, items[0]);
            Boolean installLocation = getInstallLocation(items[1]);
            String attrName = items[2];
            Integer direct = super.getDirect(items[3]);
            String statItem = items[4];
            return new StatVar(true, fanIds, installLocation, attrName, direct, statItem);
        } else if (items.length == 4) {
            // 升压站
            List<String> boosterIds = super.getSeaFacilityIds(fieldNum, items[0]);
            String attrName = items[1];
            Integer direct = super.getDirect(items[2]);
            String statItem = items[3];
            return new StatVar(false, boosterIds, attrName, direct, statItem);
        } else {
            throw new Exception("非法参数-参数个数非法");
        }
    }

    /**
     * 获取安装部位
     */
    private Boolean getInstallLocation(String s) throws Exception {
        if ("所有安装部位".equals(s)) {
            return null;
        } else if ("基础顶部".equals(s)) {
            return false;
        } else if ("塔筒顶部".equals(s)) {
            return true;
        } else {
            throw new Exception("非法参数-获取安装部位失败");
        }
    }

}
