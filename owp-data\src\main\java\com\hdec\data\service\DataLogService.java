package com.hdec.data.service;

import com.hdec.common.domain.AttrCommon;
import com.hdec.common.util.MonitorUtil;
import com.hdec.data.domain.DataLog;
import com.hdec.data.feign.MonitorService;
import com.hdec.data.mapper.DataLogMapper;
import com.hdec.data.qo.DataLogQo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 数据日志业务类
 *
 * <AUTHOR>
 */
@Service
public class DataLogService {

    @Autowired
    private DataLogMapper logMapper;

    @Autowired
    private MonitorService monitorService;

    /**
     * 日志列表
     */
    public List<DataLog> list(String fieldNum, DataLogQo qo) {
        return logMapper.list(fieldNum, qo);
    }

    /**
     * 保存日志
     */
    public void save(DataLog log) {
        // 完善测点编号
        if (log.getPointId() != null) {
            log.setPointNo(monitorService.getNoById(log.getPointId()));
        }

        // 完善方向
        if (log.getDirection() != null) {
            log.setDirectionName(MonitorUtil.transDirection(log.getDirection()));
        }

        // 完善分量名
        if (log.getAttrId() != null) {
            AttrCommon proAttrById = monitorService.getProAttrById(log.getAttrId());
            if (proAttrById != null) {
                String attrName = proAttrById.getName();
                log.setAttrName(attrName);
            }
        }

        logMapper.save(log);
    }

    public DataLog getById(Integer id) {
        return logMapper.getById(id);
    }

    public void delById(Integer id) {
        logMapper.delById(id);
    }
}
