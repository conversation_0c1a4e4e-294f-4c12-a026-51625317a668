package com.hdec.data.qo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * 自定义表格查询类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class CustomTabQo {

    /** 典型风机/非典型风机/所有风机/升压站 */
    private String type;

    /** 是否按测点编号分组 */
    private Boolean isByPoint;

    /** 是否显示安装方位 */
    private Boolean isShowInstallOrient;

    /** 是否显示安装高程 */
    private Boolean isShowInstallEle;

    /** 仪器ID */
    private Integer instId;

    /** 统计分量 */
    List<StatAttr> statAttrs;


    /**
     * 海上设施
     */
    String facilityId;

}
