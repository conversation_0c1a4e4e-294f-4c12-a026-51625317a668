package com.hdec.data.service;

import com.alibaba.fastjson.JSON;
import com.hdec.common.constant.RedisKey;
import com.hdec.data.domain.Device;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 设备控制业务逻辑层
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class DeviceService {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 设备列表
     */
    public List<Device> list() {
        Object deviceJson = redisTemplate.opsForValue().get(RedisKey.DEVICE_LIST);
        if (ObjectUtils.isEmpty(deviceJson)) {
            return new ArrayList<>();
        }
        return JSON.parseArray((String) deviceJson, Device.class);
    }

}
