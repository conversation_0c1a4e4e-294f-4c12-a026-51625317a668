package com.hdec.data.service;

import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.ddr.poi.html.ElementRenderer;
import org.ddr.poi.html.HtmlRenderContext;
import org.jsoup.nodes.Element;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @date 2023/12/19
 */
public class AnnotationRender implements ElementRenderer {
    private static final String[] TAGS = new String[]{"annotation"};

    private ArrayList<String> pics;
    private ArrayList<String> tabs;

    public AnnotationRender(ArrayList<String> pics, ArrayList<String> tabs) {
        this.pics = pics;
        this.tabs = tabs;
    }

    @Override
    public boolean renderStart(Element element, HtmlRenderContext context) {
        //判断类型  : 表 or 图
        String type = element.attr("type");
        if (!type.isEmpty()){
            XWPFParagraph closestParagraph = context.getClosestParagraph();
            if(type.equals("image")){
                //生成图
                pics.add(element.text());
                closestParagraph.createRun().setText("{{picAnnotation}}");
            }else{
                //生成表
                tabs.add(element.text());
                closestParagraph.createRun().setText("{{tabAnnotation}}");
            }
        }
        return false;
    }

    @Override
    public String[] supportedTags() {
        return TAGS;
    }

    @Override
    public boolean renderAsBlock() {
        return true;
    }
}
