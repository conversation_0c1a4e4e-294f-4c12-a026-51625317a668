package com.hdec.data.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * 频谱分析结果类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class FrequencyVo {

    /** X轴数据 */
    private List<Double> xArr;

    /** Y轴数据 */
    private List<Double> yArr;

    public FrequencyVo(List<Double> xArr, List<Double> yArr) {
        this.xArr = xArr;
        this.yArr = yArr;
    }
}
