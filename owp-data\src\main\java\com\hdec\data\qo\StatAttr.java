package com.hdec.data.qo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 统计分量查询类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class StatAttr {

    /** 属性ID */
    private Integer attrId;

    /** 属性ID */
    private String attrName;

    /** 自定义变量名 */
    private String customVar;

    /** 属性ID */
    private Boolean isPolar;

    /** 方向 */
    private String[] directs;

    /** 统计方式 */
    private String[] statItems;

    /**
     * 基准统计方式
     * 仅用于计算相对变化量中获取计算的统计方式
     */
    private String referStatItem;

    /**
     * 基准值参数ID
     * 仅用于计算相对变化量中通过此ID获取仪器参数
     */
    private Integer referParamId;

}
