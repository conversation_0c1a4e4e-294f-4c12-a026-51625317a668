//package com.hdec.data.util;
//import org.apache.poi.xwpf.usermodel.XWPFDocument;
//import org.apache.poi.xwpf.usermodel.XWPFParagraph;
//import org.apache.poi.xwpf.usermodel.XWPFRun;
//import org.apache.poi.xwpf.usermodel.XWPFTable;
//import org.apache.poi.xwpf.usermodel.XWPFTableCell;
//import org.apache.poi.xwpf.usermodel.XWPFTableRow;
//
//import java.io.FileInputStream;
//import java.io.FileOutputStream;
//import java.io.IOException;
//
///**
// * <AUTHOR>
// * @date 2023/8/25
// */
//public class ConvertDocxToPdf {
//
//    public static void main(String[] args) {
//        try {
//            // 读取docx文件
//            FileInputStream inDocx = new FileInputStream("input.docx");
//            XWPFDocument document = new XWPFDocument(inDocx);
//
//            // 将docx文件转换为pdf
//            FileOutputStream outPdf = new FileOutputStream("output.pdf");
//            PdfOptions options = PdfOptions.create();
//            PdfConverter.getInstance().convert(document, outPdf, options);
//
//            // 关闭文件流
//            inDocx.close();
//            outPdf.close();
//
//            System.out.println("成功将docx文件转换为pdf文件！");
//        } catch (IOException e) {
//            System.out.println("转换失败，出现IO异常：" + e.getMessage());
//        }
//    }
//
//}
