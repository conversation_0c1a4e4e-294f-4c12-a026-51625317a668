package com.hdec.data.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class BkgLogger {

    /**
     * 采集仪编号
     */
    private String devNo;

    /**
     * 通道
     */
    private Integer channel;

    /**
     * 测量时间
     */
    private Date dataTime;

    /**
     * 原始数据 1,
     * 一般为频率或
     * 模数
     */
    private String s1;

    /**
     * 原始数据 2
     * 一般为电阻
     */
    private String s2;

    /**
     * 原始数据 3
     * （备用）
     */
    private String s3;

    /**
     * 原始数据 4
     * （备用）
     */
    private String s4;

    /**
     * 原始数据 5
     * （备用）
     */
    private String s5;

    /**
     * 结果数据 1
     * （根据公式计算出的相应的物理量）
     */
    private String r1;

    /**
     * 结果数据 2
     * （温度）
     */
    private String r2;

    /**
     * 结果数据 3
     * （备用）
     */
    private String r3;

    /**
     * 结果数据 4
     * （备用）
     */
    private String r4;

    /**
     * 结果数据 5
     * （备用）
     */
    private String r5;

    /**
     * 是否越界
     */
    private Integer isWarning;
}
