package com.hdec.data.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FreqPic {

    /**
     * 自增主键
     */
    private Integer id;

    private String target;

    private String attrName;

    private String url;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 提交时间
     */
    private String createTime;

    private String param;

    private String fieldNum;

    /** 图片总数量 */
    private Integer picSize;

    public FreqPic(String target, String attrName, Integer picSize, String fieldNum) {
        this.target = target;
        this.attrName = attrName;
        this.picSize = picSize;
        this.fieldNum = fieldNum;
    }
}