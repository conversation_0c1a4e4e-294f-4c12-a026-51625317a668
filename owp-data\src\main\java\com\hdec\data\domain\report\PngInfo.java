package com.hdec.data.domain.report;

import com.hdec.data.domain.Datum;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class PngInfo {

    private Integer pointId;

    private String pointNo;

    private String direct;

    private List<Datum> data;
    private List<Datum> data2;

    private String monitorItem;

    private String chartAttrName;

    private String fileAttrName;

    private String chartAttrName2;

    private String fileAttrName2;

    private String type;

    private Boolean isSingleDirect;

    private String leftAttrNameWithoutUnit;
    private String rightAttrNameWithoutUnit;

    private String directAlias;

    public PngInfo(Integer pointId, String pointNo, String direct, List<Datum> data, List<Datum> data2, String chartAttrName, String chartAttrName2) {
        this.pointId = pointId;
        this.pointNo = pointNo;
        this.direct = direct;
        this.data = data;
        this.data2 = data2;
        this.chartAttrName = chartAttrName;
        this.chartAttrName2 = chartAttrName2;
    }

    public PngInfo(Integer pointId, String pointNo, String direct, List<Datum> data, List<Datum> data2, String chartAttrName, String chartAttrName2, Boolean isSingleDirect) {
        this.pointId = pointId;
        this.pointNo = pointNo;
        this.direct = direct;
        this.data = data;
        this.data2 = data2;
        this.chartAttrName = chartAttrName;
        this.chartAttrName2 = chartAttrName2;
        this.isSingleDirect = isSingleDirect;
    }

    public PngInfo(Integer pointId, String pointNo, String direct, List<Datum> data, List<Datum> data2, String chartAttrName, String chartAttrName2, Boolean isSingleDirect,
                   String leftAttrNameWithoutUnit, String rightAttrNameWithoutUnit) {
        this.pointId = pointId;
        this.pointNo = pointNo;
        this.direct = direct;
        this.data = data;
        this.data2 = data2;
        this.chartAttrName = chartAttrName;
        this.chartAttrName2 = chartAttrName2;
        this.isSingleDirect = isSingleDirect;
        this.leftAttrNameWithoutUnit = leftAttrNameWithoutUnit;
        this.rightAttrNameWithoutUnit = rightAttrNameWithoutUnit;
    }

    public PngInfo(Integer pointId, String pointNo, String direct, List<Datum> data, List<Datum> data2, String chartAttrName, String chartAttrName2, Boolean isSingleDirect,
                   String leftAttrNameWithoutUnit, String rightAttrNameWithoutUnit, String fileAttrName, String fileAttrName2) {
        this.pointId = pointId;
        this.pointNo = pointNo;
        this.direct = direct;
        this.data = data;
        this.data2 = data2;
        this.chartAttrName = chartAttrName;
        this.chartAttrName2 = chartAttrName2;
        this.isSingleDirect = isSingleDirect;
        this.leftAttrNameWithoutUnit = leftAttrNameWithoutUnit;
        this.rightAttrNameWithoutUnit = rightAttrNameWithoutUnit;
        this.fileAttrName = fileAttrName;
        this.fileAttrName2 = fileAttrName2;
    }

    public PngInfo(Integer pointId, String pointNo, String direct, List<Datum> data, String monitorItem) {
        this.pointId = pointId;
        this.pointNo = pointNo;
        this.direct = direct;
        this.data = data;
        this.monitorItem = monitorItem;
    }

    public PngInfo(Integer pointId, String pointNo, String direct, List<Datum> data, String chartAttrName, String fileAttrName) {
        this.pointId = pointId;
        this.pointNo = pointNo;
        this.direct = direct;
        this.data = data;
        this.chartAttrName = chartAttrName;
        this.fileAttrName = fileAttrName;
    }

    public PngInfo(Integer pointId, String pointNo, String direct, List<Datum> data, String monitorItem, String chartAttrName, String fileAttrName, String type) {
        this.pointId = pointId;
        this.pointNo = pointNo;
        this.direct = direct;
        this.data = data;
        this.monitorItem = monitorItem;
        this.chartAttrName = chartAttrName;
        this.fileAttrName = fileAttrName;
        this.type = type;
    }

    public PngInfo(Integer pointId, String pointNo, String direct, List<Datum> data, String monitorItem, String chartAttrName, String fileAttrName, String type, Boolean isSingleDirect) {
        this.pointId = pointId;
        this.pointNo = pointNo;
        this.direct = direct;
        this.data = data;
        this.monitorItem = monitorItem;
        this.chartAttrName = chartAttrName;
        this.fileAttrName = fileAttrName;
        this.type = type;
        this.isSingleDirect = isSingleDirect;
    }

    public PngInfo(Integer pointId, String pointNo, String direct,Boolean isSingleDirect) {
        this.pointId = pointId;
        this.pointNo = pointNo;
        this.direct = direct;
        this.isSingleDirect = isSingleDirect;
    }

    public void setLeft(List<Datum> data, String attrName, String attrNameWithoutUnit) {
        this.data = data;
        this.chartAttrName = attrName;
        this.leftAttrNameWithoutUnit = attrNameWithoutUnit;
    }

    public void setRight(List<Datum> data, String attrName, String attrNameWithoutUnit) {
        this.data2 = data;
        this.chartAttrName2 = attrName;
        this.rightAttrNameWithoutUnit = attrNameWithoutUnit;
    }

    @Override
    public String toString() {
        return "PngInfo{" +
                "pointId=" + pointId +
                ", pointNo='" + pointNo + '\'' +
                ", direct='" + direct + '\'' +
                ", monitorItem='" + monitorItem + '\'' +
                ", chartAttrName='" + chartAttrName + '\'' +
                ", fileAttrName='" + fileAttrName + '\'' +
                ", chartAttrName2='" + chartAttrName2 + '\'' +
                ", fileAttrName2='" + fileAttrName2 + '\'' +
                ", type='" + type + '\'' +
                ", isSingleDirect=" + isSingleDirect +
                ", leftAttrNameWithoutUnit='" + leftAttrNameWithoutUnit + '\'' +
                ", rightAttrNameWithoutUnit='" + rightAttrNameWithoutUnit + '\'' +
                '}';
    }
}
