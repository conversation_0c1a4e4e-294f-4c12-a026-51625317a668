package com.hdec.data.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hdec.common.domain.R;
import com.hdec.data.domain.ReportTemplate;
import com.hdec.data.qo.ReportTemplateQo;
import com.hdec.data.service.ReportTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 报告模板控制器
 *
 * <AUTHOR>
 */
@Api(tags = "报告模板")
@Validated
@RestController
@RequestMapping("api/data/reportTemplate")
public class ReportTemplateController {

    @Autowired
    private ReportTemplateService templateService;

    /**
     * 报告模板列表
     */
    @ApiOperation("报告模板列表")
    @PostMapping("list")
    public R list(@RequestBody ReportTemplateQo qo) {
        PageHelper.startPage(qo.getPageNum(), qo.getPageSize());
        List<ReportTemplate> templates = templateService.list(qo);
        return R.success(new PageInfo<>(templates));
    }

}
