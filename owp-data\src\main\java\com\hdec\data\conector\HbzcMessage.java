package com.hdec.data.conector;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class HbzcMessage implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 项目名
     */
    private String project;

    /**
     * 包编号
     */
    @JsonProperty("packet_index")
    private Integer packetIndex;

    /**
     * 设备 ID
     */
    @JsonProperty("device_id")
    private String deviceId;

    /**
     * 设备序号
     */
    @JsonProperty("device_num")
    private Integer deviceNum;

    /**
     * 通道数
     */
    @JsonProperty("channel_size")
    private Integer channelSize;

    /**
     * 每个通道数据数组长度
     */
    @JsonProperty("data_long")
    private Integer dataLong;

    /**
     * 采样率
     */
    private Integer frequence;

    /**
     * 时间戳
     */
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    private Date timestamp;

    /**
     * /GPS 信息，维度、精度单位为度
     */
    private String gps;

    /**
     * 通道数据
     */
    private List<Channel> channel;


    @Data
    public static class Channel implements Serializable {
        private static final long serialVersionUID = 1L;
        /**
         * 通道名称
         */
        @JsonProperty("channel_name")
        private String channelName;
        /**
         * 通道单位
         */
        @JsonProperty("channel_unit")
        private String channelUnit;
        /**
         * 通道描述
         */
        @JsonProperty("description")
        private String description;

        /**
         * 灵敏度
         */
        @JsonProperty("sensitivity")
        private String sensitivity;

        /**
         * 测量参量类型编码（
         * 0 表示加速度、
         * 1 表示倾斜、
         * 2 表示位移、
         * 3 表示动应变、
         * 4 表示温度、
         * 5 表示波压力、
         * 6 表示腐蚀参比电极、
         * 7 表示风速、
         * 8 表示振动速度、
         * 9 表示其它）
         */
        private String type;

        /**
         * 数据
         */
        private List<Double> data;

        /**
         * 计算结果,备用数组，例如最大值、最小值等。
         */
        private List<Double> maths;
    }

}
