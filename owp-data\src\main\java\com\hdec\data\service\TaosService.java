package com.hdec.data.service;

import com.hdec.common.domain.AttrCommon;
import com.hdec.common.formula.AttrVal;
import com.hdec.common.formula.AttrVar;
import com.hdec.data.domain.DailyCount;
import com.hdec.data.domain.RedoJob;
import com.hdec.data.domain.taos.TableColumn;

import java.util.Date;
import java.util.List;

public interface TaosService {

    /**
     * 查询分量变量值
     *
     * @param attrVar 分量变量
     * @param ts      时间
     * @return {@link AttrVal }
     */
    AttrVal selectAttrVar(AttrVar attrVar, Date ts);

    /**
     * 查询分量变量值
     *
     * @param attrVar   分量变量
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return {@link AttrVal }
     */
    List<AttrVal> selectAttrVal(AttrVar attrVar, Date startTime, Date endTime);

    /**
     * 批量保存
     *
     * @param values values
     */
    void batchSave(List<AttrVal> values);

    /**
     * 提交重做任务
     *
     * @param jobs jobs
     */
    void commitRedoJob(Integer type, List<RedoJob> jobs);

    /**
     * 查询未重做任务
     *
     * @param type           类型
     * @param createInterval 创建间隔（单位：秒）
     * @return {@link List }<{@link RedoJob }>
     */
    List<RedoJob> selectUndoTask(Integer type, Integer createInterval);

    /**
     * 完成重做任务
     *
     * @param type type
     * @param jobs jobs
     */
    void completeRedoJob(Integer type, List<RedoJob> jobs);

    /**
     * 查询日统计数量
     *
     * @param inst      仪器
     * @param point     测点
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return {@link Long }
     */
    Long selectDailyCount(Integer inst, Integer point, Date startTime, Date endTime);

    /**
     * 插入日统计信息
     *
     * @param dailyCounts 日数量
     */
    void insectDailyCount(List<DailyCount> dailyCounts);

    /**
     * 查询数据量
     *
     * @param points    测点ids
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return long
     */
    Long selectDataCount(List<Integer> points, Date startTime, Date endTime);

    /**
     * 初始化高频超级表
     *
     * @param instId      仪器ID
     * @param attrs       分量信息
     * @param instDirects 仪器方向
     */
    void initializeHighSTable(Integer instId, List<AttrCommon> attrs, Integer[] instDirects);

    /**
     * 初始化高频采样表
     *
     * @param instId      仪器ID
     * @param attrs       分量信息
     * @param instDirects 仪器方向
     */
    void initializeHighSampleSTable(Integer instId, List<AttrCommon> attrs, Integer[] instDirects);

    /**
     * 初始化非高频数据
     *
     * @param instId      仪器ID
     * @param attrs       分量信息
     * @param instDirects 仪器方向
     */
    void initializeNormalSTable(Integer instId, List<AttrCommon> attrs, Integer[] instDirects);

    /**
     * 初始化超级表
     *
     * @param database  数据库
     * @param tableName 表名
     * @param columns   列
     * @param tags      tags
     */
    void initializeSTable(String database, String tableName, List<TableColumn> columns, List<TableColumn> tags);


    /**
     * 创建重做任务超级表
     */
    void createRedoJobStable();

    /**
     * 创建日统计超级表
     */
    void createDailyCountJobStable();

    /**
     * 创建数据库
     */
    void createOwpDatabase();

    /**
     * 创建数据库
     */
    void createOwpHighDatabase();

    /**
     * 创建数据库
     */
    void createOwpJobDatabase();

    /**
     * 生成 point 的tags
     *
     * @return {@link List }<{@link TableColumn }>
     */
    List<TableColumn> getPointTags();

    /**
     * 获取表名字
     *
     * @param rate rate
     * @param inst inst
     * @return {@link String }
     */
    String getTableName(String rate, Integer inst, boolean withDbName);

    /**
     * 获取表名字
     *
     * @param rate  rate
     * @param inst  inst
     * @param point point
     * @return {@link String }
     */
    String getTableName(String rate, Integer inst, Integer point, boolean withDbName);

    /**
     * 根据频率获取数据库名称
     *
     * @param rate rate
     * @return {@link String }
     */
    String getRateDatabase(String rate);

    /**
     * 转列名
     *
     * @param attr   分量
     * @param direct 方向
     * @return {@link String }
     */
    String enColName(Integer attr, Integer direct);

    /**
     * 转缺测列名
     *
     * @param attr   分量
     * @param direct 方向
     * @return {@link String }
     */
    String enLackColName(Integer attr, Integer direct);

    /**
     * 转距离上一次数据时间列名
     *
     * @param attr   分量
     * @param direct 方向
     * @return {@link String }
     */
    String enLastTimeColName(Integer attr, Integer direct);

    /**
     * 转距离下一次数据时间列名
     *
     * @param attr   分量
     * @param direct 方向
     * @return {@link String }
     */
    String enNextTimeColName(Integer attr, Integer direct);


    /**
     * 转修改前历史数据列名
     *
     * @param attr   分量
     * @param direct 方向
     * @return {@link String }
     */
    String enLastHisColName(Integer attr, Integer direct);
}
