package com.hdec.data.service;

import com.hdec.common.constant.Constant;
import com.hdec.common.constant.RedisKey;
import com.hdec.common.domain.CollectInstMountCommon;
import com.hdec.common.domain.msg.Inst;
import com.hdec.common.formula.AttrVal;
import com.hdec.common.util.CommonUtil;
import com.hdec.data.cache.LocalCacheManager;
import com.hdec.data.config.LocalCache;
import com.hdec.data.feign.MonitorService;
import com.hdec.data.vo.Val;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DataAccessService {

    /**
     * 线程池等待队列大小
     */
    private static final Integer QUEUE_SIZE = 100;

    private static final Integer THREAD_NUM = 10;

    private static final Integer GROUP_BATCH_SIZE = 50;
    private static final Integer BATCH_FLUSH_TIME_INTERVAL = 10000;
    private static Long BATCH_FLUSH_TIME = 0L;

    private static final LinkedBlockingQueue<List<AttrVal>> BUFFER_QUEUE = new LinkedBlockingQueue<>();
    /**
     * 线程池
     */
    private final static ThreadPoolExecutor threadPool = new ThreadPoolExecutor(THREAD_NUM, THREAD_NUM, 5, TimeUnit.MINUTES, new ArrayBlockingQueue<>(QUEUE_SIZE));


    @Value("${deploy.fieldNum}")
    private String fieldNum;

    @Value("${data.access.log.enable:false}")
    private boolean logEnable;

    @Resource
    private LocalCache localCache;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private LocalCacheManager localCacheManager;

    @Resource
    private TaosService taosService;

    @Resource
    private FormulaCalcService formulaCalcService;

    @Resource
    private MonitorService monitorService;

    @Resource
    private AlarmService alarmService;

    @Resource
    private RedoJobService redoJobService;

    public void input(List<Inst> instList, String deviceType) {
        input(instList, deviceType, true);
    }

    public void input(List<Inst> instList, String deviceType, boolean withCache) {
        List<AttrVal> attrValues = new ArrayList<>();
        Map<String, Long> lastUpdateTimeMap = new HashMap<>();
        Long nowTs = new Date().getTime();
        for (Inst inst : instList) {
            Map<Integer, Float[]> channelData = inst.getChannelData();
            if (channelData == null || channelData.isEmpty()) {
                continue;
            }
            channelData.forEach((channel, values) -> {
                if (values == null || values.length == 0 || Float.isNaN(values[0])) {
                    if (logEnable && Math.random() > 0.999) {
                        log.error("{}通道{}值为异常", inst.getDeviceNo(), channel + 1);
                    }
                    return;
                }
                CollectInstMountCommon mount = localCacheManager.getMountCache(deviceType, inst.getDeviceNo(), channel + 1);
                if (mount == null) {
                    if (logEnable && Math.random() > 0.999) {
                        log.error("{}通道{}未找到挂载信息-{}", inst.getDeviceNo(), channel + 1,
                                CommonUtil.join(Constant.SPLIT_STR, deviceType, inst.getDeviceNo(), channel));
                    }
                    return;
                }
                /* 更新channel_last_update_time */
                lastUpdateTimeMap.put(RedisKey.CHANNEL_LAST_UPDATE_TIME + mount.getCollectInstId() + "_" + (channel + 1), nowTs);
                Integer instId = mount.getInstId();
                Integer pointId = mount.getPointId();
                Integer direct = mount.getDirect();
                /* 按挂载信息封装数据 */
                if ("dynamic".equals(deviceType)) {
                    Integer attr = mount.getAttrIds()[0];
                    String attrRate = mount.getAttrRates()[0];
                    Float value = values[0];
                    attrValues.add(new AttrVal(inst.getTime(), instId, pointId, attr, direct,
                            attrRate, value.doubleValue()));
                } else if ("static".equals(deviceType)) {
                    // 静态设备
                    for (int i = 0; i < mount.getAttrIds().length; i++) {
                        Integer[] attrIds = mount.getAttrIds();
                        if (attrIds[i] != null) {
                            Integer attrId = attrIds[i];
                            String attrRate = mount.getAttrRates()[i];
                            Float value = values[i];
                            attrValues.add(new AttrVal(inst.getTime(), instId, pointId, attrId,
                                    direct, attrRate, value.doubleValue()));
                        }
                    }
                } else {
                    // 雷达设备
                    Integer idx = inst.getDataIndex();
                    if (mount.getAttrIds().length > idx && mount.getAttrIds()[idx] != null) {
                        Integer attrId = mount.getAttrIds()[idx];
                        String attrRate = mount.getAttrRates()[idx];
                        Float value = values[0];
                        attrValues.add(new AttrVal(inst.getTime(), instId, pointId,
                                attrId, direct, attrRate, value.doubleValue()));
                    }
                }
            });
        }

        /*  更新最后时间  */
        redisTemplate.opsForValue().multiSet(lastUpdateTimeMap);
        try {
            threadPool.submit(() -> {
                        try {
                            processRecord(attrValues, withCache);
                        } catch (Exception e) {
                            log.error("处理数据失败！", e);
                        }
                    }
            );
        } catch (Exception e) {
            log.error("队列已满[{}]，提交失败！{}", threadPool.getQueue().size(), e.getMessage());
        }
    }

    public void processRecord(List<AttrVal> attrValues, boolean withCache) {
        if (withCache) {
            processRecord(attrValues);
        } else {
            long t0 = System.currentTimeMillis();
            /* 处理公式 */
            List<AttrVal> formulaValues = formulaCalcService.accessBatchCompute(attrValues, Collections.emptyList());
            /* 合并数据 */
            attrValues.addAll(formulaValues);
            /* 缓存最新数据 */
            cacheRecords(attrValues);
            /* 保存数据 */
            long t4 = System.currentTimeMillis();
            taosService.batchSave(attrValues);

            long t5 = System.currentTimeMillis();
            /* 处理告警 */
            alarmService.processAlarm(attrValues, fieldNum);

            long t6 = System.currentTimeMillis();
            /* 处理重做 */
            redoJobService.submitRedoJob(attrValues);
            long t1 = System.currentTimeMillis();
            if (t1 - t0 > 300) {
                log.info("处理数据总:{}ms", t1 - t0);
            }
        }
    }

    public void processRecord(List<AttrVal> attrValues) {
        long t0 = System.currentTimeMillis();
        /* 处理公式 */
        List<AttrVal> queueValues = getQueueValues();
        List<AttrVal> formulaValues = formulaCalcService.accessBatchCompute(attrValues, queueValues);
        long t1 = System.currentTimeMillis();
        /* 合并数据 */
        attrValues.addAll(formulaValues);
        /* 缓存最新数据 */
        cacheRecords(attrValues);
        long t2 = System.currentTimeMillis();
        /*  存入队列  */
        BUFFER_QUEUE.add(attrValues);
        long timeMillis = System.currentTimeMillis();
        if (BUFFER_QUEUE.size() >= GROUP_BATCH_SIZE || timeMillis - BATCH_FLUSH_TIME >= BATCH_FLUSH_TIME_INTERVAL) {
            long t3 = System.currentTimeMillis();
            BATCH_FLUSH_TIME = timeMillis;
            /*  队列数据取出  */
            List<List<AttrVal>> groupRecords = new ArrayList<>();
            BUFFER_QUEUE.drainTo(groupRecords);
            int cacheSize = BUFFER_QUEUE.size();
            List<AttrVal> flushRecords = groupRecords.stream().flatMap(List::stream).collect(Collectors.toList());

            /* 保存数据 */
            long t4 = System.currentTimeMillis();
            taosService.batchSave(flushRecords);

            long t5 = System.currentTimeMillis();
            /* 处理告警 */
            alarmService.processAlarm(flushRecords, fieldNum);

            long t6 = System.currentTimeMillis();
            /* 处理重做 */
            redoJobService.submitRedoJob(attrValues);

            long t7 = System.currentTimeMillis();
            if (t7 - t0 > 300) {
                log.info("处理数据总:{}ms 公式计算:{}ms 缓存数据:{}ms 队列取出:{}ms 写入TD:{}ms " +
                                "处理告警:{}ms 处理重做:{}ms 数据数量:{} 队列剩余:{} 线程队列:{}",
                        t7 - t0, t1 - t0, t2 - t1, t4 - t3, t5 - t4, t6 - t5, t7 - t6,
                        flushRecords.size(), cacheSize, threadPool.getQueue().size());
            }
        }
    }

    /**
     * 获取队列中缓存的值
     *
     * @return {@link List }<{@link AttrVal }>
     */
    private List<AttrVal> getQueueValues() {
        Object[] array = BUFFER_QUEUE.toArray();
        return Arrays.stream(array).map(e -> {
            List<AttrVal> result = new ArrayList<>();
            if (e instanceof List<?>) {
                for (Object item : (List<?>) e) {
                    result.add((AttrVal) item);
                }
            }
            return result;
        }).flatMap(List::stream).collect(Collectors.toList());
    }

    private void cacheRecords(List<AttrVal> attrValues) {
        int sizeShort = 2500;
        for (AttrVal attrVal : attrValues) {
            String keyShort = RedisKey.LAST_AUTO_DATA + CommonUtil.join("-", attrVal.getPoint(), attrVal.getDirect(), attrVal.getAttr());

            /* 缓存短数据 */
            LinkedList<Val> queueShort = localCache.get(keyShort);
            if (queueShort == null) {
                queueShort = new FixedLengthStack<>(sizeShort);
            }

            Date curTimeMs = attrVal.getTs();
            if (isNewVal(queueShort.peek(), curTimeMs)) {
                queueShort.push(new Val(curTimeMs, attrVal.getVal().floatValue()));
            }
            localCache.set(keyShort, queueShort, 1, TimeUnit.MINUTES);
        }
    }


    static class FixedLengthStack<E> extends LinkedList<E> {
        private final int maxSize;

        public FixedLengthStack(int maxSize) {
            this.maxSize = maxSize;
        }

        public synchronized void push(E item) {
            if (size() >= maxSize) {
                /*  移除最早的元素（栈底） */
                removeFirst();
            }
            /*  添加到栈顶  */
            addLast(item);
        }

        public E pop() {
            return super.pop();
        }

        public int maxSize() {
            return maxSize;
        }
    }

    /**
     * 判断是否存在于末尾
     */
    private boolean isNewVal(Val lastVal, Date curTime) {
        if (lastVal == null || lastVal.getT() == null || curTime == null) {
            return true;
        }

        if (curTime.getTime() > lastVal.getT().getTime()) {
            return true;
        }
        return false;
    }
}
