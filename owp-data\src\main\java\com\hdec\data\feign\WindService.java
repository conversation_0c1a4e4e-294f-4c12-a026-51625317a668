package com.hdec.data.feign;

import com.hdec.common.domain.ProResourceCommon;
import com.hdec.common.domain.SeaFacilityCommon;
import com.hdec.common.domain.SettingsCommon;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(name = "owp-wind")
public interface WindService {

    @GetMapping(value = "api/wind/process/allSeaFacility2/{fieldNum}")
    List<SeaFacilityCommon> allSeaFacility(@PathVariable(value = "fieldNum") String fieldNum);

    @GetMapping(value = "api/wind/project/getFieldNameByFieldNum/{fieldNum}")
    String getFieldNameByFieldNum(@PathVariable(value = "fieldNum") String fieldNum);

    /**
     * 获取某测点绑定风机的设计泥面高程
     */
    @GetMapping(value = "api/wind/fan/getDesignElevation/{pointId}")
    Float getDesignElevation(@PathVariable(value = "pointId") Integer pointId);

    @PostMapping(value = "api/wind/project/getFieldNameByFieldNums")
    List<ProResourceCommon> getFieldNameByFieldNums(@RequestBody List<String> fieldNums);

    @GetMapping(value = "api/wind/project/allFiled")
    List<ProResourceCommon> allFiled();

    @GetMapping("api/wind/settings/feignGet")
    SettingsCommon getSettings(@RequestHeader("fieldNum") String fieldNum,
                            @RequestParam("code") String code);

    @PostMapping(value = "api/wind/settings/feignSave")
    void saveSettings(@RequestBody SettingsCommon settings);

}
