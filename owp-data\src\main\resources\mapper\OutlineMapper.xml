<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hdec.data.mapper.ReportOutlineMapper">

    <select id="navList" resultType="com.hdec.data.domain.OutlineNav">
        SELECT
            *
        FROM
            owp_report_outline_nav
        WHERE
            outline_id = #{outlineId}
        ORDER BY `order`, id
    </select>

    <select id="selectNavByNameAndPid" resultType="com.hdec.data.domain.OutlineNav">
        SELECT
            *
        FROM
            owp_report_outline_nav
        WHERE
            field_num = #{fieldNum}
        AND `name` = #{name}
        AND pid = #{pid}
        AND outline_id = #{outlineId}
        <if test="id != null">
            AND id != #{id}
        </if>
    </select>

    <select id="getNavById" resultType="com.hdec.data.domain.OutlineNav">
        SELECT
            *
        FROM
            owp_report_outline_nav
        WHERE
            id = #{id}
    </select>

    <insert id="addNav" parameterType="com.hdec.data.domain.OutlineNav" useGeneratedKeys="true" keyProperty="nav.id">
        INSERT INTO owp_report_outline_nav (
            `name`,
            pid,
            create_time,
            outline_id,
            `order`,
            field_num
        )
        VALUES (
            #{nav.name},
            #{nav.pid},
            NOW(),
            #{nav.outlineId},
            #{nav.order},
            #{fieldNum}
        )
    </insert>

    <insert id="addNavs" parameterType="com.hdec.data.domain.OutlineNav">
        INSERT INTO owp_report_outline_nav (
            id,
            `name`,
            pid,
            create_time,
            outline_id,
            `order`,
            field_num
        )
        VALUES
        <foreach collection="navs" item="nav" separator="," >(
            #{nav.id},
            #{nav.name},
            #{nav.pid},
            NOW(),
            #{nav.outlineId},
            #{nav.order},
            #{fieldNum}
        )
        </foreach>
    </insert>

    <update id="updateNav" parameterType="com.hdec.data.domain.OutlineNav">
        UPDATE
            owp_report_outline_nav
        SET
            `name` = #{nav.name},
             pid = #{nav.pid},
             create_time = #{nav.createTime}
        WHERE
            id = #{nav.id}
    </update>

    <delete id="navDelete">
        DELETE
        FROM
            owp_report_outline_nav
        WHERE
            id = #{id}
	</delete>

    <delete id="deleteNavByOutline">
        DELETE
        FROM
            owp_report_outline_nav
        WHERE
            outline_id = #{outlineId}
	</delete>

    <select id="getChildrenById" resultType="com.hdec.data.domain.OutlineNav">
        SELECT
            *
        FROM
            owp_report_outline_nav
        WHERE
            pid = #{id}
	</select>

    <select id="getMaxNavId" resultType="int">
        SELECT
            IFNULL( MAX( id ), 0)
        FROM
            owp_report_outline_nav
    </select>

    <select id="list" resultType="com.hdec.data.domain.Outline">
        SELECT
            *
        FROM
            owp_report_outline
        <where>
            field_num = #{fieldNum}
            <if test="name != null and name != ''">
                AND `name` LIKE CONCAT('%', #{name} ,'%')
            </if>
            <if test="type != null and type != ''">
                AND `type` = #{type}
            </if>
            <if test="startTime != null and endTime != null and startTime != '' and endTime != ''">
                AND `update_time` between #{startTime} and #{endTime}
            </if>
        </where>
        ORDER BY id DESC
    </select>

    <select id="getById" resultType="com.hdec.data.domain.Outline">
        SELECT
            *
        FROM
            owp_report_outline
        WHERE
            id = #{id}
    </select>

    <insert id="add" parameterType="com.hdec.data.domain.Outline" useGeneratedKeys="true" keyProperty="id" >
        INSERT INTO owp_report_outline (
            `name`,
            `type`,
            create_time,
            update_time,
            user_id,
            username,
            field_num
        )
        VALUES (
            #{outline.name},
            #{outline.type},
            NOW(),
            NOW(),
            #{outline.userId},
            #{outline.username},
            #{outline.fieldNum}
        )
    </insert>

    <update id="update" parameterType="com.hdec.data.domain.Outline">
        UPDATE owp_report_outline
        <set>
            <if test="outline.name != null">`name` = #{outline.name},</if>
            <if test="outline.type != null">`type` = #{outline.type},</if>
            `update_time` = NOW(),
            <if test="outline.userId != null">`user_id` = #{outline.userId},</if>
            <if test="outline.username != null">`username` = #{outline.username},</if>
        </set>
        WHERE
            id = #{outline.id}
    </update>

    <delete id="delete">
        DELETE
        FROM
            owp_report_outline
        WHERE
            id IN
            <foreach collection="ids" item="id" separator=", " open="(" close=")">
                #{id}
            </foreach>
    </delete>

    <insert id="addMould2Outline">
        INSERT INTO owp_report_nav_mould (`nav_id`, `mould_id`)
        VALUES (
            #{outlineId}, #{mouldId}
        )
    </insert>

    <insert id="removeMould2Outline">
        DELETE FROM
            owp_report_nav_mould
        WHERE
            nav_id = #{outlineId}
        <if test="mouldId != null">
            AND mould_id = #{mouldId}
        </if>
    </insert>

    <update id="setMouldValue" parameterType="com.hdec.data.qo.MouldVal">
        UPDATE
            owp_report_nav_mould
        SET
            var_value = #{mouldVal.varValue}
        WHERE
            nav_id = #{mouldVal.outlineId} AND mould_id = #{mouldVal.mouldId}
    </update>

    <select id="getMouldValue" resultType="string">
        SELECT
            var_value
        FROM
            owp_report_nav_mould
        WHERE
            nav_id = #{outlineId} AND mould_id = #{mouldId}
    </select>

    <select id="getMouldByOutlineMould" resultType="int">
        SELECT
            id
        FROM
            owp_report_nav_mould
        WHERE
            nav_id = #{outlineId} AND mould_id = #{mouldId}
        limit 1
    </select>

    <select id="getMouldByNavId" resultType="com.hdec.data.domain.template.Mould">
        SELECT
            *
        FROM
            owp_report_mould
        WHERE
            id IN (
                SELECT
                    mould_id
                FROM
                    owp_report_nav_mould
                WHERE
                    nav_id = #{navId}
            )
    </select>

    <select id="getRootNavsByOutlineId" resultType="com.hdec.data.domain.OutlineNav">
        SELECT
            *
        FROM
            owp_report_outline_nav
        WHERE
            pid = 0 AND outline_id = #{outlineId}
	</select>

    <select id="listByField" resultType="com.hdec.data.domain.Outline">
        SELECT
            `id`, `name`, `type`, `update_time`, `username`, `field_num`
        FROM
            owp_report_outline
        WHERE
            field_num = #{fieldNum}
        ORDER BY id DESC
    </select>

    <select id="getNavMouldContentsByOutline" resultType="com.hdec.data.domain.template.Mould">
        SELECT
            a.id,
            temp.nav_id,
            a.content
        FROM
            owp_report_mould a
            INNER JOIN ( SELECT nav_id, mould_id FROM owp_report_nav_mould WHERE nav_id IN ( SELECT id FROM owp_report_outline_nav WHERE outline_id = #{outlineId} ) ) temp ON a.id = temp.mould_id
    </select>

    <update id="updateOrder">
        UPDATE owp_report_outline_nav
        SET `order` = `order` + 1
        WHERE
            pid = #{pid}
            AND `order` is not null AND `order` >= #{order}
    </update>

    <select id="getMaxOrder" resultType="int">
        SELECT
            max( `order` )
        FROM
            owp_report_outline_nav
        WHERE
            pid = #{pid}
    </select>

    <select id="getSameMouldNavIds" resultType="int" parameterType="int">
        SELECT rnm.nav_id
        FROM owp_report_nav_mould rnm,
             owp_report_outline_nav ron
        WHERE rnm.mould_id IN (SELECT mould_id
                               FROM owp_report_nav_mould
                               where nav_id = #{navId})
          AND rnm.nav_id = ron.id
        ORDER BY ron.`order`, ron.id
    </select>

    <select id="navListByNavId" resultType="com.hdec.data.domain.OutlineNav">
        SELECT *
        FROM owp_report_outline_nav
        WHERE outline_id =
              (SELECT outline_id
               FROM owp_report_outline_nav
               WHERE id = #{navId})
        ORDER BY `order`, id
    </select>

</mapper>