package com.hdec.data.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@ToString
public class DataTableVo {

    /** 总条数 */
    private Integer total;

    /** 表头 */
    private TableHead[] head;

    /** 表体 */
    private List<List<CellVal>> body;

    /** 方向 */
    private Set<Integer> directs;

}
