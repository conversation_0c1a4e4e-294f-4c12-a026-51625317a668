package com.hdec.data.domain.v2;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
public class ExportTask implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Integer id;

    /**
     * 测点ID
     */
    @NotBlank(message = "测点ID不允许为空")
    private String pointIds;

    /**
     * 测点ID
     */
    @NotBlank(message = "分量ID不允许为空")
    private String attrIds;

    /**
     * 频率:高频/分钟/小时
     */
    private String rate;

    /**
     * 开始时间
     */
    @NotNull(message = "开始时间不允许为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 结束时间
     */
    @NotNull(message = "结束时间不允许为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 进度
     */
    private Double progress;

    /**
     * 文件地址
     */
    @JsonIgnore
    private String url;

    /**
     * 状态:
     * 0-等待中
     * 1-进行中
     * 2-已完成
     * -1-失败
     */
    private Integer status;

    /**
     * 描述
     */
    private String description;

    /**
     * 风场编码
     */
    private String fieldNum;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建人
     */
    private Integer createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 测点名称
     */
    private String pointNos;

    /**
     * 分量名称
     */
    private String attrNames;


    /**
     * 是否可下载
     */
    private boolean downloadable;

    public boolean isDownloadable() {
        return url != null && !url.trim().isEmpty();
    }
}
