package com.hdec.data.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class ChartAttr {

    /** 属性名 */
    private String attrName;

    /** 属性别名 */
    private String attrAliasName;

    /** 是否随值调整 */
    private Boolean isAuto;

    /** 最小值 */
    private Double min;

    /** 最大值 */
    private Double max;

    public String getAttrAliasName() {
        if (ObjectUtils.isEmpty(attrAliasName)) {
            return attrName;
        }
        return attrAliasName;
    }
}
