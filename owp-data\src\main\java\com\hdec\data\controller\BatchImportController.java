package com.hdec.data.controller;

import com.hdec.common.domain.R;
import com.hdec.data.domain.BatchImportCfg;
import com.hdec.data.service.BatchImportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 批量导入控制器
 *
 * <AUTHOR>
 */
@Api(tags = "批量导入")
@RestController
@RequestMapping("api/data/batchImport")
public class BatchImportController {

    @Autowired
    private BatchImportService importService;

    /**
     * 查找配置
     */
    @ApiOperation("查找配置")
    @GetMapping("selectCfg/{type}")
    public R selectCfg(@RequestHeader("fieldNum") String fieldNum, @PathVariable Integer type) {
        BatchImportCfg importCfg = importService.selectCfg(type, fieldNum);
        return R.success(importCfg);
    }

    /**
     * 保存配置
     */
    @ApiOperation("保存配置")
    @PostMapping("saveCfg")
    public R saveCfg(@RequestHeader("fieldNum") String fieldNum, @RequestBody BatchImportCfg cfg) {
        cfg.setFieldNum(fieldNum);
        importService.deleteCfg(cfg);
        importService.saveCfg(cfg);
        return R.success("保存成功");
    }

    /**
     * 移除含有某分量的配置参数
     */
    @ApiOperation("移除含有某分量的配置参数")
    @GetMapping("removeParamByAttr/{attrId}")
    public R removeParamByAttr(@RequestHeader("fieldNum") String fieldNum, @PathVariable Integer attrId) {
        importService.removeParamByAttr(fieldNum, attrId);
        return R.success("操作成功");
    }

}
