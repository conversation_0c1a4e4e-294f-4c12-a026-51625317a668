package com.hdec.data.service;

import com.hdec.common.formula.AttrVal;
import com.hdec.common.formula.AttrVar;
import com.hdec.common.formula.DatabaseExecutor;
import com.hdec.data.domain.taos.Record;
import com.hdec.data.mapper.TaosMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


@Slf4j
@Service
public class FormulaCalcDataSourceExecutor implements DatabaseExecutor {

    @Resource
    private TaosMapper taosMapper;

    @Resource
    private TaosService taosService;

    @Override
    public List<AttrVal> select(AttrVar result, AttrVar target, String valCol, String interval, Date startTime, Date endTime) {
        String tableName = taosService.getTableName(target.getRate(), Integer.parseInt(target.getInst()), true);

        if (tableName.trim().isEmpty()) {
            log.error("公式数据查询-数据库表名错误:" + target.getRate() + "_" + target.getInst() + "_" + target.getPoint());
            throw new RuntimeException("数据库表名错误:" + target.getRate() + "_" + target.getInst() + "_" + target.getPoint());
        }
        String sqlTemp = "select #{ts} as ts, #{exp} as val " +
                "from #{table} " +
                "where point = #{point} and ts >= '#{start}' and ts < '#{end}' #{interval}";
        String sql = sqlTemp.replace("#{ts}", interval == null ? "ts" : "_wstart")
                .replace("#{table}", tableName)
                .replace("#{point}", target.getPoint())
                .replace("#{exp}", valCol)
                .replace("#{interval}", interval == null ? "" : "interval(" + interval + ")")
                .replace("#{start}", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(startTime))
                .replace("#{end}", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(endTime));
        return select(result, sql);
    }

    @Override
    public List<AttrVal> select(AttrVar attrVar, Date startTime, Date endTime) {
        String rate = attrVar.getRate();
        String tableName = taosService.getTableName(rate, Integer.parseInt(attrVar.getInst()), true);
        String col = "a_" + attrVar.getAttr() + "_" + attrVar.getDirect();
        if (tableName.trim().isEmpty()) {
            log.error("公式数据查询-数据库表名错误:" + attrVar.getRate() + "_" + attrVar.getInst() + "_" + attrVar.getPoint());
            throw new RuntimeException("数据库表名错误:" + attrVar.getRate() + "_" + attrVar.getInst() + "_" + attrVar.getPoint());
        }
        String sqlTemp = "select #{ts} as ts, #{col} as val " +
                "from #{table} " +
                "where point = #{point} and ts >= '#{start}' and ts < '#{end}'";
        String sql = sqlTemp.replace("#{ts}", "ts")
                .replace("#{col}", col)
                .replace("#{table}", tableName)
                .replace("#{point}", attrVar.getPoint())
                .replace("#{start}", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(startTime))
                .replace("#{end}", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(endTime));
        return select(attrVar, sql);
    }

    @Override
    public AttrVal select(AttrVar attrVar, Date ts) {
        return taosService.selectAttrVar(attrVar, ts);
    }

    @Override
    public int insert(String tp, String targetCol, String sp, String valCol, Date startTime, Date endTime) {
        return 0;
    }

    /**
     * 查询数据
     *
     * @param resVar 结果分量信息
     * @param sql    SQl
     * @return {@link List }<{@link AttrVal }>
     */
    private List<AttrVal> select(AttrVar resVar, String sql) {
        long l = System.currentTimeMillis();
        List<Record> record = taosMapper.selectSql(sql);
        long t = System.currentTimeMillis() - l;
        log.info("公式计算执行器-查询耗时[ {} ms]-SQL[{}]", t, sql);
        return record.stream().map(e -> {
            Double val = e.getDouble("val");
            if (val == null) {
                return null;
            }
            return new AttrVal(e.getTs(),
                    resVar.getInst(),
                    resVar.getPoint(),
                    resVar.getAttr(),
                    resVar.getDirect(),
                    resVar.getRate(),
                    val);
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }
}
