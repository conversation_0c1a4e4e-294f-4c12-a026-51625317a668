package com.hdec.data.domain.domin_word;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * 海上升压站信息
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class ReportBoosterInfo {

    /** 不均匀沉降-文字 */
    private String refPoint;
    private String tarPoints;

    private Double settleMin;
    private Double settleMax;
    private Double settleAvgMin;
    private Double settleAvgMax;

    /** 不均匀沉降-图片 */
    private Integer picIndex;
    private String settlePicBase64;

    /** 不均匀沉降-表格 */
    private List<SettlePoint> settlePoints;

    /** 倾斜 */
    private Double tiltMin;
    private Double tiltMax;
    private Double tiltAvg;

    /** 振动 */
    private Double shockMin;
    private Double shockMax;

    /** 应力-文字 */
    private Double stressMin;
    private Double stressMax;
    private Double stressAvgMin;
    private Double stressAvgMax;

    /** 应力-图片 */
    private String stressPic;

    /** 应力-表格 */
    private List<StressPoint> stressPoints;

    /** 腐蚀 */
    private Integer corrodeSize;
    private String corrodePointNos;
    private String corrodePointValues;
    private String corrodePic;

}
