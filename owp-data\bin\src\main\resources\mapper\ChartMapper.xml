<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hdec.data.mapper.ChartMapper">

    <select id="list" resultType="com.hdec.data.domain.Chart">
        SELECT
            *
        FROM
            owp_chart
        WHERE
            field_num = #{fieldNum}
        ORDER BY `create_time` DESC
    </select>

    <insert id="add" parameterType="com.hdec.data.domain.Chart" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO owp_chart (
            ids, item_name, attr_names, start_time, end_time, img_type, is_mark, point_num, create_time, status, field_num
        )
        VALUES
            (#{chart.ids}, #{chart.itemName}, #{chart.attrNames}, #{chart.startTime}, #{chart.endTime}, #{chart.imgType}, #{chart.isMark}, #{chart.pointNum}, NOW(), #{chart.status}, #{chart.fieldNum})
    </insert>

    <update id="updateStatus">
        UPDATE
            owp_chart
        SET
            `status` = #{status},
            `url` = #{url}
        WHERE
            id = #{id}
    </update>

    <delete id="delete">
        DELETE
        FROM
            owp_chart
        WHERE
            id IN
            <foreach collection="ids" item="id" separator=", " open="(" close=")">
                #{id}
            </foreach>
    </delete>

</mapper>