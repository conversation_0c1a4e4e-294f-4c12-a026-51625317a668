package com.hdec.data.domain.report;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class ReportPic {

    /** 测点编号 */
    private String id;

    private String seaFacilityId;

    private String type;

    /** 测点编号 */
    private String pointNo;

    /** 测点编号 */
    private String base64;
    private String directStr;

    public ReportPic(String id, String seaFacilityId, String type, String pointNo, String base64, String directStr) {
        this.id = id;
        this.seaFacilityId = seaFacilityId;
        this.type = type;
        this.pointNo = pointNo;
        this.base64 = base64;
        this.directStr = directStr;
    }


    @Override
    public String toString() {
        if (base64 != null && base64.length() > 30) {
            return "ReportPic{" +
                    "id='" + id + '\'' +
                    ", seaFacilityId='" + seaFacilityId + '\'' +
                    ", type='" + type + '\'' +
                    ", pointNo='" + pointNo + '\'' +
                    ", base64='" + base64.substring(0, 20) + '\'' +
                    '}';
        }
        return "ReportPic{" +
                "id='" + id + '\'' +
                ", seaFacilityId='" + seaFacilityId + '\'' +
                ", type='" + type + '\'' +
                ", pointNo='" + pointNo + '\'' +
                ", base64='" + base64 + '\'' +
                '}';
    }



}
