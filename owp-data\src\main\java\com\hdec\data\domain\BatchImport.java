package com.hdec.data.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/11/7
 */
@Data
@ToString
@NoArgsConstructor
public class BatchImport {

    /** 自增主键 */
    private Integer id;

    /** 导入时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date time;

    /** 导入方式（1：预置格式录入  2：Excel表格录入） */
    private Integer type;

    /** 是否覆盖历史数据 */
    private Boolean isOverride;

    /** 导入状态（0：进行中  1：已完成） */
    private Boolean status;

    /** 文件名 */
    private String filename;

    /** 文件路径 */
    private String filepath;

    /** 用户ID */
    private Integer userId;

    /** 用户名 */
    private String userName;

    private Double process;

    /** 风场编码 */
    private String fieldNum;

    /** 导入结果 */
    private String importRes;
}
