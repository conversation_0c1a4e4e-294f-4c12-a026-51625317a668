package com.hdec.data.util;

import com.spire.doc.Document;
import com.spire.doc.FileFormat;
import com.spire.doc.ToPdfParameterList;
import lombok.extern.slf4j.Slf4j;

/**
 * Word转pdf
 */
@Slf4j
public class WordToPdfUtil {

    public static void convert(String inPath, String outPath) {
        Document doc = new Document();
        doc.loadFromFile(inPath);

        ToPdfParameterList ppl = new ToPdfParameterList();
        ppl.isEmbeddedAllFonts(true);
        ppl.setDisableLink(true);

        doc.saveToFile(outPath, ppl);
    }

    /**
     * word转pdf
     */
    public static void word2pdf(String path) throws Exception {
        Document document = new Document();
        document.loadFromFile(path);

        String pdfPath = path.replace(".docx", ".pdf");

        if (document.getPageCount() <= 3) {
            /* 使用Spire.Doc */
            document.saveToFile(pdfPath, FileFormat.PDF);
        } else {
            /* 使用unoconv */
            Runtime runtime = Runtime.getRuntime();
            String[] command = {"unoconv", "-f", "pdf", "-o", pdfPath, path};
            Process exec = runtime.exec(command);
            exec.waitFor();
        }
    }

}
