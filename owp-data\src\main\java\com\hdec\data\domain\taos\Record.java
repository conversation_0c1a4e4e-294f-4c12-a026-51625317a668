package com.hdec.data.domain.taos;

import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.HashMap;

@NoArgsConstructor
public class Record extends HashMap<String, Object> {

    public Date getTs() {
        return (Date) get("ts");
    }

    public Float getFloat(String key) {
        if (contains<PERSON><PERSON>(key)) {
            Object value = get(key);
            if (value instanceof Number) {
                return ((Number) value).floatValue();
            }
        }
        return null;
    }

    public Double getDouble(String key) {
        if (containsKey(key)) {
            Object value = get(key);
            if (value instanceof Number) {
                return ((Number) value).doubleValue();
            }
        }
        return null;
    }

    public Boolean getBoolean(String key) {
        if (contains<PERSON>ey(key)) {
            Object value = get(key);
            if (value instanceof Boolean) {
                return (Boolean) value;
            }
        }
        return null;
    }

    public Integer getInteger(String key) {
        if (containsKey(key)) {
            Object value = get(key);
            if (value instanceof Number) {
                return ((Number) value).intValue();
            }
        }
        return null;
    }

    public Long getLong(String key) {
        if (contains<PERSON>ey(key)) {
            Object value = get(key);
            if (value instanceof Number) {
                return ((Number) value).longValue();
            }
        }
        return null;
    }

}
