package com.hdec.data.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * 过程线图变量实体类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class DiagramVar {

    /** 海上设施ID */
    private List<String> seaFacilityIds;

    /** 是否是双坐标轴 */
    private Boolean isDoubleAxis;

    /** 单图中显示的最大测点数 */
    private Integer pointNum;

    /** 是否按方向划分 */
    private Boolean isDirectDivided;

    /** 所占宽度 */
    private Integer occupyWidth;

    /** 分量名 */
    private List<String> attrNames;

    /** 方向 */
    private Integer direct;

    public DiagramVar(List<String> seaFacilityIds, Boolean isDoubleAxis, Integer pointNum, Boolean isDirectDivided, Integer occupyWidth, List<String> attrNames, Integer direct) {
        this.seaFacilityIds = seaFacilityIds;
        this.isDoubleAxis = isDoubleAxis;
        this.pointNum = pointNum;
        this.isDirectDivided = isDirectDivided;
        this.occupyWidth = occupyWidth;
        this.attrNames = attrNames;
        this.direct = direct;
    }
}
