package com.hdec.data.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hdec.common.domain.OperaLog;
import com.hdec.common.domain.R;
import com.hdec.common.domain.ResourceCommon;
import com.hdec.common.qo.ReportStatQo;
import com.hdec.common.util.TimeUtil;
import com.hdec.data.domain.Report;
import com.hdec.data.domain.ReportPerson;
import com.hdec.data.feign.UserService;
import com.hdec.data.qo.FinalReportQo;
import com.hdec.data.qo.ReportQo;
import com.hdec.data.service.DataService;
import com.hdec.data.service.ReportBaseInfoService;
import com.hdec.data.service.ReportManageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 报告管理控制器
 *
 * <AUTHOR>
 */
@Api(tags = "报告管理")
@Validated
@RestController
@RequestMapping("api/data/reportManage")
public class ReportManageController {

    @Autowired
    private DataService dataService;

    @Autowired
    private ReportManageService reportService;

    @Autowired
    private ReportBaseInfoService infoService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private UserService userService;

    /**
     * 生成月报
     */
    @ApiOperation("生成月报")
    @PostMapping("genReport")
    public R genReport(@RequestBody ReportStatQo qo) throws Exception {
        /* 先保存报告 */
        Report report = new Report();
        report.setType("监测月报");
        report.setPeriodStart(TimeUtil.parse2Day(qo.getStartTime()));
        report.setPeriodEnd(TimeUtil.parse2Day(qo.getEndTime()));
        report.setIssueYear(qo.getIssueYear());
        report.setIssueNum(qo.getIssueNum());
        report.setGenTime(new Date());
        report.setWriteTime(TimeUtil.parse2Day(qo.getWriteTime()));
        ReportPerson person = infoService.getPersonConf(qo.getFieldNum());
        if (person != null) {
            report.setEditor(person.getEditor());
            report.setChecker(person.getChecker());
            report.setReviewer(person.getReviewer());
        }
        report.setVersion("生成版");
        report.setStatus("生成中(0%)");
        report.setFieldNum(qo.getFieldNum());
        reportService.save(report);

        /* 异步生成报告 */
        dataService.genReport(report.getId(), qo);
        userService.saveOperaLog(new OperaLog("报告管理", "生成", 1));
        return R.success("正在生成中");
    }

    /**
     * 上传最终稿月报
     */
    @ApiOperation("上传最终稿月报")
    @PostMapping("finalReport")
    public R finalReport(@RequestBody FinalReportQo qo) {
        userService.saveOperaLog(new OperaLog("报告管理", "上传", 1));
        return reportService.finalReport(qo);
    }

    /**
     * 项目级报告列表
     */
    @ApiOperation("项目级报告列表")
    @PostMapping("pro/list")
    public R proList(@RequestHeader("fieldNum") String fieldNum, @RequestBody ReportQo qo) {
        PageHelper.startPage(qo.getPageNum(), qo.getPageSize());
        List<Report> reports = reportService.proList(fieldNum, qo);
        return R.success(new PageInfo<>(reports));
    }

    /**
     * 企业级报告列表
     */
    @ApiOperation("企业级报告列表")
    @PostMapping("ent/list")
    public R entList(@RequestHeader("sessionId") String sessionId, @RequestBody ReportQo qo) {
        ResourceCommon resource = (ResourceCommon) redisTemplate.opsForValue().get(sessionId);
        List<String> fieldNums = userService.getFieldResource(resource.getUnitId());

        List<Report> reports = reportService.entList(qo, fieldNums);

        PageInfo<Report> pageInfo = new PageInfo<>();
        pageInfo.setTotal(reports.size());
        List<Report> list = reports.stream().skip((qo.getPageNum() - 1) * qo.getPageSize()).limit(qo.getPageSize()).collect(Collectors.toList());
        pageInfo.setList(list);
        return R.success(pageInfo);
    }

    /**
     * 获取所有期数
     */
    @ApiOperation("获取所有期数")
    @PostMapping("ent/allIssueNums")
    public R entAllIssueNums(@RequestHeader("sessionId") String sessionId) {
        ResourceCommon resource = (ResourceCommon) redisTemplate.opsForValue().get(sessionId);
        List<String> fieldNums = userService.getFieldResource(resource.getUnitId());

        List<String> issueNums = reportService.entAllIssueNums(fieldNums);
        return R.success(issueNums);
    }

    /**
     * 查询某年的总期数
     */
    @ApiOperation("查询某年的总期数")
    @PostMapping("pro/issueNumByYear/{issueYear}")
    public R proIssueNumByYear(@RequestHeader("fieldNum") String fieldNum, @PathVariable("issueYear") Integer issueYear) {
        Integer issueNum = reportService.proIssueNumByYear(fieldNum, issueYear);
        return R.success(issueNum);
    }

    /**
     * 批量删除报告
     */
    @ApiOperation("批量删除报告")
    @DeleteMapping("{ids}")
    public R deleteBatch(@NotNull(message = "请勾选待删除项") @PathVariable Integer[] ids) {
        reportService.delete(ids);
        userService.saveOperaLog(new OperaLog("报告管理", "删除", 1));
        return R.success("删除成功");
    }

}
