package com.hdec.data.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class TimeMonthVo {

    /** 最大值*/
    private Float maxValue;

    /** 最大值时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date maxValueTime;

    /** 最小值*/
    private Float minValue;

    /** 最小值时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date minValueTime;

    /** 月份 */
    private String month;

    /** 测次*/
    private Integer measureNumber;

    /** 月变幅 */
    private Float monthVariation;

    /** 月平均 */
    private Float monthAve;
}
