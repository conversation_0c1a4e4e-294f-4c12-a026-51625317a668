package com.hdec.data.qo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
@ToString
@NoArgsConstructor
public class HistoryEigenTabQo {

    /**
     * 统计分量
     */
    @NotNull(message = "统计分量不能为空！")
    StatAttr statAttr;
    /**
     * 海上设施类型
     * 风机编号/升压站
     * 废弃-未使用
     */
    private String facility;
    /**
     * 仪器ID
     */
    @NotNull(message = "仪器ID不能为空！")
    private Integer instId;
    /**
     * 是否显示安装方位
     */
    private Boolean isShowInstallOrient;
    /**
     * 是否显示安装高程
     */
    private Boolean isShowInstallEle = false;
    /**
     * 开始日期
     */
    @NotNull(message = "结束日期不能为空！")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startDate;

}
