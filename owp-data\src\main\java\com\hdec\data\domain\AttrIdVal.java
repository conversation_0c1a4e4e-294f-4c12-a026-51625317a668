package com.hdec.data.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class AttrIdVal implements Serializable {

    private Integer attrId;

    private Float attrVal;

    public AttrIdVal(Integer attrId, Float attrVal) {
        this.attrId = attrId;
        this.attrVal = attrVal;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AttrIdVal attrIdVal = (AttrIdVal) o;
        return Objects.equals(attrId, attrIdVal.attrId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(attrId);
    }
}
