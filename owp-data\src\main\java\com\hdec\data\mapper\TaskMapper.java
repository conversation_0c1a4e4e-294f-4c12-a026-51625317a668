package com.hdec.data.mapper;

import com.hdec.data.domain.Task;
import com.hdec.data.qo.TaskQo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 任务Dao层
 *
 * <AUTHOR>
 */
@Mapper
public interface TaskMapper {

    /**
     * 任务列表
     */
    List<Task> list(@Param("qo") TaskQo qo, @Param("fieldNums") List<String> fieldNums);

    /**
     * 查询最早的N条任务
     */
    List<Task> selectTopN(@Param("N") int N);

    /**
     * 重置任务状态
     */
    void resetTaskStatus();

    /**
     * 保存任务
     */
    void saveTask(@Param("task") Task task);

    /**
     * 批量保存任务
     */
    void saveTasks(@Param("task") List<Task> tasks);

    /**
     * 更新任务
     */
    void update(@Param("task") Task task);

    /**
     * 更新任务（包括NULL值）
     */
    void updateWithNull(@Param("task") Task task);

    /**
     * 批量更新
     */
    void updateList(@Param("tasks") List<Task> tasks);

    /**
     * 删除任务
     */
    void delete(@Param("id") Integer id);

    /**
     * 批量删除任务
     */
    void deletes(@Param("ids") Integer[] ids);

    /**
     * 按等级查询最早的任务
     */
    Task selectFirstTaskByLevel(@Param("level") String level);

    /**
     * 查询某风场未完成任务的参数
     */
    List<String> selectNotFinishedParamByField(@Param("fieldNum") String fieldNum);
}
