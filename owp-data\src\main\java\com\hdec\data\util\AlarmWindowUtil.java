package com.hdec.data.util;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.hdec.data.vo.AlarmWindowVo;

import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * 告警窗口工具
 *
 * <AUTHOR>
 * @date 2025-04-27
 */
public class AlarmWindowUtil {

    /**
     * 普通告警窗口缓存时常
     */
    private static final Integer ALARM_NORMAL_WINDOW_DURATION = 200;
    /**
     * 普通告警窗口缓存
     */
    private static final Cache<String, AlarmWindowVo> ALARM_NORMAL_WINDOW = CacheBuilder.newBuilder()
            .expireAfterWrite(ALARM_NORMAL_WINDOW_DURATION, TimeUnit.SECONDS)
            .build();

    /**
     * 普通告警窗口清盘
     *
     * @param point point 测点
     * @param time  time 时间
     * @return {@link AlarmWindowVo }
     */
    public static synchronized AlarmWindowVo normalWindup(Integer point, Integer attr, Integer direction, Date time) {
        if (point == null || attr == null || direction == null || time == null) {
            return null;
        }
        String key = point + "_" + attr + "_" + direction;
        AlarmWindowVo window = ALARM_NORMAL_WINDOW.getIfPresent(key);
        if (window != null) {
            if (time.after(window.getExpire())) {
                ALARM_NORMAL_WINDOW.invalidate(key);
                return window;
            }
        }
        return null;
    }

    /**
     * 普通告警窗口计数
     *
     * @param point      point 测点
     * @param value      value 值
     * @param time       time 时间
     * @param windowTime window time 窗口时常（最长180s）
     */
    public static synchronized void normalCountUp(Integer point, Integer attr, Integer direction, Double value, Date time, Long windowTime) {
        if (point == null || attr == null || direction == null || value == null || time == null) {
            return;
        }
        windowTime = windowTime == null || windowTime > ALARM_NORMAL_WINDOW_DURATION ? ALARM_NORMAL_WINDOW_DURATION - 20 : windowTime;
        String key = point + "_" + attr + "_" + direction;
        AlarmWindowVo window = ALARM_NORMAL_WINDOW.getIfPresent(key);
        if (window == null) {
            long expire = time.getTime() + windowTime * 1000;
            ALARM_NORMAL_WINDOW.put(key, new AlarmWindowVo(point, attr, direction, value, time, new Date(expire)));
        } else {
            window.addCountAndMax(value, time);
        }

    }

    /**
     * 延迟检测告警窗口缓存时常
     */
    private static final Integer ALARM_DDC_WINDOW_DURATION = 200;
    /**
     * 延迟监测告警窗口缓存
     */
    private static final Cache<String, AlarmWindowVo> ALARM_DDC_WINDOW = CacheBuilder.newBuilder()
            .expireAfterWrite(ALARM_DDC_WINDOW_DURATION, TimeUnit.SECONDS)
            .build();

    /**
     * 延迟检测告警窗口清盘
     *
     * @param point point 测点
     * @param time  time 时间
     * @return {@link AlarmWindowVo }
     */
    public static synchronized AlarmWindowVo ddcWindup(Integer point, Integer attr, Integer direction, Date time) {
        if (point == null || attr == null || direction == null || time == null) {
            return null;
        }
        String key = point + "_" + attr + "_" + direction;
        AlarmWindowVo window = ALARM_DDC_WINDOW.getIfPresent(key);
        if (window != null) {
            if (time.after(window.getExpire())) {
                ALARM_DDC_WINDOW.invalidate(key);
                return window;
            }
        }
        return null;
    }

    /**
     * 延迟检测告警窗口计数
     *
     * @param point      point 测点
     * @param time       time 时间
     * @param value      value 值
     * @param windowTime window time 窗口时常（最长120s）
     */
    public static synchronized void ddcCountUp(Integer point, Integer attr, Integer direction, Double value, Date time, Long windowTime) {
        if (point == null || attr == null || direction == null || value == null || time == null) {
            return;
        }
        windowTime = windowTime == null || windowTime > ALARM_DDC_WINDOW_DURATION ? ALARM_DDC_WINDOW_DURATION - 20 : windowTime;
        String key = point + "_" + attr + "_" + direction;
        AlarmWindowVo window = ALARM_DDC_WINDOW.getIfPresent(key);
        if (window == null) {
            long expire = time.getTime() + windowTime * 1000;
            ALARM_DDC_WINDOW.put(key, new AlarmWindowVo(point, attr, direction, value, time, new Date(expire)));
        } else {
            window.addCountAndMax(value, time);
        }
    }


    public static void main(String[] args) {
        long time = System.currentTimeMillis() - 24 * 60 * 60 * 1000;
        for (int i = 0; i < 10000; i++) {
            double value = Math.random();
            Date date = new Date(time + i * 10);
            AlarmWindowUtil.normalCountUp(1, 1, 1, value, date, 10L);
            AlarmWindowVo window = AlarmWindowUtil.normalWindup(1, 1, 1, date);
            if (window != null) {
                System.out.println(window);
            }
        }

        System.out.println("=================================================================================================================================================");
        for (int i = 0; i < 10000; i++) {
            double value = Math.random();
            Date date = new Date(time + i * 10);
            AlarmWindowUtil.ddcCountUp(1, 1, 1, value, date, 10L);
            AlarmWindowVo window = AlarmWindowUtil.ddcWindup(1, 1, 1, date);
            if (window != null) {
                System.out.println(window);
            }
        }
    }
}
