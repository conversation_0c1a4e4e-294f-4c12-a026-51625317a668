package com.hdec.data.qo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class DataQueryMinQo {

    private String rate;

    /** 测点编号ID */
    private Integer pointId;

    /** 测点编号 */
    private String pointNo;

    /** 方向 */
    private Integer direct;

    /** 分量ID */
    private Integer attrId;

    /** 分量名称 */
    private String attrName;

    /** 开始时间 */
    private String startTime;

    /** 结束时间 */
    private String endTime;

    /** 当前页 */
    private Integer pageNum;

    /** 每页条数 */
    private Integer pageSize;

    /** 审核状态（0：未审核  1：通过  2：未通过） */
    private Integer[] approval;

    /** 小于等于 */
    private Float lt;

    /** 连接符（and / or） */
    private String connector;

    /** 大于等于 */
    private Float gt;

    /** 最小值 */
    private Float min;

    /** 最大值 */
    private Float max;

    private String fieldNum;

}
