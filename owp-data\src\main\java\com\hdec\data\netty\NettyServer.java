package com.hdec.data.netty;

import com.hdec.data.constant.Constant;
import com.hdec.data.util.HexUtil;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelOption;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.handler.codec.DelimiterBasedFrameDecoder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * Netty服务端
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class NettyServer {

    /** 目标客户端 */
    @Value("${netty.target}")
    private String nettyTarget;

    /** 服务器端口 */
    @Value("${netty.port}")
    private Integer nettyPort;

    /** socket缓存大小100KB，提高吞吐量 */
    private static final Integer SOCKET_BUF_SIZE = 100 * 1024 * 1024;

    /**
     * 启动服务
     */
    public void startServer() throws InterruptedException {
        /* 创建两个NIO线程组，一个用于接收客户端连接，一个用于通道的读写 */
        EventLoopGroup bossGroup = new NioEventLoopGroup(1);
        EventLoopGroup workGroup = new NioEventLoopGroup();

        /* 设置配置信息 */
        ServerBootstrap bootstrap = new ServerBootstrap();
        bootstrap.option(ChannelOption.SO_SNDBUF, SOCKET_BUF_SIZE);
        bootstrap.option(ChannelOption.SO_RCVBUF, SOCKET_BUF_SIZE);
        bootstrap.group(bossGroup, workGroup)
                .channel(NioServerSocketChannel.class)
                .childHandler(new ChannelInitializer<SocketChannel>() {
                    @Override
                    protected void initChannel(SocketChannel socketChannel) {
                        if (Constant.CLIENT_SIGN_DM.equals(nettyTarget)) {
                            /* DM客户端 */
                            socketChannel.pipeline().addLast(new DelimiterBasedFrameDecoder(2000, false, Unpooled.copiedBuffer(HexUtil.hexToBytes("CCCCCCCC"))));
                            socketChannel.pipeline().addLast(new NettyHandlerDM());
                        } else if (Constant.CLIENT_SIGN_3F.equals(nettyTarget)) {
                            /* 3F客户端 */
                            socketChannel.pipeline().addLast(new DelimiterBasedFrameDecoder(50000, false, Unpooled.copiedBuffer(HexUtil.hexToBytes("CCCCCCCCCCCC"))));
                            socketChannel.pipeline().addLast(new NettyHandler3F());
                        }
                        log.info("有动态设备连接");
                    }
                });
        try {
            /* 同步绑定端口，绑定成功后才返回 */
            ChannelFuture future = bootstrap.bind(nettyPort).sync();
            log.info("动态设备启动，端口：{}", nettyPort);

            /* 同步阻塞，等待服务器链路关闭后才结束main方法 */
            future.channel().closeFuture().sync();
        } finally {
            /* 优雅退出，释放线程池资源 */
            bossGroup.shutdownGracefully();
            workGroup.shutdownGracefully();
        }
    }

}
