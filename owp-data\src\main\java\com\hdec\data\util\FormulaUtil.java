package com.hdec.data.util;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;

import static java.util.regex.Pattern.compile;

/**
 * 公式工具类
 *
 * <AUTHOR>
 * @date 2025/02/28
 */
public class FormulaUtil {

    private static final String ATTR_PREFIX = "a_";
    private static final String PARAM_PREFIX = "p_";
    private static final String NONE_PREFIX = "";

    private static final String ATTR_SYMBOL_REGEX = "#\\{[\\d+:]+}";

    private static final String PARAM_SYMBOL_REGEX = "@\\{[\\d+:]+}";

    private static final String NORMAL_FUNC_SYMBOL_REGEX = "\\$\\{([^}]*)\\}";

    private static final String AGGR_FUNC_SYMBOL_REGEX = "&\\{([^}]*)\\}";

    /**
     * 获取公式中所有的分量
     *
     * @param express 公式
     * @return {@link List }<{@link String }>
     */
    public static List<String> collectAttr(String express) {
        return collect(express, ATTR_SYMBOL_REGEX, ATTR_PREFIX);
    }

    /**
     * 清除分量占位符
     *
     * @param express 公式
     * @return {@link String }
     */
    public static String clearAttrSymbol(String express) {
        return clearSymbol(express, ATTR_SYMBOL_REGEX, ATTR_PREFIX);
    }

    /**
     * 收集仪器参数
     *
     * @param express 公式
     * @return {@link List }<{@link String }>
     */
    public static List<String> collectParam(String express) {
        return collect(express, PARAM_SYMBOL_REGEX, PARAM_PREFIX);
    }

    /**
     * 清除仪器参数占位符
     *
     * @param express 公式
     * @return {@link List }<{@link String }>
     */
    public static String clearParamSymbol(String express) {
        return clearSymbol(express, PARAM_SYMBOL_REGEX, PARAM_PREFIX);
    }

    /**
     * 收集常规函数
     *
     * @param express 公式
     * @return {@link List }<{@link String }>
     */
    public static List<String> collectNormalFunc(String express) {
        return collect(express, NORMAL_FUNC_SYMBOL_REGEX, NONE_PREFIX);
    }

    /**
     * 清除常规函数占位符
     *
     * @param express 公式
     * @return {@link String }
     */
    public static String clearNormalFuncSymbol(String express) {
        return clearSymbol(express, NORMAL_FUNC_SYMBOL_REGEX, NONE_PREFIX);
    }

    /**
     * 收集聚合函数
     *
     * @param express 公式
     * @return {@link List }<{@link String }>
     */
    public static List<String> collectAggrFunc(String express) {
        return collect(express, AGGR_FUNC_SYMBOL_REGEX, NONE_PREFIX);
    }

    /**
     * 清除聚合函数占位符
     *
     * @param express 公式
     * @return {@link String }
     */
    public static String clearAggrFuncSymbol(String express) {
        return clearSymbol(express, AGGR_FUNC_SYMBOL_REGEX, NONE_PREFIX);
    }

    /**
     * 公式收集
     *
     * @param express 公式
     * @param regex   正则
     * @param prefix  前缀
     * @return {@link List }<{@link String }>
     */
    private static List<String> collect(String express, String regex, String prefix) {
        List<String> collect = new ArrayList<>();
        Matcher matcher = compile(regex).matcher(express);
        while (matcher.find()) {
            String group = matcher.group();
            String col = group.substring(group.indexOf("{") + 1, group.indexOf("}"))
                    .replaceAll(":", "_");
            collect.add(prefix + col);
        }
        return collect;
    }

    /**
     * 清除公式占位符
     *
     * @param express 公式
     * @return {@link List }<{@link String }>
     */
    private static String clearSymbol(String express, String regex, String prefix) {
        Matcher matcher = compile(regex).matcher(express);
        StringBuffer res = new StringBuffer();
        while (matcher.find()) {
            String group = matcher.group();
            String col = group.substring(group.indexOf("{") + 1, group.indexOf("}"))
                    .replaceAll(":", "_");
            matcher.appendReplacement(res, prefix + col);
        }
        matcher.appendTail(res);
        return res.toString();
    }

    private static Integer parseInt(String element, int index) {
        /* 去掉两边的花括号后按冒号分割 */
        String[] arr = element.substring(element.indexOf("{") + 1, element.indexOf("}")).split(":");
        if (index >= arr.length) {
            return null;
        }
        return Integer.parseInt(arr[index]);
    }
}
