package com.hdec.data.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.excel.read.metadata.ReadSheet;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 大数据量Excel读取工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class EasyExcelUtil {

    public Map<String, List<Map<String, String>>> readLocalExcel(String excelPath) {
        File file = new File(excelPath);
        EasyExcelReadListener listener = new EasyExcelReadListener();
        ExcelReaderBuilder excelReaderBuilder = EasyExcel.read(file, listener);
        ExcelReader excelReader = excelReaderBuilder.build();
        List<ReadSheet> sheets = excelReader.excelExecutor().sheetList();
        Map<String, List<Map<String, String>>> sheetData = new HashMap<>();
        for (ReadSheet sheet : sheets) {
            excelReaderBuilder.sheet(sheet.getSheetName()).doRead();
            sheetData.put(sheet.getSheetName(), listener.list);
            listener.list = new ArrayList<>();
        }
        return sheetData;
    }

}
