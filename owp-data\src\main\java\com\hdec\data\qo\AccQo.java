package com.hdec.data.qo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 加速度查询类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class AccQo {

    /** 测点编号 */
    private Integer pointId;

    /** 方向 */
    private Integer direct;

    /** 开始时间 */
    private String startTime;

    /** 结束时间 */
    private String endTime;

    public AccQo(Integer pointId, Integer direct, String startTime, String endTime) {
        this.pointId = pointId;
        this.direct = direct;
        this.startTime = startTime;
        this.endTime = endTime;
    }
}
