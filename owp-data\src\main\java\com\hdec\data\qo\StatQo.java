package com.hdec.data.qo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * 测值统计查询类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class StatQo {

    /** 开始时间 */
    private String startTime;

    /** 结束时间 */
    private String endTime;

    /** 特征值统计类型 */
    private String type;

    /** 测点ID */
    private List<Integer> pointIds;

    /** 测点编号 */
    private String[] pointNos;

    /** 是否是极坐标 */
    private Boolean isPolar;

    /** 分量ID */
    private Integer attrId;

    /** 分量ID数组 */
    private Integer[] attrIds;

    /** 分量名称 */
    private String attrName;

    /** 分量名称数组 */
    private String[] attrNames;

    /** 非自然月开始时间 */
    private Integer monthStartDay = 1;

    /** 非自然季开始时间 */
    private Integer quarterStartMonth = 1;

    /** 风场编码 */
    private String fieldNum;

    private Integer monitorId;

    private List<StatAttr> attrs;

    public StatQo(String startTime, String endTime, List<Integer> pointIds, Integer attrId, String fieldNum) {
        this.startTime = startTime;
        this.endTime = endTime;
        this.pointIds = pointIds;
        this.attrId = attrId;
        this.fieldNum = fieldNum;
    }

    public StatQo(String startTime, String endTime, List<Integer> pointIds, Integer attrId, Integer monitorId, String fieldNum) {
        this.startTime = startTime;
        this.endTime = endTime;
        this.pointIds = pointIds;
        this.attrId = attrId;
        this.monitorId = monitorId;
        this.fieldNum = fieldNum;
    }

}
