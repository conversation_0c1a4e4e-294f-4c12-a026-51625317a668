package com.hdec.data.service;

import com.hdec.common.domain.SeaFacilityCommon;
import com.hdec.data.feign.WindService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 变量解析基础类
 *
 * <AUTHOR>
 */
public class VarParseBase {

    @Autowired
    private WindService windService;

    /**
     * 获取所有风机ID
     */
    protected List<String> getSeaFacilityIds(String fieldNum, String s) throws Exception {
        List<SeaFacilityCommon> seaFacilities = windService.allSeaFacility(fieldNum);
        if ("所有风机".equals(s)) {
            // 获取该项目中所有风机ID
            return seaFacilities.stream().filter(e -> e.getId().startsWith("1-")).map(SeaFacilityCommon::getId).collect(Collectors.toList());
        } else if ("典型风机".equals(s)) {
            // 获取该项目中所有典型风机ID
            return seaFacilities.stream().filter(e -> e.getId().startsWith("1-")).filter(e -> e.getIsTypical() != null && e.getIsTypical()).map(SeaFacilityCommon::getId).collect(Collectors.toList());
        } else if ("非典型风机".equals(s)) {
            // 获取该项目中所有非典型风机ID
            return seaFacilities.stream().filter(e -> e.getId().startsWith("1-")).filter(e -> e.getIsTypical() == null || !e.getIsTypical()).map(SeaFacilityCommon::getId).collect(Collectors.toList());
        } else {
            // 指定风机ID
            return seaFacilities.stream().filter(e -> e.getId().equals(s)).limit(1).map(SeaFacilityCommon::getId).collect(Collectors.toList());
        }


//        if ("所有风机".equals(s)) {
//            // 获取该项目中所有风机ID
//            return seaFacilities.stream().filter(e -> e.getId().startsWith("1-")).map(SeaFacilityCommon::getId).collect(Collectors.toList());
//        } else if ("典型风机".equals(s)) {
//            // 获取该项目中所有典型风机ID
//            return seaFacilities.stream().filter(e -> e.getId().startsWith("1-")).filter(e -> e.getIsTypical() != null && e.getIsTypical()).map(SeaFacilityCommon::getId).collect(Collectors.toList());
//        } else if ("非典型风机".equals(s)) {
//            // 获取该项目中所有非典型风机ID
//            return seaFacilities.stream().filter(e -> e.getId().startsWith("1-")).filter(e -> e.getIsTypical() == null || !e.getIsTypical()).map(SeaFacilityCommon::getId).collect(Collectors.toList());
//        } else if ("@{风机编号}".equals(s)) {
//            // 指定风机ID
//            return seaFacilities.stream().filter(e -> e.getId().startsWith("1-")).limit(1).map(SeaFacilityCommon::getId).collect(Collectors.toList());
//        } else if ("@{升压站}".equals(s)) {
//            // 指定升压站ID
//            return seaFacilities.stream().filter(e -> e.getId().startsWith("2-")).limit(1).map(SeaFacilityCommon::getId).collect(Collectors.toList());
//        } else {
//            throw new Exception("非法参数");
//        }
    }

    /**
     * 获取方向
     */
    protected Integer getDirect(String s) throws Exception {
        if ("所有方向".equals(s)) {
            return null;
        } else if ("X方向".equals(s)) {
            return 1;
        } else if ("Y方向".equals(s)) {
            return 2;
        } else if ("Z方向".equals(s)) {
            return 3;
        } else {
            throw new Exception("非法参数-获取方向失败");
        }
    }

}



