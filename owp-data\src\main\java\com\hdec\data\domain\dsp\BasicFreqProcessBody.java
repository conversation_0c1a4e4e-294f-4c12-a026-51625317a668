package com.hdec.data.domain.dsp;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 分量值
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BasicFreqProcessBody {
    /**
     * 频率
     */
    private Double fs;

    /**
     * high
     */
    private Double low;

    /**
     * low
     */
    private Double high;

    /**
     * 数据
     */
    private List<Double> data;
}