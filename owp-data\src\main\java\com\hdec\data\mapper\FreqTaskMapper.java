package com.hdec.data.mapper;

import com.hdec.data.domain.FreqPic;
import com.hdec.data.domain.FreqTask;
import com.hdec.data.qo.FreqTaskQo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface FreqTaskMapper {

    /**
     * 任务列表
     */
    List<FreqTask> list(@Param("fieldNum") String fieldNum, @Param("qo") FreqTaskQo qo);
    List<FreqPic> picList(@Param("fieldNum") String fieldNum, @Param("qo") FreqTaskQo qo);

    /**
     * 按ID获取
     */
    FreqTask getById(@Param("id") Integer id);

    /**
     * 保存任务
     */
    void save(FreqTask freqTask);
    void picSave(FreqPic pic);

    /**
     * 批量删除任务
     */
    void delete(@Param("ids") Integer[] ids);
    void picDelete(@Param("ids") Integer[] ids);

    /**
     * 更新任务
     */
    void update(FreqTask task);

    FreqPic getPicById(@Param("id") Integer id);

    void updatePicStatus(@Param("id") Integer id, @Param("status") int status);
}