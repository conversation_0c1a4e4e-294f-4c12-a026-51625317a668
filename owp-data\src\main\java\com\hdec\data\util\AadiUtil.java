package com.hdec.data.util;

import com.alibaba.fastjson.JSON;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.NumberUtils;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class AadiUtil {

    private static final Logger logger = LoggerFactory.getLogger(AadiUtil.class);

    private static final String START_WITH = "MEASUREMENT";

    private static final String REGEX = "(?<!\\S)[-+]?\\d+(?:\\.\\d+)?(?!\\S)";

    private static final String CELL_INDEX = "Cell Index";

    /**
     * 解析字符串
     *
     * @param line 行
     */
    public static String mappingJson(String line) {
        if (!line.startsWith("MEASUREMENT")) {
            logger.error("非法行列数据-{}", line);
            return null;
        }
        line = line.replace(START_WITH, "");
        // 创建匹配器并替换：在数字后追加制表符
        Pattern pattern = Pattern.compile(REGEX);
        Matcher matcher = pattern.matcher(line);
        StringBuffer result = new StringBuffer();

        while (matcher.find()) {
            /*  追加匹配内容  */
            matcher.appendReplacement(result, "\t" + matcher.group() + "\n");
        }
        /*  添加剩余非匹配文本  */
        matcher.appendTail(result);
        Map<String, Object> map = new LinkedHashMap<>();
        List<Map<String, Number>> cells = new ArrayList<>();
        String[] groups = result.toString().split("\n");
        Map<String, Number> cell = null;
        for (int i = 0; i < groups.length; i++) {
            String group = groups[i];
            String[] kva = group.split("\t");
            List<String> kvs = Arrays.stream(kva).map(String::trim).filter(e -> !e.isEmpty())
                    .collect(Collectors.toList());
            String key = kvs.stream().findFirst().orElse(null);

            String value = kvs.stream().filter(e -> !Objects.equals(e, key))
                    .findFirst().orElse(null);
            if (key == null || value == null) {
                logger.error("数据解析失败");
                return null;
            }
            Number val = NumberUtils.parseNumber(value, Number.class);
            if (i == 0) {
                /*  第一个要解析成 Product Number 和 Serial Number  */
                map.put("Product Number", key);
                map.put("Serial Number", val);
            } else {
                if (cell != null || Objects.equals(CELL_INDEX, key)) {
                    /*  Cell 数据  */
                    if (cell == null || Objects.equals(CELL_INDEX, key)) {
                        /*  单元格索引  */
                        cell = new LinkedHashMap<>();
                        cell.put(key, val);
                        cells.add(cell);
                    } else {
                        /*  其他单元格数据  */
                        cell.put(key, val);
                    }
                } else {
                    /*  其他数据  */
                    map.put(key,val);
                }
            }
        }
        if (cell != null && !cell.isEmpty()) {
            map.put("Cells", cells);
        }
        return JSON.toJSONString(map);
    }

    public static void main(String[] args) throws IOException {
        Path path = Paths.get("/Users/<USER>/Desktop/原始数据2024.11.21.txt");
        List<String> lines = Files.readAllLines(path);
        for (String line : lines) {
            String s = mappingJson(line);
            System.out.println(s);
        }

    }
}
