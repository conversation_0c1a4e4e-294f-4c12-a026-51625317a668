package com.hdec.data.qo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class SloshQo {

    /** 测点ID */
    private Integer pointID;

    /** 开始时间 */
    private String startTime;

    /** 结束时间 */
    private String endTime;

    /** 夹角 */
    private Double angle;

    /** 主方向是否为相反数 */
    private Boolean isMainOpp;

    /** 垂直主方向是否为相反数 */
    private Boolean isVerticalOpp;

    public Boolean getMainOpp() {
        if (isMainOpp == null) {
            return true;
        }
        return isMainOpp;
    }

    public Boolean getVerticalOpp() {
        if (isVerticalOpp == null) {
            return true;
        }
        return isVerticalOpp;
    }
}
