package com.hdec.data.domain;

import com.hdec.common.util.Utils;
import com.hdec.data.vo.FrequencyVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.ObjectUtils;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FreqResult {

    /** 自增主键 */
    private Integer id;

    /** 测点ID */
    private Integer pointId;

    /** 测点编号 */
    private String pointNo;

    /** 方向ID */
    private Integer directId;

    /** 方向名称 */
    private String directName;

    /** 引用次数 */
    private Integer referNum;

    /** 滤波算法 */
    private String waveFilterAlg;

    /** 时间 */
    private String time;

    /** 时长 */
    private Integer duration;

    /** 时长单位（小时/天） */
    private String durationUnit;

    /** 频谱图url */
    private String imgUrl;

    /** 频谱数据 */
    private String data;

    private String taskParam;

    /** 风场编码 */
    private String fieldNum;

    private Double val;

    public FreqResult(Integer pointId, String pointNo, Integer directId, String time, String imgUrl, String fieldNum, Double val) {
        this.pointId = pointId;
        this.pointNo = pointNo;
        this.directId = directId;
        this.time = time;
        this.imgUrl = imgUrl;
        this.fieldNum = fieldNum;
        this.val = val;
    }

    public FreqResult(Integer pointId, String pointNo, Integer directId, String waveFilterAlg, String time, Integer duration, String durationUnit, String imgUrl, String taskParam, String fieldNum) {
        this.pointId = pointId;
        this.pointNo = pointNo;
        this.directId = directId;
        this.waveFilterAlg = waveFilterAlg;
        this.time = time;
        this.duration = duration;
        this.durationUnit = durationUnit;
        this.imgUrl = imgUrl;
        this.fieldNum = fieldNum;
        this.taskParam = taskParam;
        this.referNum = 0;
    }

    public String getDirectName() {
        if (ObjectUtils.isEmpty(directId)) {
            return null;
        }
        return Utils.getDirectNameById(directId);
    }

}