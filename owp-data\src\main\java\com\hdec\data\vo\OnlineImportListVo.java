package com.hdec.data.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 在线数据录入Vo类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class OnlineImportListVo {

    /** 时刻 */
    private String time;

    /** 测点ID数组 */
    private Integer[] pointIds;

    /** 测点编号数组 */
    private String[] pointNos;

    /** 频率（高频：high 分钟：min 小时：hour） */
    private String rate;
}
