package com.hdec.data.domain.taos;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TableColumn implements Serializable {
    private static final long serialVersionUID = 1L;

    private String field;

    private String type;

    private Integer length;

    private String note;

    private String encode;

    private String compress;

    private String level;

    public TableColumn(String field, String type) {
        this.field = field;
        this.type = type;
    }
}
