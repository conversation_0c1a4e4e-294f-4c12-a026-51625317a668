package com.hdec.data.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hdec.common.constant.Constant;
import com.hdec.common.domain.R;
import com.hdec.common.util.CommonUtil;
import com.hdec.common.util.FileUtil;
import com.hdec.data.domain.FreqPic;
import com.hdec.data.domain.FreqPicQo;
import com.hdec.data.domain.FreqTask;
import com.hdec.data.qo.FreqTaskQo;
import com.hdec.data.service.FreqTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 频谱分析任务控制器
 *
 * <AUTHOR>
 */
@Api(tags = "频谱分析任务")
@RestController
@RequestMapping("api/data/freqTask")
public class FreqTaskController {

    @Autowired
    private FreqTaskService freqTaskService;

    /** 文件本地路径 - Windows */
    @Value("${file.windowPath}")
    private String windowPath;

    /** 文件本地路径 - Linux */
    @Value("${file.linuxPath}")
    private String linuxPath;

    /**
     * 任务列表
     */
    @ApiOperation("任务列表")
    @PostMapping("list")
    public R list(@RequestHeader("fieldNum") String fieldNum, @RequestBody FreqTaskQo qo) {
        PageHelper.startPage(qo.getPageNum(), qo.getPageSize());
        List<FreqTask> tasks = freqTaskService.list(fieldNum, qo);
        return R.success(new PageInfo<>(tasks));
    }

    /**
     * 保存
     */
    @ApiOperation("保存")
    @PostMapping("save")
    public R save(@RequestHeader("fieldNum") String fieldNum, @RequestHeader("sessionId") String sessionId, @RequestBody FreqTask freqTask) {
        freqTask.setFieldNum(fieldNum);
        freqTaskService.save(freqTask, sessionId);
        return R.success("保存成功");
    }

    /**
     * 批量删除
     */
    @ApiOperation("批量删除")
    @DeleteMapping("delete")
    public R delete(Integer[] ids) {
        freqTaskService.delete(ids);
        return R.success("删除成功");
    }

    /**
     * 任务列表
     */
    @ApiOperation("任务列表")
    @PostMapping("pic/list")
    public R picList(@RequestHeader("fieldNum") String fieldNum, @RequestBody FreqTaskQo qo) {
        PageHelper.startPage(qo.getPageNum(), qo.getPageSize());
        List<FreqPic> tasks = freqTaskService.picList(fieldNum, qo);
        return R.success(new PageInfo<>(tasks));
    }

    /**
     * 保存
     */
    @ApiOperation("保存")
    @PostMapping("pic/save")
    public R picSave(@RequestHeader("fieldNum") String fieldNum, @RequestHeader("sessionId") String sessionId, @RequestBody FreqPicQo qo) {
        FreqPic freqTask = new FreqPic(qo.getTarget(), qo.getAttrName(), qo.getPicSize(), fieldNum);
        Integer id = freqTaskService.picSave(freqTask, sessionId);
        return R.success(id);
    }

    /**
     * 上传图片
     */
    @ApiOperation("上传图片")
    @PostMapping("pic/upload/{id}")
    public R picSave(MultipartFile file, @PathVariable Integer id) throws IOException {
        freqTaskService.picUpload(file, id);

        // todo
        try {
            TimeUnit.MILLISECONDS.sleep(100);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return R.success("上传成功");
    }

    /**
     * 批量删除
     */
    @ApiOperation("批量删除")
    @DeleteMapping("pic/delete")
    public R picDelete(Integer[] ids) {
        freqTaskService.picDelete(ids);
        return R.success("删除成功");
    }

}
