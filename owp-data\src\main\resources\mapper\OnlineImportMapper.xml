<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hdec.data.mapper.OnlineImportMapper">

    <insert id="save">
        INSERT INTO `${tabName}` (
            direction,
            `time`,
            attr_id,
            attr_val
        )
        VALUES (
            #{onlineImport.direction}, #{onlineImport.time}, #{onlineImport.attrId}, #{onlineImport.attrVal}
        )
    </insert>

    <delete id="del">
        DELETE
        FROM
            `${tabName}`
        WHERE
            `time` = #{onlineImport.time}
            AND direction = #{onlineImport.direction}
            AND attr_id = #{onlineImport.attrId}
    </delete>

    <select id="select" resultType="int">
        SELECT
            id
        FROM
            `${tabName}`
        WHERE
            `time` = #{onlineImport.time}
            AND direction = #{onlineImport.direction}
            AND attr_id = #{onlineImport.attrId}
    </select>

</mapper>