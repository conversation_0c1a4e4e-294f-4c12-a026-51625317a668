package com.hdec.data.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.models.auth.In;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class TimeIntervalVo {

    /** 测点*/
    private Integer measurePointId;

    /** 最大值*/
    private Float maxValue;

    /** 最大值时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date maxValueTime;

    /** 最小值*/
    private Float minValue;

    /** 最小值时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date minValueTime;

    /** 最大月变幅 */
    private Float maxMonthVariation;

    /** 对应月份 */
    private String maxMonth;

    /** 最小月变幅 */
    private Float minMonthVariation;

    /** 对应月份 */
    private String minMonth;

    /** 平均值 */
    private Float avgValue;

    /** 测次*/
    private Integer measureNumber;
}
