package com.hdec.data.netty;

import cn.hutool.core.util.ByteUtil;
import com.alibaba.fastjson.JSON;
import com.hdec.common.constant.RedisKey;
import com.hdec.common.util.TimeUtil;
import com.hdec.data.cache.Cache;
import com.hdec.data.domain.ChannelStatus;
import com.hdec.data.domain.Device;
import com.hdec.data.util.HexUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.PostConstruct;
import java.nio.ByteOrder;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * DM客户端消息处理类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class NettyHandlerDynamicV2Ctrl extends ChannelInboundHandlerAdapter {

    private static NettyHandlerDynamicV2Ctrl handler;

    @PostConstruct
    public void init() {
        handler = this;
    }

    @Value("${data.access.log.enable:false}")
    private boolean logEnable;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        log.info("有动态V2控制连接:{}",ctx.channel().remoteAddress());
        super.channelActive(ctx);

        ctx.writeAndFlush(Unpooled.copiedBuffer(syncTime()));
        log.info("同步时间指令已发送");

        Cache.channelMap.put(ctx.channel().remoteAddress().toString().replace("/", ""), ctx.channel());
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        log.error("有动态V2控制连接异常:{}", ctx.channel().remoteAddress(), cause);
        super.exceptionCaught(ctx, cause);
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) {
        ByteBuf byteBuf = (ByteBuf) msg;
        byte[] bytes = new byte[byteBuf.readableBytes()];
        byteBuf.readBytes(bytes);
        byteBuf.release();

//        log.info("收到动态V2控制报文：{}", HexUtil.bytesToHex(bytes, true));
        Device device = parseHex(HexUtil.bytesToHex(bytes, true), ctx.channel().remoteAddress().toString().replace("/", ""));
        if (logEnable && Math.random() > 0.9) {
            log.info(device.toString());
        }
        putDevice2Cache(device);
    }

    /**
     * 将设备放入缓存
     */
    private void putDevice2Cache(Device device) {
        if (device == null) {
            return;
        }

        Object deviceJson = handler.redisTemplate.opsForValue().get(RedisKey.DEVICE_LIST);
        if (ObjectUtils.isEmpty(deviceJson)) {
            handler.redisTemplate.opsForValue().set(RedisKey.DEVICE_LIST, JSON.toJSONString(Arrays.asList(device)), 3, TimeUnit.MINUTES);
        } else {
            List<Device> devices = JSON.parseArray((String) deviceJson, Device.class);
            updateDevice(devices, device);
            handler.redisTemplate.opsForValue().set(RedisKey.DEVICE_LIST, JSON.toJSONString(devices), 3, TimeUnit.MINUTES);
        }
    }

    /**
     * 更新设备信息
     */
    private void updateDevice(List<Device> devices, Device device) {
        /* 剔除超时设备 */
        long nowTs = System.currentTimeMillis();
        Iterator<Device> it = devices.iterator();
        while (it.hasNext()) {
            if (nowTs - it.next().getLastUpdateTime().getTime() > 5 * 60 * 1000) {
                it.remove();
            }
        }

        Device existDevice = devices.stream().filter(e -> device.getId().equals(e.getId())).findFirst().orElse(null);
        if (existDevice == null) {
            // 插入
            devices.add(device);
        } else {
            // 更新
            existDevice.setCollectStatus(device.getCollectStatus());
            existDevice.setSlaveStatus(device.getSlaveStatus());
            existDevice.setTime(device.getTime());
            existDevice.setAddr(device.getAddr());
            existDevice.setLastUpdateTime(new Date());
            existDevice.setChannelStatuses(device.getChannelStatuses());
            existDevice.setSdStatus(device.getSdStatus());
            existDevice.setSdCapacity(device.getSdCapacity());
            existDevice.setRestartCycle(device.getRestartCycle());
        }
    }

    /**
     * 解析报文
     */
    private static Device parseHex(String hex, String clientAddr) {
        if (ObjectUtils.isEmpty(hex) || !hex.startsWith("EB91EB910A0A")) {
            return null;
        }
        List<ChannelStatus> channelStatuses = new ArrayList<>();
        byte[] bytes = HexUtil.hexToBytes(hex, true);
        String deviceId = String.valueOf(ByteUtil.bytesToShort(getBytesByIndex(bytes, 6, 8), ByteOrder.BIG_ENDIAN));
        String time = TimeUtil.format2Second(new Date(ByteUtil.bytesToLong(getBytesByIndex(bytes, 8, 16), ByteOrder.BIG_ENDIAN)));
        String collectStatus = String.valueOf(getBytesByIndex(bytes, 16, 17)[0]);
        String slaveStatus = String.valueOf(getBytesByIndex(bytes, 17, 18)[0]);
        int slaveNum = getBytesByIndex(bytes, 18, 19)[0];
        for (int i = 0; i < slaveNum; i++) {
            int start = 19 + i * 2;
            short i1 = ByteUtil.bytesToShort(getBytesByIndex(bytes, start, start + 2), ByteOrder.BIG_ENDIAN);
            char[] chars = toBinaryString16(i1).substring(4, 16).toCharArray();
            reverseArray(chars);

            Map<Integer, Boolean> status = new HashMap<>();
            for (int j = 0; j < chars.length; j++) {
                status.put(j + 1, chars[j] == '1');
            }
            channelStatuses.add(new ChannelStatus(i + 1, status));
        }
//        if (Math.random() > 0.9) {
//            log.info("收到动态V2控制报文：{}", HexUtil.bytesToHex(bytes, true));
//            log.info("slaveNum:{}", slaveNum);
//            log.info("x1:{}", HexUtil.bytesToHex(getBytesByIndex(bytes, 21 + 2 * slaveNum, 21 + 2 * slaveNum + 1), true));
//            log.info("x2:{}", HexUtil.bytesToHex(getBytesByIndex(bytes, 22 + 2 * slaveNum, 22 + 2 * slaveNum + 1), true));
//            log.info("x3:{}",HexUtil.bytesToHex( getBytesByIndex(bytes, 23 + 2 * slaveNum, 23 + 2 * slaveNum + 2), true));
//        }

        slaveNum -= 1;

        // SD卡状态
        String sdStatus = String.valueOf(getBytesByIndex(bytes, 21 + 2 * slaveNum, 21 + 2 * slaveNum + 1)[0]);
        // SD卡剩余容量
        String sdCapacity = String.valueOf(getBytesByIndex(bytes, 22 + 2 * slaveNum, 22 + 2 * slaveNum + 1)[0]);
        // 定时重启周期
        String restartCycle = String.valueOf(ByteUtil.bytesToShort(getBytesByIndex(bytes, 23 + 2 * slaveNum, 23 + 2 * slaveNum + 2), ByteOrder.BIG_ENDIAN));

        return new Device(deviceId, collectStatus, slaveStatus, time, clientAddr, new Date(), channelStatuses, sdStatus, sdCapacity, restartCycle);
    }

    public static void reverseArray(char[] array) {
        int start = 0;
        int end = array.length - 1;
        while (start < end) {
            // 交换元素
            char temp = array[start];
            array[start] = array[end];
            array[end] = temp;
            start++;
            end--;
        }
    }

    public static String toBinaryString16(short value) {
        int unsignedValue = value & 0xFFFF;
        String binaryString = Integer.toBinaryString(unsignedValue);
        return String.format("%16s", binaryString).replace(' ', '0');
    }

    /**
     * 开始采集
     */
    private byte[] startCollect() {
        return HexUtil.hexToBytes("eb 91 eb 91 0002 FF FF FF FF FF FF FF FF 0D 0A 0D 0A".replace(" ", "").toUpperCase(), true);
    }

    /**
     * 同步时间
     */
    private byte[] syncTime() {
        // 消息头
        StringBuilder sb = new StringBuilder("EB91EB91");
        // 命令码
        sb.append("0003");
        sb.append(HexUtil.bytesToHexNoZero(ByteUtil.longToBytes(new Date().getTime(), ByteOrder.BIG_ENDIAN), true));
        sb.append("0D0A0D0A");
        return HexUtil.hexToBytes(sb.toString(), true);
    }

    /**
     * 按下标获取子字节数组
     */
    public static byte[] getBytesByIndex(byte[] bytes, int start, int end) {
        int len = end - start;
        byte[] target = new byte[len];
        System.arraycopy(bytes, start, target, 0, len);
        return target;
    }

}
