package com.hdec.data.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/7
 */
@Data
@ToString
@NoArgsConstructor
public class ChartNumVo {

    /* 海上设施名称 */
    private String seaFacilityName;

    /* 测点编号 */
    private String pointNo;

    /* 方向 */
    private String direct;

    /* 展示内容 */
    private List<LineLabel> labels;

    /* Json参数 */
    private String jsonParams;

    public ChartNumVo(List<LineLabel> labels, String jsonParams) {
        this.labels = labels;
        this.jsonParams = jsonParams;
    }

    public ChartNumVo(String seaFacilityName, String pointNo, String direct, String jsonParams) {
        this.seaFacilityName = seaFacilityName;
        this.pointNo = pointNo;
        this.direct = direct;
        this.jsonParams = jsonParams;
    }
}
