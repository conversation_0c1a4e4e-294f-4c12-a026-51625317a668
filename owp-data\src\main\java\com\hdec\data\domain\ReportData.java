package com.hdec.data.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class ReportData {

    private String no;

    private Double baseMaxX;

    private Double baseMinX;

    private Double baseAvgX;

    private Double baseMaxY;

    private Double baseMinY;

    private Double baseAvgY;

    private Double topMaxX;

    private Double topMinX;

    private Double topAvgX;

    private Double topMaxY;

    private Double topMinY;

    private Double topAvgY;

    public ReportData(String no, Double baseMaxX, Double baseMinX, Double baseAvgX, Double baseMaxY, Double baseMinY, Double baseAvgY, Double topMaxX, Double topMinX, Double topAvgX, Double topMaxY, Double topMinY, Double topAvgY) {
        this.no = no;
        this.baseMaxX = baseMaxX;
        this.baseMinX = baseMinX;
        this.baseAvgX = baseAvgX;
        this.baseMaxY = baseMaxY;
        this.baseMinY = baseMinY;
        this.baseAvgY = baseAvgY;
        this.topMaxX = topMaxX;
        this.topMinX = topMinX;
        this.topAvgX = topAvgX;
        this.topMaxY = topMaxY;
        this.topMinY = topMinY;
        this.topAvgY = topAvgY;
    }
}
