package com.hdec.data.qo;

import com.hdec.common.util.TimeUtil;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.util.ObjectUtils;

/**
 * 频谱分析结果查询类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class FreqResultQo {

    /** 当前页 */
    private Integer pageNum;

    /** 页大小 */
    private Integer pageSize;

    /** 测点ID */
    private Integer pointId;

    /** 方向ID */
    private Integer directId;

    /** 开始时间 */
    private String startTime;

    /** 结束时间 */
    private String endTime;

    public String getStartTime() {
        if (ObjectUtils.isEmpty(startTime)) {
            return startTime;
        }
        return TimeUtil.completeStart(startTime);
    }

    public String getEndTime() {
        if (ObjectUtils.isEmpty(endTime)) {
            return endTime;
        }
        return TimeUtil.completeEnd(endTime);
    }
}
