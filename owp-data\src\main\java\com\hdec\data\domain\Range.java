package com.hdec.data.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;
@Data
@ToString
@NoArgsConstructor
public class Range {
    /** 开始时间 */
    private Date start;
    /** 结束时间 */
    private Date end;
    /** 月份 */
    private String Month;

    private Range(Date start) {
        this.start = start;
    }

    public static Range create(Date start) {
        return new Range(start);
    }

    public Range end(Date end) {
        this.end = end;
        return this;
    }

    public void setMonth(String month) {
        Month = month;
    }
}
