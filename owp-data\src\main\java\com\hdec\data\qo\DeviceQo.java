package com.hdec.data.qo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 设备控制查询类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class DeviceQo {

    /** 当前页 */
    private Integer pageNum;

    /** 每页条数 */
    private Integer pageSize;

    /**
     * 1、重新启动
     * 2、同步系统时间
     * 3、开始采集
     * 4、停止采集
     * 5、设置设备ID
     * 6、设置设备IP
     * 7、设置设备指向服务器IP
     */
    private Integer type;

    /** 值 */
    private String value;

    private String clientIp;

}
