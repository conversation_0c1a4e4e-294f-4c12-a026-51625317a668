package com.hdec.data.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hdec.common.util.MonitorUtil;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * 测值统计实体类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class Stat {

    /** 自增主键 */
    private Integer id;

    /** 时间（天） */
    private String day;

    /** 月 */
    private String month;

    /** 季 */
    private String quarter;

    /** 年 */
    private String year;

    private String item;

    /** 测点ID */
    private Integer pointId;

    /** 测点编号 */
    private String pointNo;

    /** 方向 */
    private Integer direct;
    private String directName;

    /** 分量ID */
    private Integer attrId;

    /** 分量名 */
    private String attrName;

    /** 最大值*/
    private Float maxValue;

    /** 最大值时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date maxValueTime;

    /** 最小值*/
    private Float minValue;

    /** 最小值时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date minValueTime;

    /** 平均值 */
    private Float avgValue;

    /** 测次 */
    private Integer measureNum;

    /** 未审批数量 */
    private Integer approvalCountInit;

    /** 审批通过数量 */
    private Integer approvalCountPass;

    /** 审批不通过数量 */
    private Integer approvalCountFail;

    /** 测次 */
    private Float changeRange;

    /** 频率 */
    private String rate;

    public Float getChangeRange() {
        if (maxValue != null && minValue != null) {
            return maxValue - minValue;
        }
        return null;
    }

    public String getDirectName() {
        if (direct != null) {
            return MonitorUtil.transDirection(direct);
        }
        return null;
    }

    public Stat(String day, Integer pointId, Integer direct, Integer attrId) {
        this.day = day;
        this.pointId = pointId;
        this.direct = direct;
        this.attrId = attrId;
    }

    public Stat(String day, Integer pointId, Integer direct, Integer attrId, String rate) {
        this.day = day;
        this.pointId = pointId;
        this.direct = direct;
        this.attrId = attrId;
        this.rate = rate;
    }

}
