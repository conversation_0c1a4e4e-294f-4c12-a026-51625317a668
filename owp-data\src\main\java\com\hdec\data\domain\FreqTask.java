package com.hdec.data.domain;

import com.google.common.base.Joiner;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.ObjectUtils;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FreqTask {

    /**
     * 自增主键
     */
    private Integer id;

    private String target;

    /**
     * 测点ID数组
     */
    private Integer[] pointIds;

    private String[] pointNos;

    private String pointIdStr;

    private String pointNoStr;

    private String attrName;

    /**
     * 滤波器
     */
    private String waveFilter;

    /**
     * 滤波类型
     */
    private String filterType;
    /**
     * 滤波参数
     */
    private String filterParam;

    /** 低截止频率 */
    private Double rateLow;

    /** 高截止频率 */
    private Double rateHigh;

    /** 频率 */
    private Integer fs;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 时间跨度
     */
    private Integer timeSpan;

    /**
     * 时间跨度单位(小时/天)
     */
    private String timeSpanUnit;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 提交时间
     */
    private String createTime;

    private Integer process;

    private String param;

    private String fieldNum;

    public String getPointIdStr() {
        if (!ObjectUtils.isEmpty(pointIdStr)) {
            return pointIdStr;
        }
        if (ObjectUtils.isEmpty(pointIds)) {
            return null;
        }
        return Joiner.on(",").join(pointIds);
    }

    public String getPointNoStr() {
        if (!ObjectUtils.isEmpty(pointNoStr)) {
            return pointNoStr;
        }
        if (ObjectUtils.isEmpty(pointNos)) {
            return null;
        }
        return Joiner.on(",").join(pointNos);
    }

}