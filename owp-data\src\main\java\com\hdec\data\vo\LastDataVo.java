package com.hdec.data.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * 测点最新数据Vo类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class LastDataVo {

    /** 测点ID */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date ts;

    /** 属性名 */
    private String attrName;

    /** 方向名 */
    private String directName;

    /** 测点ID */
    private Float val;

    public LastDataVo(Date ts, String attrName, String directName) {
        this.ts = ts;
        this.attrName = attrName;
        this.directName = directName;
    }
}
