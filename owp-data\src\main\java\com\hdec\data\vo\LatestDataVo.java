package com.hdec.data.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * 自动化设备最新数据返回类
 */
@Data
@ToString
@NoArgsConstructor
public class LatestDataVo {

    /** 测点方向 */
    private String id;

    /** 最新数据 */
    private List<Val> vs;

    /** 频率数据 */
    private FrequencyVo frequencyVo;

    /** 晃点数据 */
    private List<SloshVo> sloshList;

    public LatestDataVo(String id) {
        this.id = id;
    }

}
