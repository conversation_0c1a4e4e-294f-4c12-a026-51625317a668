package com.hdec.data.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/29
 */
@Data
@ToString
@NoArgsConstructor
public class ImportVo {

    /** 本次导入记录的ID */
    private Integer id;

    /** 本地保存的excel文件名 */
    private String excelName;

    /** 原始文件名 */
    private String originName;

    public ImportVo(Integer id, String excelName) {
        this.id = id;
        this.excelName = excelName;
    }

    public ImportVo(Integer id, String excelName, String originName) {
        this.id = id;
        this.excelName = excelName;
        this.originName = originName;
    }
}
