<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hdec.data.mapper.ReportCoverMapper">

    <select id="list" resultType="com.hdec.data.domain.template.ReportCover">
        SELECT
            *
        FROM
            owp_report_cover
        WHERE
            field_num = #{fieldNum}
            <if test="name != null and name != ''">
                AND `name` LIKE CONCAT('%', #{name} ,'%')
            </if>
            <if test="type != null">
                AND `type` = #{type}
            </if>
            <if test="startTime != null and startTime != '' and endTime != null and endTime != ''">
                AND `update_time` between #{startTime} and #{endTime}
            </if>
        ORDER BY id DESC
    </select>

    <select id="selectByName" resultType="com.hdec.data.domain.template.ReportCover">
        SELECT
            *
        FROM
            owp_report_cover
        WHERE
            field_num = #{fieldNum} AND `name` = #{name}
            <if test="id != null">
                AND id != #{id}
            </if>
            LIMIT 1
    </select>

    <select id="getById" resultType="com.hdec.data.domain.template.ReportCover">
        SELECT
            *
        FROM
            owp_report_cover
        WHERE
            id = #{id}
    </select>

    <delete id="delete">
        DELETE
        FROM
            owp_report_cover
        WHERE
            id IN
            <foreach collection="ids" item="id" separator=", " open="(" close=")">
                #{id}
            </foreach>
    </delete>

    <update id="update" parameterType="com.hdec.data.domain.template.ReportCover">
        UPDATE owp_report_cover
        <set>
            `update_time` = NOW(),
            <if test="cover.name != null">`name` = #{cover.name},</if>
            <if test="cover.type != null">`type` = #{cover.type},</if>
            <if test="cover.userId != null">`user_id` = #{cover.userId},</if>
            <if test="cover.username != null">`username` = #{cover.username},</if>
            <if test="cover.remark != null">`remark` = #{cover.remark},</if>
            <if test="cover.content != null">`content` = #{cover.content},</if>
        </set>
        WHERE
        id = #{cover.id}
    </update>

    <insert id="add" parameterType="com.hdec.data.domain.template.ReportCover" useGeneratedKeys="true" keyProperty="id" >
        INSERT INTO owp_report_cover (
            `name`,
            `type`,
            create_time,
            update_time,
            user_id,
            username,
            remark,
            content,
            field_num
        )
        VALUES (
            #{cover.name},
            #{cover.type},
            NOW(),
            NOW(),
            #{cover.userId},
            #{cover.username},
            #{cover.remark},
            #{cover.content},
            #{cover.fieldNum}
        )
    </insert>

</mapper>