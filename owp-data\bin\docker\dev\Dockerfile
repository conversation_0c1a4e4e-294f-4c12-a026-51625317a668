# 构建镜像，执行命令：【docker build -t owp-data .】
FROM openjdk:8u342-slim
MAINTAINER Liyohe

# 时区问题
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && echo 'Asia/Shanghai' >/etc/timezone

# 安装 taosc 根据自己的CPU架构选择软件包
ADD TDengine-client-3.0.4.0-Linux-arm64.tar.gz /root
WORKDIR /root/TDengine-client-3.0.4.0
RUN ./install_client.sh && rm -rf /root/TDengine-*

# 安装 nodejs 18 环境
RUN apt-get update && apt-get install -y curl && curl -sL https://deb.nodesource.com/setup_18.x | bash -
RUN apt-get install nodejs -y && apt-get clean && rm -rf /var/lib/apt/lists/*

# 安装 canvas 依赖
RUN apt-get update && apt-get install -y build-essential libcairo2-dev libpango1.0-dev libjpeg-dev libgif-dev librsvg2-dev && apt-get clean && rm -rf /var/lib/apt/lists/*

# 创建 echarts-export 目录，拷贝文件
RUN mkdir -p /home/<USER>/ && mkdir -p /data/highcharts-export/
ADD ec-expoort.tar /home/<USER>
WORKDIR /home/<USER>/ec-expoort
RUN npm install -g cnpm && cnpm install

# 添加 word 模版
ADD template.docx /home

# 添加 Times New Roman 字体
ADD font/* /usr/share/fonts/truetype/echarts/
RUN fc-cache -fv
