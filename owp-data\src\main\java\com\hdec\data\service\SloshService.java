package com.hdec.data.service;

import com.alibaba.nacos.shaded.com.google.common.base.Joiner;
import com.hdec.common.domain.GraphAnalysis;
import com.hdec.common.domain.PointCommon;
import com.hdec.common.domain.R;
import com.hdec.common.util.CommonUtil;
import com.hdec.common.util.TimeUtil;
import com.hdec.data.domain.Datum;
import com.hdec.data.feign.MonitorService;
import com.hdec.data.feign.WindService;
import com.hdec.data.qo.SloshQo;
import com.hdec.data.vo.SloshVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class SloshService {

    @Autowired
    private MonitorService monitorService;

    @Autowired
    private WindService windService;

    @Autowired
    private TdService tdService;

    /**
     * 加载晃点图
     */
    public R loadSlosh(String fieldNum, SloshQo qo) throws ParseException {
        Integer targetPointId = qo.getPointID();
        Map<Integer, GraphAnalysis> mapConf = monitorService.proGraphAnalysisMap(fieldNum);
        GraphAnalysis graphAnalysis = mapConf.get(2);
        if (graphAnalysis == null) {
            mapConf = monitorService.graphAnalysisMap();
            graphAnalysis = mapConf.get(2);
            if (graphAnalysis == null) {
                return R.error("请先配置参数");
            }
        }

        /* 获取该测点绑定风机的设计泥面高程 */
        Float designElevation = windService.getDesignElevation(qo.getPointID());
        if (designElevation == null) {
            return R.error("请先维护该测点绑定海上设施的设计泥面高程");
        }

        /* 获取该测点所属海上设施下的所有测点并检查安装高程 */
        List<PointCommon> points = monitorService.getSameGroupPoints(qo.getPointID());
        for (PointCommon point : points) {
            if (ObjectUtils.isEmpty(point.getInstallElevation())) {
                return R.error("请先维护测点" + point.getNo() + "的安装高程");
            }
            Float numInstallElevation = CommonUtil.extractDecimal(point.getInstallElevation());
            if (numInstallElevation == null) {
                return R.error("测点" + point.getNo() + "的安装高程中未识别到唯一数字");
            } else {
                point.setInstallElevationNum(numInstallElevation);
            }
        }

        // 按安装高程升序
        Collections.sort(points, (p1, p2) -> {
            if (p1.getInstallElevationNum() > p2.getInstallElevationNum()) {
                return 1;
            }
            if (p1.getInstallElevationNum() < p2.getInstallElevationNum()) {
                return -1;
            }
            return 0;
        });

        /* 逐一计算偏移 */
        List<String> allLostTimes = new ArrayList<>();
        List<SloshVo> pointSloshVo = null;
        List<SloshVo> prePointSloshVo = null;
        String prePointNo = null;
        for (PointCommon point : points) {
            float height = point.getInstallElevationNum() - designElevation;
            qo.setPointID(point.getId());
            pointSloshVo = calcPointSloshVo(qo, height, graphAnalysis.getAttrName());

            // 加上前一个晃点偏移量
            List<String> lostTimes = addPreOffset(prePointNo, pointSloshVo, prePointSloshVo);
            allLostTimes.addAll(lostTimes);

            designElevation = point.getInstallElevationNum();
            prePointSloshVo = pointSloshVo;
            prePointNo = point.getNo();
            if (targetPointId.equals(point.getId())) {
                break;
            }
        }

        correctAngle(targetPointId, pointSloshVo);

        String msg = jsonMsg(allLostTimes);
        return R.success(msg, pointSloshVo);
    }

    /**
     * 拼接字符串
     */
    private String jsonMsg(List<String> allLostTimes) {
        if (ObjectUtils.isEmpty(allLostTimes)) {
            return null;
        }

        if (allLostTimes.size() > 10) {
            allLostTimes = allLostTimes.subList(0, 10);
            return Joiner.on("\n").join(allLostTimes) + "\n......";
        }
        return Joiner.on("\n").join(allLostTimes);
    }

    /**
     * 修正角度
     */
    private void correctAngle(Integer pointId, List<SloshVo> vos) {
        float angel = 0;
        Float aAngel = monitorService.getAngel(pointId);
        if (aAngel != null) {
            angel = aAngel;
        }

        // 坐标系转换
        double radian = angel * Math.PI / 180;
        for (SloshVo vo : vos) {
            Double x = vo.getMainDis();
            Double y = vo.getVerMainDis();
            vo.setMainDis(x * Math.cos(radian) - y * Math.sin(radian));
            vo.setVerMainDis(x * Math.sin(radian) + y * Math.cos(radian));
        }
    }

    /**
     * 加上前面的偏移量
     */
    private List<String> addPreOffset(String pointNo, List<SloshVo> pointSloshVo, List<SloshVo> prePointSloshVo) {
        List<String> lostTimes = new ArrayList<>();
        if (prePointSloshVo == null) {
            return lostTimes;
        }

        Map<Long, List<SloshVo>> preMap = prePointSloshVo.stream().collect(Collectors.groupingBy(e -> e.getTime().getTime()));
        Iterator<SloshVo> it = pointSloshVo.iterator();
        while (it.hasNext()) {
            SloshVo sloshVo = it.next();
            List<SloshVo> preVo = preMap.get(sloshVo.getTime().getTime());
            if (!ObjectUtils.isEmpty(preVo)) {
                Double preMainDis = preVo.get(0).getMainDis();
                Double preVerMainDis = preVo.get(0).getVerMainDis();
                if (preMainDis != null && preMainDis != null) {
                    sloshVo.setMainDis(sloshVo.getMainDis() + preMainDis);
                    sloshVo.setVerMainDis(sloshVo.getVerMainDis() + preVerMainDis);
                    continue;
                }
            }
            lostTimes.add(pointNo + "在" + TimeUtil.format2Second(sloshVo.getTime()) + "数据缺失，已忽略该时刻数据");
            it.remove();
        }
        return lostTimes;
    }

    /**
     * 计算偏移量
     */
    private List<SloshVo> calcPointSloshVo(SloshQo qo, float height, String attrName) throws ParseException {
        /* 计算结果 */
        List<SloshVo> vos = new ArrayList<>();
        List<Datum> data = tdService.querySlosh(qo, attrName);

        if (ObjectUtils.isEmpty(data)) {
            return Collections.emptyList();
        }
        Map<Date, List<Datum>> groupDatum = data.stream().collect(Collectors.groupingBy(Datum::getTime));
        groupDatum.forEach((time, v) -> {
            if (v.size() == 2) {
                Datum xDatum = v.get(0);
                Datum yDatum = v.get(1);
                if (xDatum.getAttrVal() == null || yDatum.getAttrVal() == null) {
                    return;
                }
                if (xDatum.getDirection() == 1 && yDatum.getDirection() == 2) {
                    SloshVo vo = new SloshVo(time, Math.tan(xDatum.getAttrVal() * Math.PI / 180) * height, Math.tan(yDatum.getAttrVal() * Math.PI / 180) * height);
                    vos.add(vo);
                    if (qo.getIsMainOpp()) {
                        vo.setMainDis(-vo.getMainDis());
                    }
                    if (qo.getIsVerticalOpp()) {
                        vo.setVerMainDis(-vo.getVerMainDis());
                    }
                } else if (xDatum.getDirection() == 2 && yDatum.getDirection() == 1) {
                    SloshVo vo = new SloshVo(time, Math.tan(yDatum.getAttrVal() * Math.PI / 180) * height, Math.tan(xDatum.getAttrVal() * Math.PI / 180) * height);
                    vos.add(vo);
                    if (qo.getIsMainOpp()) {
                        vo.setMainDis(-vo.getMainDis());
                    }
                    if (qo.getIsVerticalOpp()) {
                        vo.setVerMainDis(-vo.getVerMainDis());
                    }
                }
            }
        });
        return vos;
    }

}
