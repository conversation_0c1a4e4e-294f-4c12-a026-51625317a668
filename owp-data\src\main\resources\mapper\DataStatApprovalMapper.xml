<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hdec.data.mapper.DataStatApprovalMapper">

    <sql id="common_sql" >
        `day`, `point_id`, `direct`, `attr_id`, `approval_fail_num`, `field_num`
    </sql>

    <delete id="deleteByPointDirect">
        DELETE
        FROM
            owp_stat_approval
        WHERE
            field_num = #{fieldNum}
        AND `day` = #{day}
        AND point_id = #{pointId}
    </delete>

    <insert id="saveBatch">
        INSERT INTO
            owp_stat_approval (<include refid="common_sql"/>)
        VALUES
        <foreach collection="statList" separator="," item="s">
            (#{s.day}, #{s.pointId}, #{s.direct}, #{s.attrId}, #{s.approvalFailNum}, #{fieldNum})
        </foreach>
    </insert>

    <select id="select" resultType="com.hdec.data.domain.Stat">
        SELECT
            *
        FROM
            owp_stat_approval
        WHERE
            field_num = #{qo.fieldNum}
            <if test="qo.attrId != null">
                AND attr_id = #{qo.attrId}
            </if>
            <if test="qo.startTime != null and qo.startTime != '' and qo.endTime != null and qo.endTime != ''">
                AND `day` BETWEEN #{qo.startTime} AND #{qo.endTime}
            </if>
            <if test="qo.pointIds != null and qo.pointIds.size() > 0">
                AND point_id IN
                <foreach item="pointId" index="index" collection="qo.pointIds" open="(" separator="," close=")">
                    #{pointId}
                </foreach>
            </if>
    </select>

</mapper>