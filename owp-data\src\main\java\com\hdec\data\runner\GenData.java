package com.hdec.data.runner;

import com.alibaba.fastjson.JSON;
import com.hdec.common.domain.msg.Inst;
import com.hdec.data.service.KafkaService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 制造测试数据
 */
@Slf4j
@Service
public class GenData {

    private static Random random = new Random();

    /** 发送批次大小 */
    private List<Inst> batch = new ArrayList<>();

    @Autowired
    private KafkaService kafkaService;

    /**
     * 制造Hz数据
     */
    public void genHzData(String[] deviceNos, int hz) {
        float start = random.nextFloat();
        int channels = 12;
        int millis = 1000 / hz;
        while (true) {
            Date date = new Date();
            for (String deviceNo : deviceNos) {
                /* 制造一个数据 */
                Inst inst = new Inst(deviceNo + "_0", date);
                Map<Integer, Float[]> channelData = new HashMap<>(channels);
                for (int i = 0; i < channels; i++) {
                    channelData.put(i, new Float[]{start + random.nextFloat() / 1000});
                }
                inst.setChannelData(channelData);

                batch.add(inst);
                if (batch.size() >= 1) {
                    kafkaService.consume(batch, "dynamic");
                    batch.clear();
                }
            }
            try {
                TimeUnit.MILLISECONDS.sleep(millis);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }
}
