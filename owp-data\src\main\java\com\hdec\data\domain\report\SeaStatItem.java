package com.hdec.data.domain.report;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.util.ObjectUtils;

/**
 * 统计项
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class SeaStatItem {

    /** 测点ID */
    private Integer pointId;

    /** 测点编号 */
    private String pointNo;

    private String install;

    /** 方向(X/Y) */
    private Double minX;
    private Double maxX;
    private Double avgX;

    private Double minY;
    private Double maxY;
    private Double avgY;

    private Double minZ;
    private Double maxZ;
    private Double avgZ;


    public String getInstall() {
        if (ObjectUtils.isEmpty(install) || ObjectUtils.isEmpty(install.trim())) {
            return null;
        }
        return install;
    }

    public SeaStatItem(Integer pointId, String pointNo, String install, Double minX, Double maxX, Double avgX, Double minY, Double maxY, Double avgY) {
        this.pointId = pointId;
        this.pointNo = pointNo;
        this.install = install;
        this.minX = minX;
        this.maxX = maxX;
        this.avgX = avgX;
        this.minY = minY;
        this.maxY = maxY;
        this.avgY = avgY;
    }

    public SeaStatItem(Integer pointId, String pointNo, String install, Double minX, Double maxX, Double avgX, Double minY, Double maxY, Double avgY, Double minZ, Double maxZ, Double avgZ) {
        this.pointId = pointId;
        this.pointNo = pointNo;
        this.install = install;
        this.minX = minX;
        this.maxX = maxX;
        this.avgX = avgX;
        this.minY = minY;
        this.maxY = maxY;
        this.avgY = avgY;
        this.minZ = minZ;
        this.maxZ = maxZ;
        this.avgZ = avgZ;
    }
}
