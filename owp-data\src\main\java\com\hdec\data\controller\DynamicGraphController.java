package com.hdec.data.controller;

import com.hdec.common.domain.R;
import com.hdec.common.util.TimeUtil;
import com.hdec.data.qo.DynamicGraphSloshConf;
import com.hdec.data.service.DynamicGraphService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 动态图形
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@Api(tags = "动态图形")
@Validated
@Slf4j
@RestController
@RequestMapping("api/data/dynamicGraph")
public class DynamicGraphController {

    @Resource
    private DynamicGraphService dynamicGraphService;

    @ApiOperation("晃点图配置")
    @GetMapping("slosh/conf")
    public R getSloshConf(@RequestHeader("fieldNum") String fieldNum) {
        DynamicGraphSloshConf sloshConf = dynamicGraphService.getSloshConf(fieldNum);
        return R.success(sloshConf);
    }

    @ApiOperation("晃点图配置")
    @PostMapping("slosh/conf")
    public R setSlosh(@RequestHeader("fieldNum") String fieldNum,
                          @RequestBody DynamicGraphSloshConf conf) {
        dynamicGraphService.settingSlosh(fieldNum,conf);
        return R.success();
    }
}
