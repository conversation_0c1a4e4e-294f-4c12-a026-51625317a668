package com.hdec.data.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 报告模板
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class ReportTemplate {

    /** 自增主键 */
    private Integer id;

    /** 标题 */
    private String title;

    /** 类别 */
    private String classify;

    /** 模板类型 */
    private String templateType;

    /** 属性 */
    private String attr;

    /** 形式 */
    private String form;

    /** 报告链接 */
    private String url;
}
