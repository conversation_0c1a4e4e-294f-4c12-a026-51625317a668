package com.hdec.data.controller;

import cn.hutool.core.util.ByteUtil;
import com.github.pagehelper.PageInfo;
import com.hdec.common.domain.R;
import com.hdec.common.util.TimeUtil;
import com.hdec.data.cache.Cache;
import com.hdec.data.domain.ChannelStatus;
import com.hdec.data.domain.Device;
import com.hdec.data.qo.DeviceQo;
import com.hdec.data.service.DeviceService;
import com.hdec.data.util.HexUtil;
import io.netty.buffer.Unpooled;
import io.netty.channel.Channel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.nio.ByteOrder;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 设备控制控制器
 *
 * <AUTHOR>
 */
@Api(tags = "设备控制")
@Slf4j
@Validated
@RestController
@RequestMapping("api/data/device")
public class DeviceController {

    @Autowired
    private DeviceService deviceService;

    /**
     * 设备列表
     */
    @ApiOperation("设备列表")
    @PostMapping("list")
    public R list(@RequestHeader("sessionId") String sessionId, @RequestBody DeviceQo qo) {
        List<Device> devices = deviceService.list();

        /* 测试使用 */
//        List<ChannelStatus> channelStatuses = new ArrayList<>();
//        Map<Integer, Boolean> statusMap1 = new HashMap<>();
//        for (int i = 1; i <= 12; i++) {
//            statusMap1.put(i, true);
//        }
//        channelStatuses.add(new ChannelStatus(1, statusMap1));
//
//        Map<Integer, Boolean> statusMap2 = new HashMap<>();
//        for (int i = 1; i <= 12; i++) {
//            statusMap2.put(i, false);
//        }
//        channelStatuses.add(new ChannelStatus(2, statusMap2));
//
//        devices.add(new Device("101", "1", "1", TimeUtil.format2Second(new Date()), "192.168.3.173", new Date(), channelStatuses));

        for (Device device : devices) {
            device.setCollectStatus("1".equals(device.getCollectStatus()) ? "采集中" : "停止采集");
            device.setSlaveStatus("1".equals(device.getSlaveStatus()) ? "正常" : "异常");
            device.setSdStatus("0".equals(device.getSdStatus()) ? "无SD卡" : "有SD卡");
            device.setSdCapacity(device.getSdCapacity() + "%");
            device.setRestartCycle("0".equals(device.getRestartCycle()) ? "未配置重启周期" : device.getRestartCycle() + "小时");
        }

        // 分页
        PageInfo<Device> page = new PageInfo<>();
        page.setTotal(devices.size());
        List<Device> list = devices.stream().skip((qo.getPageNum() - 1) * qo.getPageSize()).limit(qo.getPageSize()).collect(Collectors.toList());

        // 按照 id 的数值大小进行升序排序
        try {
            list.sort(Comparator.comparingInt(d -> Integer.parseInt(d.getId())));
        } catch (Exception e) {
            e.printStackTrace();
        }

        page.setList(list);
        return R.success(page);
    }

    @ApiOperation("设备列表")
    @GetMapping("info")
    public R getInfo(@RequestParam("id") String id) {
        Device device = deviceService.list().stream().filter(d -> Objects.equals(d.getId(), id)).findFirst().orElse(null);
        if (device != null) {
            device.setCollectStatus("1".equals(device.getCollectStatus()) ? "采集中" : "停止采集");
            device.setSlaveStatus("1".equals(device.getSlaveStatus()) ? "正常" : "异常");
            device.setSdStatus("0".equals(device.getSdStatus()) ? "无SD卡" : "有SD卡");
            device.setSdCapacity(device.getSdCapacity() + "%");
            device.setRestartCycle("0".equals(device.getRestartCycle()) ? "未配置重启周期" : device.getRestartCycle() + "小时");
        }
        return R.success(device);
    }

    /**
     * 设备控制
     */
    @ApiOperation("设备控制")
    @PostMapping("deviceSetting")
    public R deviceSetting(@RequestHeader("sessionId") String sessionId, @RequestBody DeviceQo qo) throws InterruptedException {
        if (ObjectUtils.isEmpty(qo.getClientIp())) {
            return R.error("客户端地址不允许为空");
        }

        Channel channel = Cache.channelMap.get(qo.getClientIp());
        if (channel == null) {
            return R.error("设备不在线");
        }

        String msg = "未知类型";
        byte[] bytes = new byte[0];
        if (qo.getType() == 1) {
            // 重新启动
            bytes = restart();
            msg = "设备已重启";
        } else if (qo.getType() == 2) {
            // 同步系统时间
            bytes = syncTime();
            msg = "设备时间已同步，时间为" + TimeUtil.format2Second(new Date());
        } else if (qo.getType() == 3) {
            // 开始采集
            bytes = startCollect();
            msg = "设备已开始采集";
        } else if (qo.getType() == 4) {
            // 停止采集
            bytes = stopCollect();
            msg = "设备已停止采集";
        } else if (qo.getType() == 5) {
            // 设置设备ID
            bytes = setDeviceId(Short.parseShort(qo.getValue()));
            msg = "已将设备ID设置为：" + qo.getValue();
        } else if (qo.getType() == 6) {
            // 设置设备IP
            bytes = setDeviceIp(Short.parseShort(qo.getValue()));
        } else if (qo.getType() == 7) {
            // 设置设备指向服务器IP
            bytes = setDeviceServerIp(Short.parseShort(qo.getValue()));
        } else if (qo.getType() == 8) {
            // 开启通道
            bytes = openChannel(qo.getValue());
            msg = "通道" + qo.getValue().split(",")[0] + "已开启";
        } else if (qo.getType() == 9) {
            // 关闭通道
            bytes = closeChannel(qo.getValue());
            msg = "通道" + qo.getValue() + "已关闭";
        } else if (qo.getType() == 10) {
            // 批量打开通道‘
            String[] arr = qo.getValue().split("-");
            if (arr.length == 2) {
                String channels = arr[0];
                String params = arr[1];
                for (String c : channels.split(",")) {
                    bytes = openChannel(c + "," + params);
                    if (bytes.length > 0) {
                        channel.writeAndFlush(Unpooled.copiedBuffer(bytes));
                    }

                    TimeUnit.MILLISECONDS.sleep(50);
                }
            }

            bytes = null;
            msg = "通道" + arr[0] + "已全部开启";
        } else if (qo.getType() == 11) {
            // 关闭全部通道
            bytes = closeAllChannel();
//            for (String c : qo.getValue().split(",")) {
//                bytes = closeChannel(c);
//                if (bytes.length > 0) {
//                    channel.writeAndFlush(Unpooled.copiedBuffer(bytes));
//                }
//
//                TimeUnit.MILLISECONDS.sleep(50);
//            }
//            bytes = null;
            msg = "通道已全部关闭";
        } else if (qo.getType() == 12) {
            // 设置重启周期
            bytes = setRestartCircle(Short.parseShort(qo.getValue()));
            msg = "已将重启周期设置为：" + qo.getValue() + "小时";
        } else if (qo.getType() == 13) {
            // 清除所有历史数据
            bytes = clearAllHistoryData();
            msg = "已清除所有历史数据";
        }

        try {
            if (bytes != null && bytes.length > 0) {
                channel.writeAndFlush(Unpooled.copiedBuffer(bytes));
            }
        } catch (Exception e) {
            return R.error("操作失败，无法连接到设备");
        }
        return R.success(msg);
    }

    /**
     * 设置设备指向服务器IP
     */
    private byte[] setDeviceServerIp(short deviceId) {
        // 消息头
        StringBuilder sb = new StringBuilder("EB91EB91");
        // 命令码
        sb.append("0006");
        sb.append("000000000000");
        sb.append(HexUtil.bytesToHexNoZero(ByteUtil.shortToBytes(deviceId, ByteOrder.BIG_ENDIAN), true));
        sb.append("0D0A0D0A");
        return HexUtil.hexToBytes(sb.toString(), true);
    }

    /**
     * 设置设备IP
     */
    private byte[] setDeviceIp(short deviceId) {
        // 消息头
        StringBuilder sb = new StringBuilder("EB91EB91");
        // 命令码
        sb.append("0005");
        sb.append("000000000000");
        sb.append(HexUtil.bytesToHexNoZero(ByteUtil.shortToBytes(deviceId, ByteOrder.BIG_ENDIAN), true));
        sb.append("0D0A0D0A");
        return HexUtil.hexToBytes(sb.toString(), true);
    }

    /**
     * 停止采集
     */
    private byte[] stopCollect() {
        return HexUtil.hexToBytes("eb 91 eb 91 0002 00 00 00 00 00 00 00 00 0D 0A 0D 0A".replace(" ", "").toUpperCase(), true);
    }

    /**
     * 开始采集
     */
    private byte[] startCollect() {
        return HexUtil.hexToBytes("eb 91 eb 91 0002 FF FF FF FF FF FF FF FF 0D 0A 0D 0A".replace(" ", "").toUpperCase(), true);
    }

    /**
     * 重新启动
     */
    private byte[] restart() {
        return HexUtil.hexToBytes("eb 91 eb 91 0001 00 00 00 00 00 00 00 00 0D 0A 0D 0A".replace(" ", "").toUpperCase(), true);
    }

    /**
     * 设置设备ID
     */
    private byte[] setDeviceId(short deviceId) {
        // 消息头
        StringBuilder sb = new StringBuilder("EB91EB91");
        // 命令码
        sb.append("0004");
        sb.append("000000000000");
        sb.append(HexUtil.bytesToHexNoZero(ByteUtil.shortToBytes(deviceId, ByteOrder.BIG_ENDIAN), true));
        sb.append("0D0A0D0A");
        return HexUtil.hexToBytes(sb.toString(), true);
    }

    /**
     * 设置重启周期
     */
    private byte[] setRestartCircle(short hour) {
        // 消息头
        StringBuilder sb = new StringBuilder("EB91EB91");
        // 命令码
        sb.append("0009");
        sb.append(HexUtil.bytesToHexNoZero(ByteUtil.shortToBytes(hour, ByteOrder.BIG_ENDIAN), true));
        sb.append("FFFFFFFFFFFF");
        sb.append("0D0A0D0A");
        return HexUtil.hexToBytes(sb.toString(), true);
    }

    /**
     * 清除所有历史数据
     */
    private byte[] clearAllHistoryData() {
        // 消息头
        StringBuilder sb = new StringBuilder("EB91EB91");
        // 命令码
        sb.append("0009");
        sb.append("FFFFFFFFFFFFFFFF");
        sb.append("0D0A0D0A");
        return HexUtil.hexToBytes(sb.toString(), true);
    }

    /**
     * 同步时间
     */
    private byte[] syncTime() {
        // 消息头
        StringBuilder sb = new StringBuilder("EB91EB91");
        // 命令码
        sb.append("0003");
        sb.append(HexUtil.bytesToHexNoZero(ByteUtil.longToBytes(new Date().getTime(), ByteOrder.BIG_ENDIAN), true));
        sb.append("0D0A0D0A");
        return HexUtil.hexToBytes(sb.toString(), true);
    }

    /**
     * 开启通道
     */
    private static byte[] openChannel(String value) {
//        打开通道13，通道类型设置为电压，测量范围为5V,供电电压 10V, 采样频率为100Hz
//        eb 91 eb 91 0007 000D 0000 0001 0102 0D 0A 0D 0A
        String[] arr = value.split(",");
        int channelNum = Integer.parseInt(arr[0]);
        int type = Integer.parseInt(arr[1]);
        int range = Integer.parseInt(arr[2]);
        int voltage = Integer.parseInt(arr[3]);
        int hz = Integer.parseInt(arr[4]);
        // 消息头
        StringBuilder sb = new StringBuilder("EB91EB91");
        // 命令码
        sb.append("0007");
        // 通道
        sb.append(intToHexStr2Byte(channelNum));
        // 通道类型
        sb.append(type == 0 ? "0000" : "0001");
        // 测量范围
        sb.append(intToHexStr2Byte(range));
        // 供电电压
        sb.append(intToHexStr1Byte(voltage));
        // 采样频率
        sb.append(intToHexStr1Byte(hz));
        sb.append("0D0A0D0A");
        log.info("开启通道：{}", sb.toString());
        return HexUtil.hexToBytes(sb.toString(), true);
    }

    /**
     * 关闭通道
     */
    private static byte[] closeChannel(String channelNum) {
        // 消息头
        StringBuilder sb = new StringBuilder("EB91EB91");
        // 命令码
        sb.append("0008");
        sb.append(intToHexStr2Byte(Integer.parseInt(channelNum)));
        sb.append("000000000000");
        sb.append("0D0A0D0A");
        log.info("关闭通道：{}", sb.toString());
        return HexUtil.hexToBytes(sb.toString(), true);
    }

    /**
     * 关闭全部通道
     */
    private static byte[] closeAllChannel() {
        // 消息头
        StringBuilder sb = new StringBuilder("EB91EB91");
        // 命令码
        sb.append("0008");
        sb.append("FFFF");
        sb.append("000000000000");
        sb.append("0D0A0D0A");
        return HexUtil.hexToBytes(sb.toString(), true);
    }

    /**
     * int数值转2字节十六进制
     */
    public static String intToHexStr2Byte(int num) {
        if (num < 0 || num >= 300) {
            throw new IllegalArgumentException("输入的整数需要小于100");
        }
        String hex = Integer.toHexString(num);
        // 如果长度不足4，在前面补0
        while (hex.length() < 4) {
            hex = "0" + hex;
        }
        return hex.toUpperCase();
    }

    /**
     * int数值转1字节十六进制
     */
    public static String intToHexStr1Byte(int num) {
        String hex = Integer.toHexString(num);
        // 如果长度不足2，在前面补0
        while (hex.length() < 2) {
            hex = "0" + hex;
        }
        return hex.toUpperCase();
    }


    /*
                channel.writeAndFlush(Unpooled.copiedBuffer(stopCollect()));
            TimeUnit.MILLISECONDS.sleep(100);

            // 关闭通道
            bytes = closeChannel(qo.getValue());
            msg = "通道" + qo.getValue().split(",")[0] + "已关闭";

            TimeUnit.MILLISECONDS.sleep(100);
            channel.writeAndFlush(Unpooled.copiedBuffer(startCollect()));
     */


}
