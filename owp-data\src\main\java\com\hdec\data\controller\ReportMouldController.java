package com.hdec.data.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hdec.common.domain.R;
import com.hdec.data.domain.ChartNum;
import com.hdec.data.domain.ReportOutlineNav;
import com.hdec.data.domain.template.Mould;
import com.hdec.data.qo.MouldQo;
import com.hdec.data.service.ReportMouldService;
import com.hdec.data.vo.FieldVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 报告模板控制器
 *
 * <AUTHOR>
 */
@Api(tags = "报告模板管理")
@Validated
@Slf4j
@RestController
@RequestMapping("api/data/mould")
public class ReportMouldController {

    @Autowired
    private ReportMouldService mouldService;

    /**
     * 模板列表
     */
    @ApiOperation("模板列表")
    @PostMapping("list")
    public R list(@RequestHeader("fieldNum") String fieldNum, @RequestBody MouldQo qo) {
        PageHelper.startPage(qo.getPageNum(), qo.getPageSize());
        List<Mould> moulds = mouldService.list(fieldNum, qo);
        return R.success(new PageInfo<>(moulds));
    }

    /**
     * 风场下所有模板
     */
    @ApiOperation("风场下所有模板")
    @PostMapping("listByField/{fieldNum}")
    public R list(@PathVariable("fieldNum") String fieldNum) {
        List<Mould> moulds = mouldService.listByField(fieldNum);
        return R.success(moulds);
    }

    /**
     * 获取某个模板详情
     */
    @ApiOperation("获取某个模板详情")
    @GetMapping("detail/{id}")
    public R detail(@PathVariable Integer id) {
        Mould mould = mouldService.detail(id);
        return R.success(mould);
    }

    /**
     * 修改模板
     */
    @ApiOperation("修改模板")
    @PostMapping("update")
    public R update(@RequestBody Mould mould) {
        Map<String, String> map = mouldService.update(mould);
        return R.success(map);
    }

    /**
     * 判断模板名称是否存在
     */
    @ApiOperation("判断模板名称是否存在")
    @PostMapping("isNameExist")
    public R isNameExist(@RequestHeader("fieldNum") String fieldNum, @RequestBody Mould mould) {
        mould.setFieldNum(fieldNum);
        Mould existMould = mouldService.selectByName(mould.getId(), mould.getFieldNum(), mould.getName());
        if (existMould != null) {
            return R.error("模板名称已存在");
        }
        return R.success();
    }

    /**
     * 新增模板
     */
    @ApiOperation("新增模板")
    @PostMapping("add")
    public R add(@RequestHeader("fieldNum") String fieldNum, @RequestBody Mould mould, @RequestHeader("sessionId") String sessionId) {
        mould.setFieldNum(fieldNum);
        Integer id = mouldService.add(mould, sessionId);
        if (id == -1) {
            return R.error("模板名称已存在");
        }
        return R.success(id);
    }

    /**
     * 批量删除模板
     */
    @ApiOperation("批量删除模板")
    @DeleteMapping("{ids}")
    public R deleteBatch(@PathVariable Integer[] ids) {
        return mouldService.delete(ids);
    }

    /**
     * 获取所有模板标签
     */
    @ApiOperation("获取所有模板标签")
    @GetMapping("getAllMouldLabels")
    public R getAllMouldLabels(@RequestHeader("fieldNum") String fieldNum) {
        Set<String> labels = mouldService.getAllMouldLabels(fieldNum);
        return R.success(labels);
    }

    /**
     * 列出某模板中的所有图表编号
     */
    @ApiOperation("列出某模板中的所有图表编号")
    @GetMapping("listChartNumsByMould/{mouldId}")
    public R listChartNumsByMould(@PathVariable Integer mouldId) {
        List<ChartNum> chartNums = mouldService.listChartNumsByMould(mouldId);
        return R.success(chartNums);
    }

    /**
     * 列出某大纲中的所有图表编号(导航树方式)
     */
    @ApiOperation("列出某大纲中的所有图表编号(导航树方式)")
    @GetMapping("listChartNumsByOutline/{outlineId}")
    public R listChartNumsByOutline(@RequestHeader("fieldNum") String fieldNum, @PathVariable Integer outlineId) {
        List<ReportOutlineNav> navs = mouldService.listChartNumsByOutline(fieldNum, outlineId);
        return R.success(navs);
    }

    /**
     * 获取权限范围内所有风场
     */
    @ApiOperation("获取权限范围内所有风场")
    @PostMapping("listAuthFields")
    public R listAuthFields(@RequestHeader("fieldNum") String fieldNum, @RequestHeader("sessionId") String sessionId) {
        List<FieldVo> fieldVos = mouldService.listAuthFields(fieldNum, sessionId);
        return R.success(fieldVos);
    }

    /**
     * 获取权限范围内所有风场的模板(导航树方式)
     */
    @ApiOperation("获取权限范围内所有风场的模板(导航树方式)")
    @PostMapping("listAuthFieldMoulds")
    public R listAuthFieldMoulds(@RequestHeader("fieldNum") String fieldNum, @RequestHeader("sessionId") String sessionId) {
        List<ReportOutlineNav> navs = mouldService.listAuthFieldMoulds(fieldNum, sessionId);
        return R.success(navs);
    }

    /**
     * 批量导入模板
     */
    @ApiOperation("批量导入模板")
    @PostMapping("importMoulds")
    public R importMoulds(@RequestHeader("fieldNum") String fieldNum, Integer[] mouldIds, @RequestHeader("sessionId") String sessionId) {
        mouldService.importMoulds(fieldNum, mouldIds, sessionId);
        return R.success("导入成功");
    }

    /**
     * 模板预览
     */
    @ApiOperation("模板预览")
    @GetMapping("mouldPreview/{mouldId}")
    public String mouldPreview(@PathVariable Integer mouldId) {
        return mouldService.mouldPreview(mouldId);
    }

}
