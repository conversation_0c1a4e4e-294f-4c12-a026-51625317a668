package com.hdec.data.conector;


import com.ghgande.j2mod.modbus.facade.ModbusTCPMaster;
import com.ghgande.j2mod.modbus.procimg.InputRegister;
import com.hdec.common.domain.msg.Inst;
import com.hdec.data.service.DataAccessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;

@Slf4j
@Service
@EnableScheduling
@ConditionalOnProperty(value = "connector.reference-electrode.enable", havingValue = "true", matchIfMissing = false)
public class ReferenceElectrodeService {

    private static final int THREAD_POOL_SIZE = 10;

    private static final ThreadPoolExecutor EXECUTOR = new ThreadPoolExecutor(THREAD_POOL_SIZE, THREAD_POOL_SIZE,
            0L, TimeUnit.MILLISECONDS, new SynchronousQueue<>(), Executors.defaultThreadFactory(),
            new ThreadPoolExecutor.CallerRunsPolicy()
    );
    @Resource
    private ReferenceElectrodeProperties properties;

    @Resource(name = "referenceDevice")
    private Map<String, ModbusTCPMaster> referenceDevice;

    @Resource
    private DataAccessService accessService;

    @Scheduled(cron = "${connector.reference-electrode.cron: 0 0 */1 * * ?}")
    public void readChannel() {
        Map<String, ReferenceElectrodeProperties.ModbusProperties> device = properties.getDevice();
        Date timestamp = new Date(new Date().getTime() / 1000 * 1000);
        EXECUTOR.execute(() -> {
            try {
                List<Inst> instList = new ArrayList<>();
                referenceDevice.forEach((key, master) -> {
                    if (master == null || device.get(key) == null || device.get(key).getChannels() == null ||
                            device.get(key).getChannels().isEmpty()) {
                        return;
                    }
                    Map<Integer, Float[]> chs = new HashMap<>();
                    Inst inst = new Inst(key, timestamp, chs);
                    Map<Integer, Integer> channels = device.get(key).getChannels();
                    channels.forEach((channel, address) -> {
                        /*  这里是为了适配accessService.input里面的加一  */
                        channel = channel - 1;
                        try {
                            if (!master.isConnected()) {
                                master.connect();
                            }
                            InputRegister[] registers = master.readInputRegisters(address, 1);
                            if (registers != null && registers.length > 0) {
                                int value = registers[0].toShort();
                                log.info("读取参比电极Modbus[{}][{}][{}]", key, channel, value);
                                chs.put(channel, new Float[]{(float) value});
                            }
                        } catch (Exception e) {
                            log.error("读取参比电极Modbus异常[{}]", device.get(key), e);
                        } finally {
                            master.disconnect();
                        }
                    });
                    if (!chs.isEmpty()) {
                        instList.add(inst);
                    }
                });
                if (!instList.isEmpty()) {
                    accessService.input(instList, "dynamic", false);
                }
            } catch (Exception e) {
                log.error("读取参比电极Modbus异常", e);
            }

        });
    }

}
