package com.hdec.data.domain.template;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hdec.data.domain.Outline;
import com.hdec.data.vo.ParamVo;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * 模板
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class Mould {

    /** 自增主键 */
    private Integer id;

    /** 名称 */
    private String name;

    /** 类型 */
    private String type;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createTime;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updateTime;

    /** 操作人ID */
    private Integer userId;

    /** 操作人用户名 */
    private String username;

    /** 标签数组 */
    private String[] labels;

    /** 标签 */
    private String label;

    private Integer navId;

    /** 内容 */
    private String content;

    private String fieldNum;

    /** 是否告警 */
    private Boolean isWarning = false;

    /** 该模板所绑定的大纲集合 */
    private List<Outline> outlines;

    private String fanVal;

    private String fanValName;

    private List<ParamVo> refValArr;

}
