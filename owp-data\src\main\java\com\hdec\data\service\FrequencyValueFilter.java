package com.hdec.data.service;

import com.alibaba.fastjson.serializer.ValueFilter;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 频谱小数序列化（保留小数位数）
 *
 * <AUTHOR>
 */
@Component
public class FrequencyValueFilter implements ValueFilter {

    @Override
    public Object process(Object object, String name, Object value) {
        if ("xArr".equals(name)) {
            return handleValue((List<Double>) value, "%.5f");
        }
        if ("yArr".equals(name)) {
            return handleValue((List<Double>) value, "%.6f");
        }
        return value;
    }

    /**
     * 处理Double类型数据
     */
    private Object handleValue(List<Double> value, String format) {
        List<Double> nums = value;
        List<Double> newNums = new ArrayList<>(nums.size());
        for (int i = 0; i < nums.size(); i++) {
            newNums.add(Double.parseDouble(String.format(format, nums.get(i))));
        }
        return newNums;
    }

}