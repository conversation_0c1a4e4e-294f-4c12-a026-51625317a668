package com.hdec.data.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/7/29
 */
@Data
@ToString
@NoArgsConstructor
public class Td {

    /** 列合并 */
    private Integer c;

    /** 行合并 */
    private Integer r;

    /** 内容 */
    private String v;

    /** 内容(json格式) */
    private String j;

    public Td(String v, String j) {
        this.c = 1;
        this.r = 1;
        this.v = v;
        this.j = j;
    }

    public Td(Integer c, Integer r, String v) {
        this.c = c;
        this.r = r;
        this.v = v;
    }

    public Td(String v) {
        this.c = 1;
        this.r = 1;
        this.v = v;
    }

}
