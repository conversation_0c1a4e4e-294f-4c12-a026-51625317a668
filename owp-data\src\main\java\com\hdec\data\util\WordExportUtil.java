package com.hdec.data.util;

import freemarker.template.Configuration;
import freemarker.template.Template;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.util.Map;

/**
 * 导出word工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class WordExportUtil {

    /**
     * @param templateFilePath eg: /template/test/test.ftl
     * @param dataMap
     * @param exportFilePath   eg: /tmp/test/test123.doc
     * @return
     * @throws Exception
     */
    public static File createDocFile(String templateFilePath, Map<String, Object> dataMap, String exportFilePath) throws Exception {
        Template t = null;
        Configuration configuration = new Configuration(Configuration.VERSION_2_3_28);
        configuration.setDefaultEncoding("UTF-8");
        try {
            templateFilePath = pathReplace(templateFilePath);
            String ftlPath = templateFilePath.substring(0, templateFilePath.lastIndexOf("/"));
            configuration.setDirectoryForTemplateLoading(new File(ftlPath)); // FTL文件所存在的位置

            String ftlFile = templateFilePath.substring(templateFilePath.lastIndexOf("/") + 1);
            t = configuration.getTemplate(ftlFile); // 模板文件名

            File outFile = new File(exportFilePath);
            Writer out  = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(outFile)));

            t.process(dataMap, out);
            out.close();
        } catch (Exception e) {
            log.error("导出word文档出错{}", e);
            throw e;
        }
        return null;
    }

    /**
     * 把路径的\替换成/
     */
    private static String pathReplace(String path) {
        while (path != null && path.contains("\\")) {
            path = path.replace("\\", "/");
        }
        return path;
    }

}