package com.hdec.data.domain.domin_word;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 总结
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class ReportSummaryInfo {

    /** 倾斜 */
    private Double tiltBaseMin;
    private Double tiltBaseMax;
    private Double tiltBaseAvg;
    private Double tiltTopMin;
    private Double tiltTopMax;
    private Double tiltTopAvg;

    /** 振动 */
    private Double shockBaseMin;
    private Double shockBaseMax;
    private Double shockTopMin;
    private Double shockTopMax;

    private Double shockMin;
    private Double shockMax;

    /** 应力 */
    private Double stressMin;
    private Double stressMax;

    public Double getShockMin() {
        if (shockBaseMin == null && shockTopMin != null) {
            return shockTopMin;
        } else if (shockBaseMin != null && shockTopMin == null) {
            return shockBaseMin;
        } else if (shockBaseMin != null && shockTopMin != null) {
            return Math.min(shockBaseMin, shockTopMin);
        }
        return null;
    }

    public Double getShockMax() {
        if (shockBaseMax == null && shockTopMax != null) {
            return shockTopMax;
        } else if (shockBaseMax != null && shockTopMax == null) {
            return shockBaseMax;
        } else if (shockBaseMax != null && shockTopMax != null) {
            return Math.max(shockBaseMax, shockTopMax);
        }
        return null;
    }

}
