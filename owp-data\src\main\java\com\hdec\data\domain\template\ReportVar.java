package com.hdec.data.domain.template;

import com.hdec.data.domain.CustomVarParam;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * 报告变量
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class ReportVar {

    /** 自增主键 */
    private Integer id;

    /** 变量名称 */
    private String name;

    /** 变量类型 */
    private String type;

    /** 变量类型 */
    private String dataType;

    /** 仪器ID */
    private String paramJson;

    /** 参数 */
    private Integer instId;

    /** 自定义变量参数 */
    private List<CustomVarParam> params;

    /** 风场编码 */
    private String fieldNum;

}
