package com.hdec.data.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hdec.common.constant.Constant;
import com.hdec.common.domain.AttrCommon;
import com.hdec.common.domain.PointCommon;
import com.hdec.common.domain.SettingsCommon;
import com.hdec.common.util.CommonUtil;
import com.hdec.common.util.TimeUtil;
import com.hdec.common.vo.Direct;
import com.hdec.common.vo.InstDirectAttr;
import com.hdec.data.domain.ExcelSheet;
import com.hdec.data.domain.taos.Record;
import com.hdec.data.domain.v2.ExportTask;
import com.hdec.data.feign.MonitorService;
import com.hdec.data.feign.WindService;
import com.hdec.data.mapper.ExportTaskMapper;
import com.hdec.data.mapper.TaosMapper;
import com.hdec.data.util.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
@EnableScheduling
public class ExportTaskService {

    private static final String EXPORT_SECRET_SETTINGS_KEY = "DATA_EXPORT_TASK_SECRET";
    /**
     * 线程池数量
     */
    private static final int THREAD_NUM = 1;

    /**
     * 线程池
     */
    private final static ThreadPoolExecutor THREAD_POOL = new ThreadPoolExecutor(THREAD_NUM, THREAD_NUM,
            0L, TimeUnit.MILLISECONDS, new SynchronousQueue<>());

    /**
     * 文件本地路径 - Windows
     */
    @Value("${file.windowPath}")
    private String windowPath;

    /**
     * 文件本地路径 - Linux
     */
    @Value("${file.linuxPath}")
    private String linuxPath;
    @Resource
    private ExportTaskMapper exportTaskMapper;

    @Resource
    private TaosMapper taosMapper;

    private TaosService taosService;

    @Resource
    private MonitorService monitorService;

    @Resource
    private WindService windService;

    @Scheduled(initialDelay = 10 * 1000, fixedRate = 10 * 1000)
    public void scheduled() {
        if (THREAD_POOL.getActiveCount() < THREAD_NUM) {
            /*  获取下一个任务执行  */
            ExportTask task = exportTaskMapper.selectNextTask();
            if (task != null && THREAD_POOL.getActiveCount() < THREAD_NUM) {
                /*  异步执行导出任务  */
                log.info("[导出任务]执行扫描到任务:{}", task);
                THREAD_POOL.execute(() -> export(task));
            }
        }

    }

    public boolean secretVerify(String fieldNum, String secret, String salt) {
        SettingsCommon settings = windService.getSettings(fieldNum, EXPORT_SECRET_SETTINGS_KEY);
        if (settings == null || settings.getId() == null) {
            log.error("导出任务密钥未配置[{}]:{}", EXPORT_SECRET_SETTINGS_KEY, fieldNum);
            return false;
        }
        return Objects.equals(secret, saltHash(settings.getValue(), salt));
    }

    private static String saltHash(String password, String salt) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            md.update(salt.getBytes());
            byte[] hashedBytes = md.digest(password.getBytes());
            return Base64.getEncoder().encodeToString(hashedBytes);
        } catch (NoSuchAlgorithmException e) {
            log.error("加密失败:{}", e.getMessage());
        }
        return null;
    }

    public PageInfo<ExportTask> page(String fieldNum, int pageNum, int pageSize, ExportTask task) {
        PageHelper.startPage(pageNum, pageSize);
        List<ExportTask> list = exportTaskMapper.page(task);
        PageInfo<ExportTask> page = new PageInfo<>(list);
        PageHelper.clearPage();
        /*  转换测点&分量名称  */
        if (!list.isEmpty()) {
            Map<Integer, String> pointMap = monitorService.getPointsByFieldNum(fieldNum).stream()
                    .collect(Collectors.toMap(PointCommon::getId, PointCommon::getNo));
            Map<Integer, String> attrMap = monitorService.allAttrByField(fieldNum).stream()
                    .collect(Collectors.toMap(AttrCommon::getId, AttrCommon::getName));
            list.forEach(t -> {
                String pointIds = t.getPointIds();
                if (pointIds != null && !pointIds.isEmpty()) {
                    List<String> pointNos = Arrays.stream(pointIds.split(","))
                            .map(Integer::parseInt)
                            .map(pointMap::get)
                            .filter(Objects::nonNull).collect(Collectors.toList());
                    t.setPointNos(String.join(",", pointNos));
                }
                String attrIds = t.getAttrIds();
                if (attrIds != null && !attrIds.isEmpty()) {
                    List<String> attrNames = Arrays.stream(attrIds.split(","))
                            .map(Integer::parseInt)
                            .map(attrMap::get)
                            .filter(Objects::nonNull).collect(Collectors.toList());
                    t.setAttrNames(String.join(",", attrNames));
                }
            });
        }

        return page;
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void addTask(ExportTask task) {
        task.setProgress(0D).setStatus(0);
        /*  校验信息  */
        String attrIds = task.getAttrIds();
        List<AttrCommon> attrs = monitorService.allAttrByField(task.getFieldNum());
        List<String> allAttrIds = attrs.stream().filter(e -> !Objects.isNull(e.getId()))
                .map(e -> e.getId() + "").collect(Collectors.toList());
        List<String> attrIdList = Arrays.asList(attrIds.split(","));
        if (attrIdList.isEmpty()) {
            throw new RuntimeException("未读取到分量ID");
        }
        /*  校验分量ID合法性  */
        allAttrIds.stream().filter(e -> !allAttrIds.contains(e)).findFirst().ifPresent(e -> {
            throw new RuntimeException("分量ID[" + e + "]不存在");
        });
        /*  校验分量是否相同频率  */
        Set<String> rateSet = attrs.stream().filter(e -> attrIdList.contains(String.valueOf(e.getId())))
                .map(AttrCommon::getRate).collect(Collectors.toSet());
        if (rateSet.size() != 1) {
            throw new RuntimeException("导出任务所选分量必须为相同频率");
        }
        rateSet.forEach(task::setRate);
        exportTaskMapper.insert(task);
        /*  异步执行导出任务  */
        scheduled();
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteTask(Integer id) {
        /*  先修改状态,防止查到  */
        exportTaskMapper.updateStatus(id, -1, "已删除");
        ExportTask exportTask = exportTaskMapper.selectById(id);
        if (exportTask != null &&
                exportTask.getUrl() != null &&
                !exportTask.getUrl().isEmpty()) {
            /*  删除文件  */
            try {
                boolean b = Files.deleteIfExists(Paths.get(exportTask.getUrl()));
                if (!b) {
                    log.error("删除文件失败:{}", exportTask.getUrl());
                }
            } catch (Exception e) {
                log.error("删除文件异常:{}", exportTask.getUrl());
            }
        }
        exportTaskMapper.delete(id);
    }

    public void deleteFile(Integer id) {
        ExportTask exportTask = exportTaskMapper.selectById(id);
        if (exportTask != null &&
                exportTask.getUrl() != null &&
                !exportTask.getUrl().isEmpty()) {
            /*  删除文件  */
            try {
                boolean b = Files.deleteIfExists(Paths.get(exportTask.getUrl()));
                if (b) {
                    exportTaskMapper.updateUrl(id, null);
                } else {
                    log.error("删除文件失败:{}", exportTask.getUrl());
                }
            } catch (Exception e) {
                log.error("删除文件异常:{}", exportTask.getUrl());
            }
        }
    }

    public void download(Integer id, HttpServletResponse response) throws Exception {
        ExportTask exportTask = exportTaskMapper.selectById(id);
        if (exportTask == null ||
                exportTask.getUrl() == null ||
                exportTask.getUrl().isEmpty()) {
            log.error("文件不存在:{}", id);
            return;
        }
        File file = new File(exportTask.getUrl());
        if (!file.exists()) {
            log.error("文件不存在:{}", id);
            return;
        }
        response.setHeader("Content-Disposition", "attachment; filename=" + file.getName());
        response.setContentType("application/octet-stream");
        try {
            Files.copy(file.toPath(), response.getOutputStream());
        } catch (Exception e) {
            log.error("文件下载异常:{}", file.getName());
        }
    }

    public void export(ExportTask task) {
        try {
            exportTaskMapper.exportStartUpdate(task.getId());
            String pointIds = task.getPointIds();
            String rate = task.getRate();
            List<Integer> taskAttrIds = Arrays.stream(task.getAttrIds().split(","))
                    .map(Integer::parseInt).collect(Collectors.toList());
            Date startTime = task.getStartTime();
            Date endTime = task.getEndTime();
            if (pointIds == null || pointIds.isEmpty() || taskAttrIds.isEmpty() || startTime == null || endTime == null) {
                log.error("执行导出异常:{}", task);
                exportTaskMapper.updateStatus(task.getId(), -1, "参数异常");
                return;
            }
            if (!(Objects.equals(Constant.RATE_HIGH, rate) ||
                    Objects.equals(Constant.RATE_MIN, rate) ||
                    Objects.equals(Constant.RATE_HOUR, rate))) {
                log.error("执行导出异常:{}", task);
                exportTaskMapper.updateStatus(task.getId(), -1, "未知频率");
                return;
            }
            /*  文件记录  */
            String timeName = TimeUtil.format2Day(task.getStartTime()) + "_" + TimeUtil.format2Day(TimeUtil.addDays(task.getEndTime(), -1));
            String dir = CommonUtil.isLinux() ? linuxPath : windowPath;
            String outDir = (dir.endsWith("/") ? dir : dir + "/") + "exportTask/" + task.getFieldNum() + "-" + task.getId() + "/";
            List<String> zipFiles = new ArrayList<>();
            String zipPath = outDir + timeName + ".zip";
            String excelPath = outDir + timeName + ".xlsx";

            String[] points = pointIds.split(",");
            List<Pair<Date, Date>> days = splitDay(startTime, endTime);
            Map<String, ExcelSheet> excelSheetMap = new HashMap<>();
            int totalProgress = points.length * days.size();
            for (int pi = 0; pi < points.length; pi++) {
                /*  查询相关信息  */
                Integer pointId = Integer.parseInt(points[pi]);
                String pointNo = monitorService.getNoById(pointId);
                InstDirectAttr instAttrs = monitorService.getInstAttrsByRate(pointId, rate);
                if (instAttrs == null || instAttrs.getInstId() == null || instAttrs.getAttrs() == null || instAttrs.getDirects() == null) {
                    log.error("数据导出-测点仪器信息获取失败:{}-{}[{}-{}]  [attrs:{}]", pointId, rate, startTime, endTime, instAttrs);
                    return;
                }
                Integer instId = instAttrs.getInstId();
                List<AttrCommon> attrs = instAttrs.getAttrs().stream()
                        .filter(e -> taskAttrIds.contains(e.getId())).collect(Collectors.toList());
                if (attrs.isEmpty()) {
                    log.info("数据导出-测点未设置该频率分量:{}-{}[{}-{}]  [attrs:{}]", pointId, rate, startTime, endTime, instAttrs);
                    continue;
                }
                List<Integer> attrIds = attrs.stream().map(AttrCommon::getId).collect(Collectors.toList());
                if (attrIds.isEmpty()) {
                    continue;
                }
                List<Integer> directs = instAttrs.getDirects().stream().map(Direct::getDirectId).collect(Collectors.toList());
                List<String> titles = attrs.stream().map(attr -> instAttrs.getDirects().stream()
                        .map(direct -> direct.getDirectName() + "方向" + attr.getName())
                        .collect(Collectors.toList())).flatMap(Collection::stream).collect(Collectors.toList());
                titles.add(0, "测点编号");
                titles.add(1, "时间");
                List<String> keys = attrs.stream().map(attr -> instAttrs.getDirects().stream()
                        .map(direct -> "a_" + attr.getId() + "_" + direct.getDirectId())
                        .collect(Collectors.toList())).flatMap(Collection::stream).collect(Collectors.toList());
                keys.add(0, "point_no");
                keys.add(1, "ts");
                for (Pair<Date, Date> day : days) {
                    Date dayStart = day.getLeft();
                    Date dayEnd = day.getRight();
                    String txtPath = outDir + TimeUtil.format2Day(dayStart) + "_" + pointNo + ".txt";
                    /*  高频数据  */
                    if (Objects.equals(Constant.RATE_HIGH, rate)) {
                        List<Pair<Date, Date>> times = splitTime(dayStart, dayEnd, 60);
                        for (int i = 0; i < times.size(); i++) {
                            Pair<Date, Date> time = times.get(i);
                            Date timeStart = time.getLeft();
                            Date timeEnd = time.getRight();
                            List<Record> records = selectRecord(rate, instId, pointId, attrIds, directs, timeStart, timeEnd);
                            /*  写入TXT  */
                            if (i == 1) {
                                /*  写表头  */
                                writToTxtFile(titles, keys, pointNo, Collections.emptyList(), txtPath, false);
                            }
                            if (!records.isEmpty()) {
                                writToTxtFile(titles, keys, pointNo, records, txtPath, true);
                                if (!zipFiles.contains(txtPath)) {
                                    zipFiles.add(txtPath);
                                }
                            }
                        }
                    } else {
                        /*  分钟和小时数据  */
                        List<Record> records = selectRecord(rate, instId, pointId, attrIds, directs, dayStart, dayEnd);
                        /*  写入EXCEL  */
                        ExcelSheet excelSheet = excelSheetMap.get(pointNo);
                        if (excelSheet == null) {
                            excelSheet = new ExcelSheet(keys.toArray(new String[0]),
                                    titles.toArray(new String[0]), new ArrayList<>());
                            excelSheetMap.put(pointNo, excelSheet);
                        }
                        List<Map<String, Object>> values = excelSheet.getValues();
                        records.forEach(record -> record.put("point_no", pointNo));
                        values.addAll(records);
                    }
                    double progress = (pi * days.size() + days.indexOf(day) + 1) * 100 / (double) totalProgress;
                    exportTaskMapper.updateProgress(task.getId(), progress > 99 ? 99 : progress);
                }
            }
            if (!zipFiles.isEmpty()) {
                /* 打包文件 */
                exportTaskMapper.updateStatus(task.getId(), 1, "打包中");
                DataService.zipFiles(zipFiles, zipPath);
                zipFiles.forEach(file -> {
                    try {
                        Files.deleteIfExists(Paths.get(file));
                    } catch (Exception e) {
                        log.error("删除文件异常:{}", file, e);
                    }
                });
                exportTaskMapper.updateUrl(task.getId(), zipPath);
            }
            if (!excelSheetMap.isEmpty()) {
                /*  写入EXCEL  */
                exportTaskMapper.updateStatus(task.getId(), 1, "写入Excel中");
                Files.createDirectories(Paths.get(excelPath).getParent());
                ExcelUtil.createNBig2File("数据.xlsx", excelSheetMap, excelPath);
                exportTaskMapper.updateUrl(task.getId(), excelPath);
            }
            exportTaskMapper.updateProgress(task.getId(), 100D);
            exportTaskMapper.exportEndUpdate(task.getId());
        } catch (Exception e) {
            log.error("导出任务执行异常:{}", task, e);
            exportTaskMapper.updateStatus(task.getId(), -1, "执行异常:" + e.getMessage());
        }

    }

    /**
     * 查询记录
     *
     * @param rate      频率
     * @param instId    仪器ID
     * @param pointId   测点ID
     * @param attrs     分量
     * @param directs   方向
     * @param startTime 开始
     * @param endTime   结束
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     */
    public List<Record> selectRecord(String rate, Integer instId, Integer pointId, List<Integer> attrs, List<Integer> directs, Date startTime, Date endTime) {
        String tableName = taosService.getTableName(rate, instId, pointId, true);
        List<String> columns = attrs.stream().map(attr -> directs.stream().map(direct -> "a_" + attr + "_" + direct).collect(Collectors.toList()))
                .flatMap(Collection::stream).collect(Collectors.toList());
        StringBuilder sql = new StringBuilder().append("select ts, point ,");
        columns.forEach(column -> sql.append(column).append(", "));
        sql.delete(sql.length() - 2, sql.length());
        sql.append(" from ").append(tableName).append(" where ts >= '").append(TimeUtil.format2Second(startTime))
                .append("' and ts < '").append(TimeUtil.format2Second(endTime)).append("' order by ts asc");
        return taosMapper.selectSql(sql.toString());
    }

    public void writToTxtFile(List<String> titles, List<String> keys, String pointNo, List<Record> records,
                              String filepath, boolean append) {
        try {
            Files.createDirectories(Paths.get(filepath).getParent());
            File file = new File(filepath);
            if (!file.exists() && !file.createNewFile()) {
                log.error("[数据导出]-创建文件目录失败:{}", filepath);
            }
        } catch (IOException e) {
            log.error("[数据导出]-创建文件目录失败:{}", filepath, e);
            return;
        }
        try (FileWriter writer = new FileWriter(filepath, append)) {
            /*  写入列名  */
            if (!append) {
                StringBuilder header = new StringBuilder();
                for (String title : titles) {
                    header.append(title).append("\t");
                }
                writer.write(header + "\n");
            }
            /*  写入数据  */
            for (Record record : records) {
                StringBuilder dataLine = new StringBuilder();
                Date ts = record.getTs();
                dataLine.append(pointNo).append("\t").append(TimeUtil.format2Ms(ts)).append("\t");
                for (String key : keys) {
                    if ("pointNo".equals(key) || "point_no".equals(key) || "ts".equals(key)) {
                        continue;
                    }
                    Float v = record.getFloat(key);
                    dataLine.append(v != null ? v : "null").append("\t");
                }
                writer.write(dataLine + "\n");
            }
        } catch (IOException e) {
            log.error("[数据导出]-写入数据到文件失败:{}", filepath, e);
        }
    }

    /**
     * 按天切割
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return {@link List }<{@link Pair }<{@link Date }, {@link Date }>>
     */
    private static List<Pair<Date, Date>> splitDay(Date startTime, Date endTime) {
        long start = startTime.getTime();
        long end = endTime.getTime();
        Date date = TimeUtil.parse2Day(TimeUtil.format2Day(startTime));
        if (start > end || date == null) {
            log.error("按天切割异常:[{}-{}]", startTime, endTime);
            return Collections.emptyList();
        }
        date = TimeUtil.addDays(date, 1);
        List<Pair<Date, Date>> times = new ArrayList<>();
        times.add(Pair.of(startTime, date));
        long segStart = date.getTime();
        while (segStart < end) {
            long segEnd = segStart + 24 * 60 * 60 * 1000;
            segEnd = Math.min(segEnd, end);
            times.add(Pair.of(new Date(segStart), new Date(segEnd)));
            segStart = segEnd;
        }
        return times;
    }

    /**
     * 切割时间
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param interval  间隔(秒)
     * @return {@link List }<{@link Pair }<{@link Date }, {@link Date }>>
     */
    private static List<Pair<Date, Date>> splitTime(Date startTime, Date endTime, long interval) {
        long start = startTime.getTime();
        long end = endTime.getTime();
        if (start > end) {
            log.error("时间切割异常:[{}-{}][{}]", startTime, endTime, interval);
            return Collections.emptyList();
        }
        interval = interval * 1000;
        List<Pair<Date, Date>> times = new ArrayList<>();
        long segStart = start;
        while (segStart < end) {
            long segEnd = Math.min((segStart + interval), end);
            times.add(Pair.of(new Date(segStart), new Date(segEnd)));
            segStart += interval;
        }
        return times;
    }
}
