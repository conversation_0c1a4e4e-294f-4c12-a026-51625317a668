package com.hdec.data.service;

import com.alibaba.druid.pool.DruidDataSource;
import com.hdec.common.constant.Constant;
import com.hdec.common.vo.Direct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

/**
 * TD数据库操作基础类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class TdBaseService {

    /**
     * 基础数据源
     */
    private DruidDataSource baseDataSource = new DruidDataSource();

    /**
     * 高频数据源
     */
    private DruidDataSource highDataSource = new DruidDataSource();

//    /**
//     * 旧数据源
//     */
//    private DruidDataSource oldDataSource = new DruidDataSource();

    /**
     * 初始化连接池
     */
    TdBaseService(@Value("${td.username}") String username, @Value("${td.password}") String password, @Value("${td.url.base}") String urlBase, @Value("${td.url.high}") String urlHigh) {
        setDataSource(baseDataSource, username, password, urlBase, 4);
        setDataSource(highDataSource, username, password, urlHigh, 1);
//        setDataSource(oldDataSource, username, password, urlOld, 1);
    }

    /**
     * 设置数据源
     */
    private void setDataSource(DruidDataSource source, String username, String password, String url, int connectSize) {
        source.setDriverClassName("com.taosdata.jdbc.TSDBDriver");
        source.setUrl("jdbc:TAOS://" + url);

        /* 用户名、密码 */
        source.setUsername(username);
        source.setPassword(password);

        /* 连接数 */
        source.setInitialSize(connectSize);
        source.setMinIdle(connectSize);
        source.setMaxActive(connectSize * 8);

        /* 连接回收 */
        source.setValidationQuery("select 1");
        source.setTestWhileIdle(true);
        source.setTestOnBorrow(true);
        source.setTestOnReturn(false);
        source.setMaxWait(30 * 1000);
    }

    /**
     * 按频率获取连接
     */
    public Connection getConnByRate(String rate) {
        try {
            if (Constant.RATE_HIGH.equals(rate)) {
                return highDataSource.getConnection();
            }
            return baseDataSource.getConnection();
        } catch (SQLException e) {
            log.error("获取连接失败：" + e);
        }
        return null;
    }

    /**
     * 按频率获取连接
     */
    public Connection getOldConn() {
        return null;
    }

    /**
     * 查询多条数据
     */
    public List<Map<String, Object>> oldSelectMulti(String sql) {
        Connection conn = getOldConn();
        try {
            ResultSet rs = conn.createStatement().executeQuery(sql);
            return getMultiMaps(rs);
        } catch (Exception e) {
//            e.printStackTrace();
        } finally {
            try {
                conn.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        return new ArrayList<>();
    }

    /**
     * 查询多条数据
     */
    public List<Map<String, Object>> selectMulti(String sql, String rate) {
        Connection conn = getConnByRate(rate);
        try {
            ResultSet rs = conn.createStatement().executeQuery(sql);
            return getMultiMaps(rs);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                conn.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        return new ArrayList<>();
    }

    /**
     * 查询单条数据
     */
    public Map<String, Object> selectOne(String sql, String rate) {
        Connection conn = getConnByRate(rate);
        try {
            ResultSet rs = conn.createStatement().executeQuery(sql);
            return getOneMap(rs);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                conn.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        return new HashMap<>();
    }

    /**
     * 执行SQL
     */
    public boolean execute(String sql, String rate) throws SQLException {
        Connection conn = getConnByRate(rate);
        try {
            return conn.createStatement().execute(sql);
        } catch (Exception e) {
            throw e;
        } finally {
            try {
                conn.close();
            } catch (SQLException ex) {
                ex.printStackTrace();
            }
        }
    }

    /**
     * 执行SQL
     */
    public void executeSql(String sql, String rate) {
        if ("insert into ".equals(sql)) {
            return;
        }

        Connection conn = getConnByRate(rate);
        try {
            conn.createStatement().execute(sql);
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            try {
                conn.close();
            } catch (SQLException ex) {
                ex.printStackTrace();
            }
        }
    }

    /**
     * 从结果集中封装单条记录
     */
    private Map<String, Object> getOneMap(ResultSet rs) throws SQLException {
        Map<String, Object> map = new HashMap<>();
        if (rs.next()) {
            for (int i = 0; i < rs.getMetaData().getColumnCount(); i++) {
                String colName = rs.getMetaData().getColumnLabel(i + 1);
                Object colVal = rs.getObject(i + 1);
                map.put(colName, colVal);
            }
        }
        return map;
    }

    /**
     * 从结果集中封装多条记录
     */
    private List<Map<String, Object>> getMultiMaps(ResultSet rs) throws SQLException {
        List<Map<String, Object>> maps = new ArrayList<>();
        while (rs.next()) {
            Map<String, Object> map = new HashMap<>();
            for (int i = 0; i < rs.getMetaData().getColumnCount(); i++) {
                String colName = rs.getMetaData().getColumnLabel(i + 1);
                Object colVal = rs.getObject(i + 1);
                map.put(colName, colVal);
            }
            maps.add(map);
        }
        return maps;
    }

    /**
     * 由频率和仪器获取超级表名称
     */
    public String getSTableNameByRateInst(String rate, Integer instId) {
        if (Constant.RATE_HIGH.equals(rate)) {
            return "high_" + instId;
        }
        if (Constant.RATE_MIN.equals(rate)) {
            return "min_" + instId;
        }
        if (Constant.RATE_HOUR.equals(rate)) {
            return "hour_" + instId;
        }
        if (Constant.RATE_SAMPLE.equals(rate)) {
            return "sample_" + instId;
        }
        return null;
    }

    /**
     * 由极坐标属性和仪器方向计算最终方向
     */
    public List<Direct> getFinalDirects(Boolean isPolar, List<Direct> instDirects) {
        if (isPolar) {
            List<Direct> directs = new ArrayList<>(1);
            directs.add(new Direct(0, ""));
            return directs;
        }
        return instDirects;
    }

    /**
     * 由极坐标属性和仪器方向计算最终方向
     */
    public Integer[] getFinalDirects(Boolean isPolar, Integer[] instDirects) {
        if (isPolar) {
            return new Integer[]{0};
        }
        return instDirects;
    }

}
