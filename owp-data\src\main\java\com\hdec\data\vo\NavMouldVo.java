package com.hdec.data.vo;

import com.hdec.data.domain.template.Mould;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class NavMouldVo {

    /** 模板 */
    private List<Mould> moulds;

    /** 是否告警 */
    private boolean isWarning;

    public NavMouldVo(List<Mould> moulds, boolean isWarning) {
        this.moulds = moulds;
        this.isWarning = isWarning;
    }

}
