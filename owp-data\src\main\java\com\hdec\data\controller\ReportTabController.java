package com.hdec.data.controller;

import com.hdec.common.domain.R;
import com.hdec.data.qo.CustomTabQo;
import com.hdec.data.qo.HistoryEigenTabQo;
import com.hdec.data.qo.ProcessLineJsonQo;
import com.hdec.data.qo.WeekStatisticsTabQo;
import com.hdec.data.service.ReportTabService;
import com.hdec.data.vo.ChartNumVo;
import com.hdec.data.vo.TabResVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 报告表格控制器
 *
 * <AUTHOR>
 */
@Api(tags = "报告表格")
@Validated
@Slf4j
@RestController
@RequestMapping("api/data/report/tab")
public class ReportTabController {

    @Autowired
    private ReportTabService tabService;

    /**
     * 自定义表格数据
     */
    @ApiOperation("自定义表格数据")
    @PostMapping("customTabList")
    public R customTabList(@RequestHeader("fieldNum") String fieldNum, @RequestBody CustomTabQo customTab) {
        TabResVo vo = tabService.customTabList(fieldNum, customTab);
        return R.success(vo);
    }

    /**
     * 获取过程线图
     */
    @ApiOperation("获取过程线图")
    @PostMapping("processLineNum")
    public R processLineNum(@RequestHeader("fieldNum") String fieldNum, @RequestBody ProcessLineJsonQo lineJsonQo) {
        List<ChartNumVo> vos = tabService.processLineNum(fieldNum, lineJsonQo);
        return R.success(vos);
    }

    /**
     * 获取频谱图
     */
    @ApiOperation("获取频谱图")
    @PostMapping("freqNum")
    public R freqNum(@RequestHeader("fieldNum") String fieldNum, @RequestBody ProcessLineJsonQo lineJsonQo) {
        List<ChartNumVo> vos = tabService.freqNum(fieldNum, lineJsonQo);
        return R.success(vos);
    }

    /**
     * 历史特征值统计表数据
     */
    @ApiOperation("历史特征值统计表数据")
    @PostMapping("historyEigenTabList")
    public R historyEigenTabList(@RequestHeader("fieldNum") String fieldNum, @RequestBody @Validated HistoryEigenTabQo historyEigenTabQo) {
        TabResVo vo = tabService.historyEigenTabList(fieldNum, historyEigenTabQo);
        return R.success(vo);
    }

    /**
     * 按周统计表
     */
    @ApiOperation("按周统计表")
    @PostMapping("weekStatisticsTabList")
    public R weekStatisticsTabList(@RequestHeader("fieldNum") String fieldNum, @RequestBody WeekStatisticsTabQo weekStatisticsTabQo) {
        TabResVo vo = tabService.weekStatisticsTabList(fieldNum, weekStatisticsTabQo);
        return R.success(vo);
    }

}
