# 2025-04-18 数据导出
create table owp_export_task_v2
(
    id          int auto_increment comment '自增主键'
        primary key,
    point_ids   text         not null comment '测点ID列表',
    rate        varchar(10)  not null comment '频率:高频/分钟/小时',
    start_time  datetime     not null comment '开始时间',
    end_time    datetime     not null comment '结束时间',
    progress    double(5, 2) null default 0 comment '进度(1-100)',
    url         varchar(255) null comment '文件地址',
    status      int          null default 0 comment '状态:0-等待中/1-进行中/2-已完成/-1-失败',
    description text         null comment '描述',
    field_num   char(8)      null comment '风场编码',
    creator     varchar(255) null comment '创建人',
    create_by   int          null comment '创建人',
    create_time datetime     null comment '创建时间'
) comment '数据导出任务';

alter table owp_export_task_v2
    add attr_ids text not null comment '分量ID' after point_ids;

# 2025-05-27 数据导出支持记录开始结束时间
alter table owp_data.owp_export_task_v2
    add export_start_time datetime null comment '导出开始时间' after description;
alter table owp_data.owp_export_task_v2
    add export_end_time datetime null comment '导出结束时间' after export_start_time;
