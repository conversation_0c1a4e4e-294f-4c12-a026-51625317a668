<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hdec.data.mapper.DataStatMapper">

    <sql id="common_sql" >
        `day`, `point_id`, `direct`, `max_value`, `max_value_time`, `min_value`, `min_value_time`, `avg_value`, approval_count_init, approval_count_pass, approval_count_fail
    </sql>

    <update id="createStatTableIfNotExist">
        CREATE TABLE IF NOT EXISTS owp_stat_${fieldNum}_${monitorId}_${attrId} (
          `id` int NOT NULL AUTO_INCREMENT,
          `day` date DEFAULT NULL,
          `point_id` int DEFAULT NULL,
          `direct` int DEFAULT NULL,
          `max_value` DECIMAL(30,12) DEFAULT NULL,
          `max_value_time` datetime DEFAULT NULL,
          `min_value` DECIMAL(30,12) DEFAULT NULL,
          `min_value_time` datetime DEFAULT NULL,
          `avg_value` DECIMAL(30,12) DEFAULT NULL,
          `approval_count_init` int DEFAULT NULL,
          `approval_count_pass` int DEFAULT NULL,
          `approval_count_fail` int DEFAULT NULL,
          PRIMARY KEY (`id`),
          KEY `point_id` (`point_id`) USING BTREE
        ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
    </update>

    <select id="select" resultType="com.hdec.data.domain.Stat">
        SELECT
            *
        FROM
            owp_stat_${qo.fieldNum}_${qo.monitorId}_${qo.attrId}
        <where>
            <if test="qo.startTime != null and qo.startTime != '' and qo.endTime != null and qo.endTime != ''">
                AND `day` BETWEEN #{qo.startTime} AND #{qo.endTime}
            </if>
            <if test="qo.pointIds != null and qo.pointIds.size() > 0">
                AND point_id IN
                <foreach item="pointId" index="index" collection="qo.pointIds" open="(" separator="," close=")">
                    #{pointId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getStatDataByTimePointAttrRate" resultType="com.hdec.data.domain.Stat">
        SELECT
            id, <include refid="common_sql"/>
        FROM
            owp_stat_${fieldNum}_${monitorId}_${attrId}
        <where>
            <if test="startTime != null and startTime != '' and endTime != null and endTime != ''">
                AND `day` BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="pointIds != null and pointIds.size() > 0">
                AND point_id IN
                <foreach item="pointId" index="index" collection="pointIds" open="(" separator="," close=")">
                    #{pointId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getStatFirstLastTime" resultType="com.hdec.data.domain.Stat">
        SELECT
            *
        FROM
            owp_stat_first_last_${fieldNum}_${monitorId}_${attrId}
        <where>
            <if test="startTime != null and startTime != '' and endTime != null and endTime != ''">
                AND `day` BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="pointIds != null and pointIds.size() > 0">
                AND point_id IN
                <foreach item="pointId" index="index" collection="pointIds" open="(" separator="," close=")">
                    #{pointId}
                </foreach>
            </if>
        </where>
        order by `day`
    </select>

    <delete id="deleteByDayPointAttr">
        DELETE
        FROM
            owp_stat_${fieldNum}_${monitorId}_${attrId}
        WHERE
            `day` = #{day}
        AND point_id = #{pointId}
    </delete>

    <delete id="delBatch">
        DELETE
        FROM
            owp_stat_${fieldNum}
        WHERE
            `day` = #{day}
        AND point_id = #{pointId}
        AND direct = #{direct}
        AND attr_id = #{attrId}
    </delete>

    <insert id="save" parameterType="com.hdec.data.domain.Stat">
        INSERT INTO owp_stat_${fieldNum} (<include refid="common_sql"/>)
        VALUES (#{s.day}, #{s.pointId}, #{s.direct}, #{s.attrId}, #{s.maxValue}, #{s.maxValueTime},
        #{s.minValue}, #{s.minValueTime}, #{s.avgValue}, #{s.measureNum})
    </insert>

    <insert id="saveBatch">
        INSERT INTO owp_stat_${fieldNum}_${monitorId}_${attrId} (<include refid="common_sql"/>)
        VALUES
        <foreach collection="statList" separator="," item="s">
            (#{s.day}, #{s.pointId}, #{s.direct}, #{s.maxValue}, #{s.maxValueTime},
            #{s.minValue}, #{s.minValueTime}, #{s.avgValue}, #{s.approvalCountInit}, #{s.approvalCountPass}, #{s.approvalCountFail})
        </foreach>
    </insert>

    <update id="dropStatTableIfExist">
        DROP TABLE IF EXISTS owp_stat_${fieldNum}_${monitorId}_${attrId}
    </update>

    <select id="getNames" resultType="string">
        SHOW TABLES LIKE 'owp_stat_owp%'
    </select>

    <select id="getStatTableNamesByField" resultType="string">
        SHOW TABLES LIKE 'owp_stat_${fieldNum}_%'
    </select>

    <update id="dropTableByName">
        DROP TABLE ${tableName}
    </update>

    <update id="createHighStatTableIfNotExist">
        CREATE TABLE IF NOT EXISTS owp_high_stat_${fieldNum} (
          `id` int NOT NULL AUTO_INCREMENT,
          `point_id` int DEFAULT NULL,
          `day` date DEFAULT NULL,
          `count` int DEFAULT NULL,
          PRIMARY KEY (`id`)
        ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
    </update>

    <select id="getHighStatCount" resultType="int">
        SELECT
            sum( count )
        FROM
            owp_high_stat_${fieldNum}
        WHERE
            point_id IN
            <foreach item="pointId" index="index" collection="pointIds" open="(" separator="," close=")">
                #{pointId}
            </foreach>
            AND `day` BETWEEN #{startTime} AND #{endTime}
    </select>

    <insert id="delHighStatCount">
        DELETE
        FROM
            owp_high_stat_${fieldNum}
        WHERE
            point_id = #{pointId} AND `day` = #{day}
    </insert>

    <insert id="saveHighStatCount">
        INSERT INTO owp_high_stat_${fieldNum} ( point_id, `day`, `count` )
        VALUES
            (#{pointId}, #{day}, #{count})
    </insert>

</mapper>