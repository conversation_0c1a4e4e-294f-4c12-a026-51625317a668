package com.hdec.data.service;

import com.hdec.common.domain.PointCommon;
import com.hdec.data.domain.DiagramVar;
import com.hdec.data.domain.StatVar;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 变量业务逻辑基础类
 *
 * <AUTHOR>
 */
public class VarServiceBase {

    /**
     * 未查询到结果返回
     */
    protected final static String NO_RES_STR = "/";

    /**
     * 查询涉及到的所有测点
     */
    protected List<PointCommon> getRelatedPointIds(StatVar statVar, List<PointCommon> fieldPoints) {
        List<String> seaFacilityIds = statVar.getSeaFacilityIds();
        return fieldPoints.stream().filter(e -> seaFacilityIds.contains(e.getSeaFacilityId())).collect(Collectors.toList());
    }

    /**
     * 查询涉及到的所有测点
     */
    protected List<PointCommon> getRelatedPointIds(DiagramVar statVar, List<PointCommon> fieldPoints) {
        List<String> seaFacilityIds = statVar.getSeaFacilityIds();
        return fieldPoints.stream().filter(e -> seaFacilityIds.contains(e.getSeaFacilityId())).collect(Collectors.toList());
    }

}
