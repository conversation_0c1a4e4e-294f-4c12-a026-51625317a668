package com.hdec.data.service;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.hdec.common.constant.Constant;
import com.hdec.common.constant.RedisKey;
import com.hdec.common.domain.*;
import com.hdec.common.qo.ReportStatQo;
import com.hdec.common.util.*;
import com.hdec.common.vo.Direct;
import com.hdec.common.vo.InstDirectAttr;
import com.hdec.common.vo.ProcessLineVo;
import com.hdec.data.cache.Cache;
import com.hdec.data.config.LocalCache;
import com.hdec.data.domain.*;
import com.hdec.data.domain.domin_word.*;
import com.hdec.data.domain.report.*;
import com.hdec.data.feign.MonitorService;
import com.hdec.data.feign.WindService;
import com.hdec.data.mapper.DataMapper;
import com.hdec.data.qo.*;
import com.hdec.data.util.ExcelUtil;
import com.hdec.data.util.JsoupUtil;
import com.hdec.data.util.WordExportUtil;
import com.hdec.data.vo.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static java.util.stream.Collectors.groupingBy;

/**
 * 数据查询
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@EnableAsync
public class DataService {

    @Autowired
    private DataMapper dataMapper;

    @Autowired
    private TdService tdService;

    @Autowired
    private WindService windService;

    @Autowired
    private MonitorService monitorService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private ReportBaseInfoService infoService;

    @Autowired
    private ReportManageService reportService;

    @Autowired
    private ExcelImportService excelImportService;

    @Autowired
    private TaskService taskService;

    @Value("${templateFile}")
    private String templateFile;

    @Value("${exportFile}")
    private String exportFile;

    /**
     * 访问映射
     */
    @Value("${file.visitMapping}")
    private String visitMapping;

    /**
     * 文件本地路径 - Windows
     */
    @Value("${file.windowPath}")
    private String windowPath;

    /**
     * 文件本地路径 - Linux
     */
    @Value("${file.linuxPath}")
    private String linuxPath;

    @Autowired
    private TdBaseService tdBaseService;

    @Autowired
    private LocalCache localCache;

    private static ObjectMapper objMapper = new ObjectMapper();

    public static DecimalFormat df = new DecimalFormat("#.#");

    /**
     * 数据查询(表)
     */
    public DataTableVo queryTable(DataQueryQo qo) {
        /* 获取仪器、属性等相关信息 */
        InstDirectAttr instInfo = monitorService.getInstAttrs(qo.getPointIds()[0]);
        InstDirectAttr directInfo = monitorService.getInstDirectAttr(qo.getPointIds()[0]);
        if (ObjectUtils.isEmpty(instInfo.getRateAttrs()) || ObjectUtils.isEmpty(instInfo.getRateAttrs().get(qo.getRate()))) {
            return new DataTableVo();
        }
        List<AttrCommon> attrs = instInfo.getRateAttrs().get(qo.getRate());
        if (qo.getAttrIds() != null && qo.getAttrIds().length > 0) {
            attrs = attrs.stream().filter(e -> Arrays.asList(qo.getAttrIds()).contains(String.valueOf(e.getId()))).collect(Collectors.toList());
        }
        /* 查询数据 */
        DataTableVo vo = tdService.selectData(qo.getFieldNum(), instInfo.getInstId(), directInfo.getDirects(), qo.getStartTime(), qo.getEndTime(), qo.getPointIds(), qo.getPointNos(),
                qo.getPageNum(), qo.getPageSize(), qo.getOrderBy(), qo.getOrderRule(), qo.getApproval(), attrs, qo.getRate(), true, qo.getDirect(), null);
        return vo;
    }

    public R queryMinTable(DataQueryMinQo qo) {
        InstDirectAttr instDirectAttr = monitorService.getInstDirectAttr(qo.getPointId());
        if (ObjectUtils.isEmpty(instDirectAttr.getAttrs())) {
            return R.error("选中测点所绑定仪器下没有中间量和成果量");
        }

        String attrName = findAttrName(instDirectAttr.getAttrs(), qo.getAttrId());
        qo.setAttrName(attrName);

        DataTableVo vo = tdService.selectMinData(instDirectAttr.getInstId(), qo.getRate(), qo.getStartTime(), qo.getEndTime(), qo.getPointId(), qo.getPointNo(),
                qo.getPageNum(), qo.getPageSize(), qo.getApproval(), qo.getAttrId(), qo.getAttrName(), qo.getDirect(), qo.getLt(), qo.getConnector(), qo.getGt(), qo.getMin(), qo.getMax());
        return R.success(vo);
    }

    private String findAttrName(List<AttrCommon> attrs, Integer attrId) {
        if (ObjectUtils.isEmpty(attrs) || ObjectUtils.isEmpty(attrId)) {
            return "未知";
        }

        for (AttrCommon attr : attrs) {
            if (attrId.equals(attr.getId())) {
                return attr.getName();
            }
        }
        return "未知";
    }

    /**
     * 导出Excel
     */
    public void exportTable(DataQueryQo qo, HttpServletResponse response) throws Exception {
        InstDirectAttr instInfo = monitorService.getInstAttrs(qo.getPointIds()[0]);
        InstDirectAttr directInfo = monitorService.getInstDirectAttr(qo.getPointIds()[0]);

        DataTableVo vo = tdService.selectData(qo.getFieldNum(), instInfo.getInstId(), directInfo.getDirects(), qo.getStartTime(), qo.getEndTime(), qo.getPointIds(), qo.getPointNos(),
                qo.getPageNum(), qo.getPageSize(), qo.getOrderBy(), qo.getOrderRule(), qo.getApproval(), instInfo.getRateAttrs().get(qo.getRate()), qo.getRate(), false, qo.getDirect(), null);

        List<Map<String, Object>> list = new ArrayList<>();
        TableHead[] heads = vo.getHead();
        String[] keys = new String[heads.length];
        String[] titles = new String[heads.length];
        for (int i = 0; i < heads.length; i++) {
            keys[i] = heads[i].getName();
            titles[i] = heads[i].getShowName();
        }

        List<List<CellVal>> body = vo.getBody();

        if (!ObjectUtils.isEmpty(body)) {
            for (List<CellVal> cells : body) {
                Map<String, Object> oneData = new HashMap<>();
                for (int i = 0; i < keys.length; i++) {
                    oneData.put(keys[i], cells.get(i).getVal());
                }
                list.add(oneData);
            }
        }
        ExcelUtil.createNBig("数据.xlsx", titles, keys, list, response);
    }

    /**
     * 告警过程线
     */
    public List<Datum> alarmProcessLine(ProcessLineVo vo) throws ParseException {
        InstDirectAttr info = monitorService.getInstDirectAttr(vo.getPointId());
        if (ObjectUtils.isEmpty(info.getAttrs())) {
            return Collections.emptyList();
        }

        String attrName = null;
        List<AttrCommon> attrs = info.getAttrs();
        if (!ObjectUtils.isEmpty(attrs)) {
            for (AttrCommon attr : attrs) {
                if (vo.getAttrId().equals(attr.getId())) {
                    attrName = attr.getName();
                }
            }
        }

        if (!ObjectUtils.isEmpty(vo.getTime())) {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            DateTime dateTime = new DateTime(format.parse(vo.getTime()));
            DateTime sTime = dateTime.minusMonths(3);
            DateTime eTime = dateTime.plusMonths(3);
            return tdService.alarmProcessLine(info.getInstId(), vo.getPointId(), vo.getDirection(), vo.getAttrId(), attrName,
                    vo.getTime(), sTime.toString("yyyy-MM-dd HH:mm:ss"), eTime.toString("yyyy-MM-dd HH:mm:ss"), vo.getGroupNum(), vo.getType());
        } else {
            return tdService.alarmProcessLine(info.getInstId(), vo.getPointId(), vo.getDirection(), vo.getAttrId(), attrName,
                    null, vo.getStartTime(), vo.getEndTime(), vo.getGroupNum(), vo.getType());
        }
    }

//    /**
//     * 审核数据
//     */
//    public R verify(VerifyQo qo) {
//        InstDirectAttr instDirectAttr = monitorService.getInstDirectAttr(qo.getPointId());
//        if (ObjectUtils.isEmpty(instDirectAttr.getAttrs())) {
//            return R.error("系统错误");
//        }
//
//        tdService.verify(instDirectAttr.getInstId(), qo);
//        return R.success("审核成功");
//    }

    /**
     * 批量审核数据
     */
    public R verifyBatch(String sessionId, String fieldNum, VerifyQo[] qo, String rate) throws ParseException {
        if (ObjectUtils.isEmpty(qo)) {
            return R.success("审核成功");
        }
        ResourceCommon resource = (ResourceCommon) redisTemplate.opsForValue().get(sessionId);
        InstDirectAttr info = monitorService.getInstDirectAttr(qo[0].getPointId());

        List<Redo> redoList = new ArrayList<>();
        for (VerifyQo veQo : qo) {
            tdService.verify(info.getInstId(), veQo, rate);

            redoList.add(new Redo(TimeUtil.parse2Day(veQo.getTime()), veQo.getPointId(), rate, fieldNum));
        }

        /* 重做统计 */
        excelImportService.redoStat(redoList, "审核", "fast", resource.getUserId(), resource.getUsername(), fieldNum);
        return R.success("审核成功");
    }

    /**
     * 删除数据
     */
    public R del(String sessionId, String fieldNum, List<VerifyQo> qos, Integer direct) {
//        if (!ObjectUtils.isEmpty(qos) && "高频".equals(qos.get(0).getRate())) {
//            return R.error("高频数据暂无支持删除，请等待后续版本");
//        }
        /* 获取用户信息 */
        ResourceCommon resource = (ResourceCommon) redisTemplate.opsForValue().get(sessionId);
        if (resource == null) {
            return R.error("系统错误");
        }

        InstDirectAttr info = monitorService.getInstDirectAttr(qos.get(0).getPointId());
        tdService.delBatch(fieldNum, info.getInstId(), info.getAttrs(), direct, qos, resource.getUserId(), resource.getUsername());

        new Thread(() -> {
            List<Redo> redoList = new ArrayList<>();
            String rate = null;
            for (VerifyQo veQo : qos) {
                rate = veQo.getRate();
                redoList.add(new Redo(TimeUtil.parse2Day(veQo.getTime()), veQo.getPointId(), rate, fieldNum));
            }
            /* 重做统计 */
            excelImportService.redoStat(redoList, "删除", "fast", resource.getUserId(), resource.getUsername(), fieldNum);
        }).start();
        return R.success("删除成功");
    }

    /**
     * 当前项目批量删除任务
     */
    public BatchDelQo getCurBatchDelTask(String fieldNum) {
        String key = RedisKey.BATCH_DEL + fieldNum;
        Object o = redisTemplate.opsForValue().get(key);
        if (o == null) {
            return null;
        }

        return null;
    }

    /**
     * 批量删除
     */
    @Async
    public void batchDelNew(String sessionId, String fieldNum, BatchDelQo qo) throws InterruptedException {
        /* 获取用户信息 */
        ResourceCommon resource = (ResourceCommon) redisTemplate.opsForValue().get(sessionId);
        if (resource == null) {
            return;
        }

        // 重置进度
        String key = RedisKey.BATCH_DEL + fieldNum;
        qo.setProcess(0);
        redisTemplate.opsForValue().set(key, JSON.toJSONString(qo));

        InstDirectAttr info = monitorService.getInstDirectAttr(qo.getPointIds()[0]);
        tdService.del(info.getInstId(), qo.getPointIds(), qo.getStartTime(), qo.getEndTime(), qo.getRate());
        log.info("删除完毕");

        /* 重做统计 */
        List<Redo> redoList = new ArrayList<>();
        List<String> days = TimeUtil.days(qo.getStartTime().substring(0, 10), qo.getEndTime().substring(0, 10));
        for (String day : days) {
            for (Integer pointId : qo.getPointIds()) {
                redoList.add(new Redo(TimeUtil.parse2Day(day), pointId, qo.getRate(), fieldNum));
            }
        }
        excelImportService.redoStat(redoList, "批量删除", "fast", resource.getUserId(), resource.getUsername(), fieldNum);

        redisTemplate.delete(key);
    }

    /**
     * 批量删除
     */
    @Async
    public void batchDel(String sessionId, String fieldNum, BatchDelQo qo) throws InterruptedException {
        /* 获取用户信息 */
        ResourceCommon resource = (ResourceCommon) redisTemplate.opsForValue().get(sessionId);
        if (resource == null) {
            return;
        }

        String key = RedisKey.BATCH_DEL + fieldNum;
        qo.setProcess(0);
        redisTemplate.opsForValue().set(key, JSON.toJSONString(qo));

        InstDirectAttr info = monitorService.getInstDirectAttr(qo.getPointIds()[0]);

        /* 查询该段时间范围内所有数据 */
        List<Map<String, Object>> records = tdService.getByTimeBetween(info.getInstId(), qo.getPointIds(), qo.getStartTime(), qo.getEndTime(), qo.getRate());
        log.info("待删除所有数据数量：{}", records.size());
        if (ObjectUtils.isEmpty(records)) {
            return;
        }

        List<VerifyQo> verifyQos = new ArrayList<>(records.size());
        for (Map<String, Object> record : records) {
            Integer pointId = (Integer) record.get("point");
            String ts = TimeUtil.format2Ms((Date) record.get("ts"));
            verifyQos.add(new VerifyQo(qo.getRate(), pointId, ts));
        }

        List<List<VerifyQo>> parts = Lists.partition(verifyQos, 10);
        log.info("待删除数据被分成了{}份", parts.size());

        int i = 0;
        for (List<VerifyQo> part : parts) {
            tdService.delBatch(fieldNum, info.getInstId(), info.getAttrs(), -1, part, resource.getUserId(), resource.getUsername());

            qo.setProcess((int) Math.floor(++i * 100.0 / parts.size()));
            if (qo.getProcess() >= 100) {
                qo.setProcess(99);
            }
            redisTemplate.opsForValue().set(key, JSON.toJSONString(qo));
            log.info("正在删除第{}份", i);
            TimeUnit.SECONDS.sleep(10);
        }

        /* 重做统计 */
        List<Redo> redoList = new ArrayList<>();
        String rate;
        for (VerifyQo veQo : verifyQos) {
            rate = veQo.getRate();
            redoList.add(new Redo(TimeUtil.parse2Day(veQo.getTime()), veQo.getPointId(), rate, fieldNum));
        }
        excelImportService.redoStat(redoList, "批量删除", "fast", resource.getUserId(), resource.getUsername(), fieldNum);

        redisTemplate.delete(key);
    }

    /**
     * 编辑数据
     */
    public R update(String sessionId, String fieldNum, VerifyQo qo, HttpServletRequest request) throws Exception {
        InstDirectAttr instDirectAttr = monitorService.getInstDirectAttr(qo.getPointId());
        if (ObjectUtils.isEmpty(instDirectAttr.getAttrs())) {
            return R.error("系统错误");
        }

        /* 获取用户信息 */
        ResourceCommon resource = (ResourceCommon) redisTemplate.opsForValue().get(sessionId);
        if (resource == null) {
            return R.error("系统错误");
        }

        tdService.update(fieldNum, instDirectAttr.getInstId(), qo, resource.getUserId(), resource.getUsername());

        List<Redo> redoList = new ArrayList<>();
        redoList.add(new Redo(TimeUtil.parse2Day(qo.getTime()), qo.getPointId(), qo.getRate(), fieldNum));
        /* 重做统计 */
        excelImportService.redoStat(redoList, "编辑", "fast", resource.getUserId(), resource.getUsername(), fieldNum);
        return R.success("编辑成功");
    }

    /**
     * 还原数据
     */
    public R returnBack(String sessionId, String fieldNum, VerifyQo qo, HttpServletRequest request) throws Exception {
        InstDirectAttr instDirectAttr = monitorService.getInstDirectAttr(qo.getPointId());
        if (ObjectUtils.isEmpty(instDirectAttr.getAttrs())) {
            return R.error("系统错误");
        }

        /* 获取用户信息 */
        ResourceCommon resource = (ResourceCommon) redisTemplate.opsForValue().get(sessionId);
        if (resource == null) {
            return R.error("系统错误");
        }

        tdService.returnBack(instDirectAttr.getInstId(), qo, resource.getUserId(), resource.getUsername(), fieldNum);

        List<Redo> redoList = new ArrayList<>();
        redoList.add(new Redo(TimeUtil.parse2Day(qo.getTime()), qo.getPointId(), qo.getRate(), fieldNum));
        /* 重做统计 */
        excelImportService.redoStat(redoList, "还原", "fast", resource.getUserId(), resource.getUsername(), fieldNum);
        return R.success("还原成功");
    }

    /**
     * 数据查询(图)
     */
    public List<DataChartVo> queryChart(Integer[] pointIds, String[] pointNos,
                                        String[] attrIds, String[] attrNames, String startTime, String endTime, Integer[] approval,
                                        Float min, Float max, String rate) throws Exception {
        if (ObjectUtils.isEmpty(pointIds)) {
            return null;
        }
        InstDirectAttr instDirectAttr = monitorService.getInstDirectAttr(pointIds[0]);
        if (ObjectUtils.isEmpty(instDirectAttr.getAttrs())) {
            return null;
        }

        List<DataChartVo> dataChartVos = tdService.queryChart(instDirectAttr.getInstId(), pointIds, pointNos,
                attrIds, attrNames, startTime, endTime, approval, min, max, rate);
        return dataChartVos;
    }

    /**
     * 由测点ID获取测点绑定仪器的ID、方向、属性
     */
    public InstDirectAttr getInstDirectAttr(Integer pointId, String rate) {
        InstDirectAttr info = monitorService.getInstDirectAttrByRate(pointId, rate);
        return info;
    }

    /**
     * 获取测点最新成果量数据
     */
    public R pointLastAchieveData(Integer pointId) {
        InstDirectAttr instInfo = monitorService.getInstDirectAttr(pointId);
        List<AttrCommon> attrs = filterAchieve(instInfo.getAttrs());
        if (ObjectUtils.isEmpty(attrs)) {
            return R.error("选中测点所绑定仪器下没有成果量");
        }

        List<LastDataVo> lastDataVos = null;
        try {
            lastDataVos = tdService.pointLastAchieveData3(instInfo.getInstId(), instInfo.getDirects(), attrs, pointId);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return R.success(lastDataVos);
    }

    /**
     * 过滤成果量
     */
    private List<AttrCommon> filterAchieve(List<AttrCommon> attrs) {
        if (ObjectUtils.isEmpty(attrs)) {
            return Collections.emptyList();
        }
        return attrs.stream().filter(e -> "成果量".equals(e.getType()) && !e.getIsPolar()).collect(Collectors.toList());
    }

    /**
     * 创建超级表
     */
    public void createOrModifySTable(String fieldNum, Integer instId, String instDirect, List<AttrCommon> attrs) {
        tdService.createOrModifySTable(fieldNum, instId, instDirect, attrs);
    }

    /**
     * 生成某风场某月的报告
     */
    @Async
    public void genReport(Integer reportId, ReportStatQo qo) throws Exception {
        int picIndex = 1001;

        ReportInfo reportInfo = new ReportInfo();

        /* 设置报告名称 */
        try {
            configReportName(reportInfo, qo.getFieldNum());
        } catch (Exception e) {
            e.printStackTrace();
        }

        /* 设置报告时间 */
        try {
            configReportTime(reportInfo, qo.getStartTime(), qo.getEndTime());
        } catch (Exception e) {
            e.printStackTrace();
        }

        /* 设置报告编写时间 */
        try {
            configReportWriteTime(reportInfo, qo.getWriteTime());
        } catch (Exception e) {
            e.printStackTrace();
        }

        /* 设置报告年份和期数 */
        try {
            configReportPhase(reportInfo, qo.getIssueYear(), qo.getIssueNum(), qo.getStartTime());
        } catch (Exception e) {
            e.printStackTrace();
        }

        /* 设置报告编校审人员 */
        try {
            configReportPerson(reportInfo, qo.getFieldNum());
        } catch (Exception e) {
            e.printStackTrace();
        }

        /* 设置概览 */
        try {
            configOverviews(reportInfo, qo.getFieldNum());
        } catch (Exception e) {
            e.printStackTrace();
        }

        // 更新进度
        updateProcess(reportId, 1, 5);

        /* 获取该风场名称、所有仪器、测点、海上设施、属性 */
        Map<String, List<StatItem>> tiltStatItemsMap;
        Map<String, List<StatItem>> shockStatItemsMap;
        Map<String, List<StatItem>> shockBoosterStatItemsMap;
        Map<String, List<StatItem>> stressStatItemsMap;
        Map<String, List<StatItem>> corrodeStatItemsMap;
        Map<String, List<StatItem>> subsideStatItemsMap;
        Map<String, List<ReportPic>> picsMap;
        Map<Integer, PointCommon> pointMap;
        List<SeaFacilityCommon> fieldSeaFacilities;
        List<SeaStatItem> seaTiltStatItems;
        List<SeaStatItem> seaShockStatItems;
        List<StatItem> tiltStatItems;
        List<StatItem> shockStatItems;
        List<StatItem> stressStatItems;
        List<ReportPic> pics = new ArrayList<>();

        try {
            List<InstCommon> fieldInsts = monitorService.allInstByField(qo.getFieldNum());
            List<PointCommon> fieldPoints = monitorService.getPointsByFieldNum(qo.getFieldNum());
            Map<String, List<PointCommon>> seaPointMap = toSeaPointMap(fieldPoints);
            pointMap = toPointMap(fieldPoints);
            fieldSeaFacilities = windService.allSeaFacility(qo.getFieldNum());
            List<AttrCommon> fieldAttrs = monitorService.allAttrByField(qo.getFieldNum());

            // 更新进度
            updateProcess(reportId, 6, 10);

            /* 获取倾斜仪器ID */
            tiltStatItems = getTiltInfo(fieldInsts, seaPointMap, pointMap, fieldSeaFacilities, fieldAttrs,
                    qo.getStartTime(), qo.getEndTime(), pics, "倾斜", "倾角", null);

            // 更新进度
            updateProcess(reportId, 11, 20);

            shockStatItems = getTiltInfo(fieldInsts, seaPointMap, pointMap, fieldSeaFacilities, fieldAttrs,
                    qo.getStartTime(), qo.getEndTime(), pics, "振动", "有效值", "双向");
            // 更新进度
            updateProcess(reportId, 21, 30);

            List<StatItem> shockBoosterStatItems = getTiltInfo(fieldInsts, seaPointMap, pointMap, fieldSeaFacilities, fieldAttrs,
                    qo.getStartTime(), qo.getEndTime(), pics, "振动", "有效值", "三向");
            // 更新进度
            updateProcess(reportId, 31, 40);

            stressStatItems = getTiltInfo(fieldInsts, seaPointMap, pointMap, fieldSeaFacilities, fieldAttrs,
                    qo.getStartTime(), qo.getEndTime(), pics, "钢板应力", "应力", null);
            // 更新进度
            updateProcess(reportId, 41, 50);

            List<StatItem> corrodeStatItems = getTiltInfo(fieldInsts, seaPointMap, pointMap, fieldSeaFacilities, fieldAttrs,
                    qo.getStartTime(), qo.getEndTime(), pics, "腐蚀", "电位", null);
            // 更新进度
            updateProcess(reportId, 51, 60);

            List<StatItem> subsideStatItems = getTiltInfo(fieldInsts, seaPointMap, pointMap, fieldSeaFacilities, fieldAttrs,
                    qo.getStartTime(), qo.getEndTime(), pics, "静力水准", "相对沉降", null);
            // 更新进度
            updateProcess(reportId, 61, 70);

            seaTiltStatItems = getSeaTiltInfo(fieldInsts, seaPointMap, pointMap, fieldSeaFacilities, fieldAttrs,
                    qo.getStartTime(), qo.getEndTime(), pics, "倾斜", "倾角", null);
            // 更新进度
            updateProcess(reportId, 71, 80);

            seaShockStatItems = getSeaTiltInfo(fieldInsts, seaPointMap, pointMap, fieldSeaFacilities, fieldAttrs,
                    qo.getStartTime(), qo.getEndTime(), pics, "振动", "有效值", "三向");
            // 更新进度
            updateProcess(reportId, 81, 90);

            tiltStatItemsMap = tiltStatItems.stream().collect(groupingBy(StatItem::getSeaFacilityId));
            shockStatItemsMap = shockStatItems.stream().collect(groupingBy(StatItem::getSeaFacilityId));
            shockBoosterStatItemsMap = shockBoosterStatItems.stream().collect(groupingBy(StatItem::getSeaFacilityId));
            stressStatItemsMap = stressStatItems.stream().collect(groupingBy(StatItem::getSeaFacilityId));
            corrodeStatItemsMap = corrodeStatItems.stream().collect(groupingBy(StatItem::getSeaFacilityId));
            subsideStatItemsMap = subsideStatItems.stream().collect(groupingBy(StatItem::getSeaFacilityId));
            picsMap = pics.stream().collect(groupingBy(ReportPic::getSeaFacilityId));
        } catch (Exception e) {
            e.printStackTrace();
            tiltStatItemsMap = new HashMap<>(0);
            shockStatItemsMap = new HashMap<>(0);
            shockBoosterStatItemsMap = new HashMap<>(0);
            stressStatItemsMap = new HashMap<>(0);
            corrodeStatItemsMap = new HashMap<>(0);
            subsideStatItemsMap = new HashMap<>(0);
            picsMap = new HashMap<>(0);
            pointMap = new HashMap<>(0);
            fieldSeaFacilities = new ArrayList<>(0);
            seaTiltStatItems = new ArrayList<>(0);
            seaShockStatItems = new ArrayList<>(0);
            tiltStatItems = new ArrayList<>(0);
            shockStatItems = new ArrayList<>(0);
            stressStatItems = new ArrayList<>(0);
        }

        /* 升压站 */
        ReportBoosterInfo reportBoosterInfo = null;
        try {
            reportBoosterInfo = getReportBoosterInfo(fieldSeaFacilities, pointMap, tiltStatItemsMap, shockBoosterStatItemsMap, stressStatItemsMap, corrodeStatItemsMap, subsideStatItemsMap, picsMap);
        } catch (Exception e) {
            e.printStackTrace();
        }

        /* 结论 */
        ReportSummaryInfo typicalSummary = null;
        ReportSummaryInfo atypicalSummary = null;
        try {
            typicalSummary = getReportTypicalSummaryInfo(fieldSeaFacilities, tiltStatItems, shockStatItems, stressStatItems, true);
            atypicalSummary = getReportTypicalSummaryInfo(fieldSeaFacilities, tiltStatItems, shockStatItems, stressStatItems, false);
        } catch (Exception e) {
            e.printStackTrace();
        }

        /* 典型风机 */
        List<ReportTypicalFanInfo> typicalFanInfos = new ArrayList<>();
        List<ReportTypicalFanInfo> speFanInfos = new ArrayList<>();
        try {
            List<SeaFacilityCommon> typicalFans = fieldSeaFacilities.stream().filter(e -> e.getId().startsWith("1-")).filter(e -> e.getIsTypical() != null).filter(e -> e.getIsTypical()).collect(Collectors.toList());
            for (SeaFacilityCommon typicalFan : typicalFans) {
                ReportTypicalFanInfo typicalFanInfo = new ReportTypicalFanInfo(typicalFan.getName());
                // 倾斜
                fillTilt(typicalFan, typicalFanInfo, tiltStatItemsMap);
                // 振动
                fillShock(typicalFan, typicalFanInfo, shockStatItemsMap);
                // 钢板应力
                fillStress(typicalFan, typicalFanInfo, stressStatItemsMap, pointMap, picsMap);
                // 腐蚀
                fillCorrode(typicalFan, typicalFanInfo, corrodeStatItemsMap, pointMap, picsMap);
                typicalFanInfo.setPicIndex(picIndex++);
                typicalFanInfos.add(typicalFanInfo);
            }
            reportInfo.setTypicalFanInfos(typicalFanInfos);

            /* 非典型风机 - 只限倾斜和振动 */
            List<SeaFacilityCommon> speFans = fieldSeaFacilities.stream()
                    .filter(e -> e.getId().startsWith("1-"))
                    .filter(e -> e.getIsTypical() == null || !e.getIsTypical()).collect(Collectors.toList());

            for (SeaFacilityCommon typicalFan : speFans) {
                ReportTypicalFanInfo typicalFanInfo = new ReportTypicalFanInfo(typicalFan.getName());
                // 倾斜
                fillTilt(typicalFan, typicalFanInfo, tiltStatItemsMap);
                // 振动
                fillShock(typicalFan, typicalFanInfo, shockStatItemsMap);
                speFanInfos.add(typicalFanInfo);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        List<ReportTypicalFanInfo> allList = new ArrayList<>();
        try {
            allList.addAll(typicalFanInfos);
            allList.addAll(speFanInfos);

            /* 排序 */
            allList.sort((f1, f2) -> {
                Integer no1 = CommonUtil.extractUniqueNum(f1.getNo());
                Integer no2 = CommonUtil.extractUniqueNum(f2.getNo());
                if (no1 == null && no2 == null) {
                    return 0;
                }
                if (no1 == null) {
                    return 1;
                }
                if (no2 == null) {
                    return -1;
                }
                return no1 - no2;
            });
        } catch (Exception e) {
            e.printStackTrace();
        }

        /* 非典型风机 */
        ReportTypicalFanInfo nonTypicalFanInfo = new ReportTypicalFanInfo();
        List<ReportNonTypicalFanInfo> nonTypicalFanInfos = new ArrayList<>();
        try {
            List<SeaFacilityCommon> nonTypicalFans = fieldSeaFacilities.stream().filter(e -> e.getId().startsWith("1-")).filter(e -> (e.getIsTypical() == null || !e.getIsTypical())).collect(Collectors.toList());
            // 倾斜
            fillNonTypicalTilt(nonTypicalFans, nonTypicalFanInfo, tiltStatItemsMap);
            // 振动
            fillNonTypicalShock(nonTypicalFans, nonTypicalFanInfo, shockStatItemsMap);
            // 钢板应力
            fillNonTypicalStress(nonTypicalFans, nonTypicalFanInfo, stressStatItemsMap, pointMap);
            // 腐蚀
            for (SeaFacilityCommon nonTypicalFan : nonTypicalFans) {
                fillNonTypicalCorrode(nonTypicalFan, nonTypicalFanInfos, corrodeStatItemsMap, pointMap, picsMap);
            }
            reportInfo.setNonTypicalFanInfo(nonTypicalFanInfo);
            reportInfo.setNonTypicalFanInfos(nonTypicalFanInfos);
        } catch (Exception e) {
            e.printStackTrace();
        }

        List<ReportPic> tiltPics = null;
        List<ReportPic> seaTiltPics = null;
        List<ReportPic> shockPics = null;
        List<ReportPic> seaShockPics = null;
        try {
            tiltPics = pics.stream().filter(e -> "倾斜".equals(e.getType()) && e.getSeaFacilityId().startsWith("1-")).collect(Collectors.toList());
            seaTiltPics = pics.stream().filter(e -> "倾斜".equals(e.getType()) && e.getSeaFacilityId().startsWith("2-")).collect(Collectors.toList());
            shockPics = pics.stream().filter(e -> "振动".equals(e.getType()) && e.getSeaFacilityId().startsWith("1-")).collect(Collectors.toList());
            seaShockPics = pics.stream().filter(e -> "振动".equals(e.getType()) && e.getSeaFacilityId().startsWith("2-")).collect(Collectors.toList());
        } catch (Exception e) {
            e.printStackTrace();
        }

        // 更新进度
        updateProcess(reportId, 91, 95);

        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("fanType", qo.getTypicalType());
        dataMap.put("aType", qo.getAtypicalType());
        dataMap.put("info", reportInfo);
        dataMap.put("fans", typicalFanInfos);
        dataMap.put("allList", allList);
        dataMap.put("aFan", nonTypicalFanInfo);
        dataMap.put("aFans", nonTypicalFanInfos);
        dataMap.put("booster", reportBoosterInfo);

        /* 结论 */
        dataMap.put("typical", typicalSummary);
        dataMap.put("atypical", atypicalSummary);

        /* 图片 */
        dataMap.put("tiltPics", tiltPics);
        dataMap.put("tiltPics", tiltPics);
        dataMap.put("seaTiltPics", seaTiltPics);
        dataMap.put("shockPics", shockPics);
        dataMap.put("seaShockPics", seaShockPics);

        dataMap.put("seaTiltStatItems", seaTiltStatItems);
        dataMap.put("seaShockStatItems", seaShockStatItems);

        String uuid = CommonUtil.uuid();
        String outputDirPre = "/var/owp-upload-files/reports/" + uuid + "/";
        makeExist(outputDirPre);
        String pre = qo.getIssueYear() + "第" + qo.getIssueNum() + "期(总第" + qo.getIssueNum() + "期)月报";
        String outputDirSuf = pre + ".doc";
        String pdfOutputDirSuf = pre + ".pdf";
        WordExportUtil.createDocFile(templateFile, dataMap, outputDirPre + outputDirSuf);

        // 更新报告状态
        reportService.updatePath(reportId, "已完成", "owp-files/reports/" + uuid + "/" + outputDirSuf, "owp-files/reports/" + uuid + "/" + pdfOutputDirSuf);
    }

    /**
     * 更新进度
     */
    private void updateProcess(Integer reportId, int min, int max) {
        try {
            reportService.updateStatus(reportId, "生成中(" + CommonUtil.randomInt(min, max) + "%)");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void makeExist(String outputDirPre) {
        File f = new File(outputDirPre);
        if (!f.exists()) {
            f.mkdirs();
        }
    }

    /**
     * 设置概览
     */
    private void configOverviews(ReportInfo reportInfo, String fieldNum) {
        ReportBaseInfo baseInfo = infoService.getBaseInfo(fieldNum);
        if (baseInfo == null) {
            return;
        }

        List<Map<String, List<Map<String, Object>>>> projectOverviews = JsoupUtil.getRichText(baseInfo.getProjectOverview());
        List<Map<String, List<Map<String, Object>>>> monitorOverviews = JsoupUtil.getRichText(baseInfo.getMonitorOverview());
        reportInfo.setProjectOverviews(projectOverviews);
        reportInfo.setMonitorOverviews(monitorOverviews);
    }

    /**
     * 设置报告编校审人员
     */
    private void configReportPerson(ReportInfo reportInfo, String fieldNum) {
        ReportPerson person = infoService.getPersonConf(fieldNum);
        if (person != null) {
            reportInfo.setEditor(person.getEditor());
            reportInfo.setChecker(person.getChecker());
            reportInfo.setReviewer(person.getReviewer());
            reportInfo.setCompany(person.getCompany());
            reportInfo.setDept(person.getDept());
        }
    }

    /**
     * 获取典型风机总结信息
     */
    private ReportSummaryInfo getReportTypicalSummaryInfo(List<SeaFacilityCommon> seaFacilities, List<StatItem> tiltStatItems,
                                                          List<StatItem> shockStatItems, List<StatItem> stressStatItems,
                                                          boolean isTypical) {
        ReportSummaryInfo summaryInfo = new ReportSummaryInfo();

        // 获取目标风机的ID
        List<SeaFacilityCommon> fans = seaFacilities.stream()
                .filter(e -> e.getId().startsWith("1-"))
                .filter(e -> (isTypical ? e.getIsTypical() != null && e.getIsTypical() : e.getIsTypical() == null || !e.getIsTypical())).collect(Collectors.toList());

        if (ObjectUtils.isEmpty(fans)) {
            return summaryInfo;
        }
        List<String> fanIds = fans.stream().map(SeaFacilityCommon::getId).collect(Collectors.toList());
        System.out.println("fanIds:" + fanIds);

        /* 倾斜 */
        List<StatItem> tiltBaseMinItems = tiltStatItems.stream().filter(e -> fanIds.contains(e.getSeaFacilityId()) && e.getMin() != null && "基础顶".equals(e.getPosition())).collect(Collectors.toList());
        System.out.println("tiltBaseMinItems:" + tiltBaseMinItems);
        if (tiltBaseMinItems.size() > 0) {
            summaryInfo.setTiltBaseMin(tiltBaseMinItems.stream().mapToDouble(StatItem::getMin).min().orElse(0));
        }
        List<StatItem> tiltBaseMaxItems = tiltStatItems.stream().filter(e -> fanIds.contains(e.getSeaFacilityId()) && e.getMax() != null && "基础顶".equals(e.getPosition())).collect(Collectors.toList());
        System.out.println("tiltBaseMaxItems:" + tiltBaseMaxItems);
        if (tiltBaseMaxItems.size() != 0) {
            summaryInfo.setTiltBaseMax(tiltBaseMaxItems.stream().mapToDouble(StatItem::getMax).max().orElse(0));
        }
        List<StatItem> tiltBaseAvgItems = tiltStatItems.stream().filter(e -> fanIds.contains(e.getSeaFacilityId()) && e.getAvg() != null && "基础顶".equals(e.getPosition())).collect(Collectors.toList());
        System.out.println("tiltBaseAvgItems:" + tiltBaseAvgItems);
        if (tiltBaseAvgItems.size() != 0) {
            summaryInfo.setTiltBaseAvg(tiltBaseAvgItems.stream().mapToDouble(StatItem::getAvg).average().orElse(0));
        }

        List<StatItem> tiltTopMinItems = tiltStatItems.stream().filter(e -> fanIds.contains(e.getSeaFacilityId()) && e.getMin() != null && "塔筒顶".equals(e.getPosition())).collect(Collectors.toList());
        if (tiltTopMinItems.size() > 0) {
            summaryInfo.setTiltTopMin(tiltTopMinItems.stream().mapToDouble(StatItem::getMin).min().orElse(0));
        }
        List<StatItem> tiltTopMaxItems = tiltStatItems.stream().filter(e -> fanIds.contains(e.getSeaFacilityId()) && e.getMax() != null && "塔筒顶".equals(e.getPosition())).collect(Collectors.toList());
        if (tiltTopMaxItems.size() != 0) {
            summaryInfo.setTiltTopMax(tiltTopMaxItems.stream().mapToDouble(StatItem::getMax).max().orElse(0));
        }
        List<StatItem> tiltTopAvgItems = tiltStatItems.stream().filter(e -> fanIds.contains(e.getSeaFacilityId()) && e.getAvg() != null && "塔筒顶".equals(e.getPosition())).collect(Collectors.toList());
        if (tiltTopAvgItems.size() != 0) {
            summaryInfo.setTiltTopAvg(tiltTopAvgItems.stream().mapToDouble(StatItem::getAvg).average().orElse(0));
        }

        /* 振动 */
        List<StatItem> shockBaseMinItems = shockStatItems.stream().filter(e -> fanIds.contains(e.getSeaFacilityId()) && e.getMin() != null && "基础顶".equals(e.getPosition())).collect(Collectors.toList());
        if (shockBaseMinItems.size() > 0) {
            summaryInfo.setShockBaseMin(shockBaseMinItems.stream().mapToDouble(StatItem::getMin).min().orElse(0));
        }
        List<StatItem> shockBaseMaxItems = shockStatItems.stream().filter(e -> fanIds.contains(e.getSeaFacilityId()) && e.getMax() != null && "基础顶".equals(e.getPosition())).collect(Collectors.toList());
        if (shockBaseMaxItems.size() != 0) {
            summaryInfo.setShockBaseMax(shockBaseMaxItems.stream().mapToDouble(StatItem::getMax).max().orElse(0));
        }

        List<StatItem> shockTopMinItems = shockStatItems.stream().filter(e -> fanIds.contains(e.getSeaFacilityId()) && e.getMin() != null && "塔筒顶".equals(e.getPosition())).collect(Collectors.toList());
        if (shockTopMinItems.size() > 0) {
            summaryInfo.setShockTopMin(shockTopMinItems.stream().mapToDouble(StatItem::getMin).min().orElse(0));
        }
        List<StatItem> shockTopMaxItems = shockStatItems.stream().filter(e -> fanIds.contains(e.getSeaFacilityId()) && e.getMax() != null && "塔筒顶".equals(e.getPosition())).collect(Collectors.toList());
        if (shockTopMaxItems.size() != 0) {
            summaryInfo.setShockTopMax(shockTopMaxItems.stream().mapToDouble(StatItem::getMax).max().orElse(0));
        }

        /* 应力 */
        List<StatItem> stressBaseMinItems = stressStatItems.stream().filter(e -> fanIds.contains(e.getSeaFacilityId()) && e.getMin() != null && "基础顶".equals(e.getPosition())).collect(Collectors.toList());
        if (stressBaseMinItems.size() > 0) {
            summaryInfo.setStressMin(stressBaseMinItems.stream().mapToDouble(StatItem::getMin).min().orElse(0));
        }
        List<StatItem> stressBaseMaxItems = stressStatItems.stream().filter(e -> fanIds.contains(e.getSeaFacilityId()) && e.getMax() != null && "基础顶".equals(e.getPosition())).collect(Collectors.toList());
        if (stressBaseMaxItems.size() != 0) {
            summaryInfo.setStressMax(stressBaseMaxItems.stream().mapToDouble(StatItem::getMax).max().orElse(0));
        }
        return summaryInfo;
    }

    /**
     * 获取升压站报告信息
     */
    private ReportBoosterInfo getReportBoosterInfo(List<SeaFacilityCommon> seaFacilities, Map<Integer, PointCommon> pointMap,
                                                   Map<String, List<StatItem>> tiltStatItemsMap, Map<String, List<StatItem>> shockStatItemsMap,
                                                   Map<String, List<StatItem>> stressStatItemsMap, Map<String, List<StatItem>> corrodeStatItemsMap,
                                                   Map<String, List<StatItem>> subsideStatItemsMap, Map<String, List<ReportPic>> picsMap) {
        ReportBoosterInfo boosterInfo = new ReportBoosterInfo();

        /* 获取升压站 */
        SeaFacilityCommon booster = filterBooster(seaFacilities);
        if (booster == null) {
            return boosterInfo;
        }

        /* 倾斜 */
        List<StatItem> tiltStatItems = tiltStatItemsMap.get(booster.getId());
        if (!ObjectUtils.isEmpty(tiltStatItems)) {
            Double min = null;
            long tiltStatItemsCount = tiltStatItems.stream().filter(e -> e.getMin() != null).count();
            if (tiltStatItemsCount != 0) {
                min = tiltStatItems.stream().filter(e -> e.getMin() != null).mapToDouble(StatItem::getMin).min().orElse(0);
            }
            Double max = null;
            if (tiltStatItemsCount != 0) {
                max = tiltStatItems.stream().filter(e -> e.getMax() != null).mapToDouble(StatItem::getMax).max().orElse(0);
            }
            Double avg = null;
            if (tiltStatItemsCount != 0) {
                avg = tiltStatItems.stream().filter(e -> e.getAvg() != null).mapToDouble(StatItem::getAvg).average().orElse(0);
            }
            boosterInfo.setTiltMin(min);
            boosterInfo.setTiltMax(max);
            boosterInfo.setTiltAvg(avg);
        }

        /* 沉降 */
        List<StatItem> subsideStatItems = subsideStatItemsMap.get(booster.getId());
        if (!ObjectUtils.isEmpty(subsideStatItems)) {
            List<StatItem> settles = subsideStatItems.stream().filter(e -> e.getPointId() != null).collect(Collectors.toList());
            if (settles.size() >= 2) {
                StatItem first = settles.get(0);
                List<StatItem> statItems = settles.subList(1, settles.size());

                /* 最小值、最大值 */
                Double min = null;
                long count = statItems.stream().filter(e -> e.getMin() != null).count();
                if (count != 0) {
                    min = statItems.stream().filter(e -> e.getMin() != null).mapToDouble(StatItem::getMin).min().orElse(0);
                }
                Double max = null;
                if (count != 0) {
                    max = statItems.stream().filter(e -> e.getMax() != null).mapToDouble(StatItem::getMax).max().orElse(0);
                }
                boosterInfo.setSettleMin(min);
                boosterInfo.setSettleMax(max);

                /* 平均值的最小值、最大值 */
                Double avgMin = null;
                count = statItems.stream().filter(e -> e.getAvg() != null).count();
                if (count != 0) {
                    avgMin = statItems.stream().filter(e -> e.getAvg() != null).mapToDouble(StatItem::getAvg).min().orElse(0);
                }
                Double avgMax = null;
                if (count != 0) {
                    avgMax = statItems.stream().filter(e -> e.getAvg() != null).mapToDouble(StatItem::getAvg).max().orElse(0);
                }
                boosterInfo.setSettleAvgMin(avgMin);
                boosterInfo.setSettleAvgMax(avgMax);

                /* 测点 */
                List<SettlePoint> settlePoints = new ArrayList<>();
                for (StatItem item : statItems) {
                    if (item.getPointId() == null) {
                        continue;
                    }
                    PointCommon point = pointMap.get(item.getPointId());
                    if (point != null) {
                        settlePoints.add(new SettlePoint(item.getPointNo(), point.getInstallElevation(), item.getMin(), item.getMax(), item.getAvg()));
                    }
                }
                if (!ObjectUtils.isEmpty(settlePoints)) {
                    boosterInfo.setSettlePoints(settlePoints);
                }
            }
        }

        /* 振动 */
        List<StatItem> shockStatItems = shockStatItemsMap.get(booster.getId());
        if (!ObjectUtils.isEmpty(shockStatItems)) {
            Double min = null;
            long shockStatItemsCount = shockStatItems.stream().filter(e -> e.getMin() != null).count();
            if (shockStatItemsCount != 0) {
                min = shockStatItems.stream().filter(e -> e.getMin() != null).mapToDouble(StatItem::getMin).min().orElse(0);
            }
            Double max = null;
            if (shockStatItemsCount != 0) {
                max = shockStatItems.stream().filter(e -> e.getMin() != null).mapToDouble(StatItem::getMax).max().orElse(0);
            }
            boosterInfo.setShockMin(min);
            boosterInfo.setShockMax(max);
        }

        /* 应力 */
        List<StatItem> stressStatItems = stressStatItemsMap.get(booster.getId());
        if (!ObjectUtils.isEmpty(stressStatItems)) {
            /* 最小值、最大值 */
            Double min = null;
            long count = stressStatItems.stream().filter(e -> e.getMin() != null).count();
            if (count != 0) {
                min = stressStatItems.stream().filter(e -> e.getMin() != null).mapToDouble(StatItem::getMin).min().orElse(0);
            }
            Double max = null;
            if (count != 0) {
                max = stressStatItems.stream().filter(e -> e.getMax() != null).mapToDouble(StatItem::getMax).max().orElse(0);
            }
            boosterInfo.setStressMin(min);
            boosterInfo.setStressMax(max);

            /* 平均值的最小值、最大值 */
            Double avgMin = null;
            count = stressStatItems.stream().filter(e -> e.getAvg() != null).count();
            if (count != 0) {
                avgMin = stressStatItems.stream().filter(e -> e.getAvg() != null).mapToDouble(StatItem::getAvg).min().orElse(0);
            }
            Double avgMax = null;
            if (count != 0) {
                avgMax = stressStatItems.stream().filter(e -> e.getAvg() != null).mapToDouble(StatItem::getAvg).max().orElse(0);
            }
            boosterInfo.setStressAvgMin(avgMin);
            boosterInfo.setStressAvgMax(avgMax);

            /* 测点 */
            List<StressPoint> stressPoints = new ArrayList<>();
            for (StatItem item : stressStatItems) {
                if (item.getPointId() == null) {
                    continue;
                }
                PointCommon point = pointMap.get(item.getPointId());
                if (point != null) {
                    stressPoints.add(new StressPoint(item.getPointNo(), point.getInstallElevation(), point.getInstallOrient(), item.getMin(), item.getMax(), item.getAvg()));
                }
            }
            if (!ObjectUtils.isEmpty(stressPoints)) {
                boosterInfo.setStressPoints(stressPoints);
            }

            /* 图片 */
            List<ReportPic> reportPics = picsMap.get(booster.getId());
            if (!ObjectUtils.isEmpty(reportPics)) {
                for (ReportPic reportPic : reportPics) {
                    if ("钢板应力".equals(reportPic.getType())) {
                        boosterInfo.setStressPic(reportPic.getBase64());
                    } else if ("静力水准".equals(reportPic.getType())) {
                        boosterInfo.setSettlePicBase64(reportPic.getBase64());
                    }
                }
            }

            /* 腐蚀 */
            List<StatItem> corrodeStatItems = corrodeStatItemsMap.get(booster.getId());
            if (!ObjectUtils.isEmpty(corrodeStatItems)) {
                int pointCount = 0;
                StringBuilder pointNoSb = new StringBuilder();
                StringBuilder pointAvgValSb = new StringBuilder();
                for (StatItem item : corrodeStatItems) {
                    if (item.getPointNo() == null) {
                        continue;
                    }
                    pointCount++;
                    pointNoSb.append(item.getPointNo() + "、");
                    pointAvgValSb.append(formatDouble(item.getAvg()) + "、");
                }
                if (pointCount > 0) {
                    pointNoSb.deleteCharAt(pointNoSb.length() - 1);
                    pointAvgValSb.deleteCharAt(pointAvgValSb.length() - 1);
                }
                String speStr = null;
                if (pointCount > 2) {
                    // 两个测点用顿号，三个及以上测点用~
                    String start = null;
                    String end = null;
                    String[] arr = pointNoSb.toString().split("、");
                    String[] split = arr[0].split("-");
                    if (split.length == 2) {
                        start = arr[0];
                    }
                    String[] lastSplit = arr[arr.length - 1].split("-");
                    if (lastSplit.length == 2) {
                        end = lastSplit[1];
                    }
                    if (start != null && end != null) {
                        speStr = start + "~" + end;
                    }
                }
                boosterInfo.setCorrodeSize(pointCount);
                if (speStr == null) {
                    boosterInfo.setCorrodePointNos(pointNoSb.toString());
                } else {
                    boosterInfo.setCorrodePointNos(speStr);
                }
                boosterInfo.setCorrodePointValues(pointAvgValSb.toString());

                // 图片
                List<ReportPic> pics = picsMap.get(booster.getId());
                if (!ObjectUtils.isEmpty(pics)) {
                    for (ReportPic pic : pics) {
                        if ("腐蚀".equals(pic.getType())) {
                            boosterInfo.setCorrodePic(pic.getBase64());
                        }
                    }
                }
            }
        }
        return boosterInfo;
    }

    /**
     * 从海上设施中过滤升压站
     */
    private SeaFacilityCommon filterBooster(List<SeaFacilityCommon> seaFacilities) {
        for (SeaFacilityCommon seaFacility : seaFacilities) {
            if (seaFacility.getId().startsWith("2-")) {
                return seaFacility;
            }
        }
        return null;
    }

    private void fillNonTypicalCorrode(SeaFacilityCommon nonTypicalFan, List<ReportNonTypicalFanInfo> nonTypicalFanInfos, Map<String,
            List<StatItem>> corrodeStatItemsMap, Map<Integer, PointCommon> pointMap, Map<String, List<ReportPic>> picsMap) {
        List<StatItem> items = corrodeStatItemsMap.get(nonTypicalFan.getId());
        if (ObjectUtils.isEmpty(items)) {
            return;
        }

        int count = 0;
        StringBuilder pointNoSb = new StringBuilder();
        StringBuilder pointAvgValSb = new StringBuilder();
        for (StatItem item : items) {
            if (item.getPointNo() == null) {
                continue;
            }
            count++;
            pointNoSb.append(item.getPointNo() + "、");
            pointAvgValSb.append(formatDouble(item.getAvg()) + "、");
        }
        if (count > 0) {
            pointNoSb.deleteCharAt(pointNoSb.length() - 1);
            pointAvgValSb.deleteCharAt(pointAvgValSb.length() - 1);
        }
        String spe = null;
        if (count > 2) {
            // 两个测点用顿号，三个及以上测点用~
            String start = null;
            String end = null;
            String[] arr = pointNoSb.toString().split("、");
            String[] split = arr[0].split("-");
            if (split.length == 2) {
                start = arr[0];
            }
            String[] lastSplit = arr[arr.length - 1].split("-");
            if (lastSplit.length == 2) {
                end = lastSplit[1];
            }
            if (start != null && end != null) {
                spe = start + "~" + end;
            }
        }
        ReportNonTypicalFanInfo typicalFanInfo = new ReportNonTypicalFanInfo();
        typicalFanInfo.setNo(nonTypicalFan.getName());
        typicalFanInfo.setCorrodeSize(count);
        if (spe == null) {
            typicalFanInfo.setCorrodePointNos(pointNoSb.toString());
        } else {
            typicalFanInfo.setCorrodePointNos(spe);
        }
        typicalFanInfo.setCorrodePointValues(pointAvgValSb.toString());

        // 设置图片
        List<ReportPic> reportPics = picsMap.get(nonTypicalFan.getId());
        if (!ObjectUtils.isEmpty(reportPics)) {
            ReportPic reportPic = reportPics.stream().filter((e -> "腐蚀".equals(e.getType()))).findFirst().orElse(null);
            if (reportPic != null) {
                typicalFanInfo.setCorrodePicBase64(reportPic.getBase64());
            }
        }

        nonTypicalFanInfos.add(typicalFanInfo);
    }

    private void fillNonTypicalStress(List<SeaFacilityCommon> nonTypicalFans, ReportTypicalFanInfo nonTypicalFanInfo, Map<String, List<StatItem>> stressStatItemsMap, Map<Integer, PointCommon> pointMap) {

    }

    private void fillNonTypicalShock(List<SeaFacilityCommon> nonTypicalFans, ReportTypicalFanInfo nonTypicalFanInfo, Map<String, List<StatItem>> shockStatItemsMap) {
        if (ObjectUtils.isEmpty(nonTypicalFans)) {
            return;
        }

        Double xMax = null;
        Double yMax = null;
        for (SeaFacilityCommon nonTypicalFan : nonTypicalFans) {
            List<StatItem> items = shockStatItemsMap.get(nonTypicalFan.getId());
            if (ObjectUtils.isEmpty(items)) {
                return;
            }
            for (StatItem item : items) {
                if ("基础顶".equals(item.getPosition()) && "X".equals(item.getDirect())) {
                    if (xMax == null || (item.getMax() != null && xMax < item.getMax())) {
                        xMax = item.getMax();
                    }
                } else if ("基础顶".equals(item.getPosition()) && "Y".equals(item.getDirect())) {
                    if (yMax == null || (item.getMax() != null && yMax < item.getMax())) {
                        yMax = item.getMax();
                    }
                }
            }
        }

        nonTypicalFanInfo.setShockMaxX(xMax);
        nonTypicalFanInfo.setShockMaxY(yMax);
    }

    private void fillNonTypicalTilt(List<SeaFacilityCommon> nonTypicalFans, ReportTypicalFanInfo nonTypicalFanInfo, Map<String, List<StatItem>> tiltStatItemsMap) {
        if (ObjectUtils.isEmpty(nonTypicalFans)) {
            return;
        }

        Double baseMinX = null;
        Double baseMaxX = null;
        Double baseAvgX_count = 0d;
        Double baseAvgX_sum = 0d;
        Double baseMinY = null;
        Double baseMaxY = null;
        Double baseAvgY_count = 0d;
        Double baseAvgY_sum = 0d;
        Double topMinX = null;
        Double topMaxX = null;
        Double topAvgX_count = 0d;
        Double topAvgX_sum = 0d;
        Double topMinY = null;
        Double topMaxY = null;
        Double topAvgY_count = 0d;
        Double topAvgY_sum = 0d;
        for (SeaFacilityCommon nonTypicalFan : nonTypicalFans) {
            List<StatItem> tiltItems = tiltStatItemsMap.get(nonTypicalFan.getId());
            if (ObjectUtils.isEmpty(tiltItems)) {
                return;
            }
            for (StatItem tiltItem : tiltItems) {
                if ("基础顶".equals(tiltItem.getPosition()) && "X".equals(tiltItem.getDirect())) {
                    if (baseMinX == null || (tiltItem.getMin() != null && baseMinX > tiltItem.getMin())) {
                        baseMinX = tiltItem.getMin();
                    }
                    if (baseMaxX == null || (tiltItem.getMax() != null && baseMaxX < tiltItem.getMax())) {
                        baseMaxX = tiltItem.getMax();
                    }
                    if (tiltItem.getAvg() != null) {
                        baseAvgX_count++;
                        baseAvgX_sum += tiltItem.getAvg();
                    }
                } else if ("基础顶".equals(tiltItem.getPosition()) && "Y".equals(tiltItem.getDirect())) {
                    if (baseMinY == null || (tiltItem.getMin() != null && baseMinY > tiltItem.getMin())) {
                        baseMinY = tiltItem.getMin();
                    }
                    if (baseMaxY == null || (tiltItem.getMax() != null && baseMaxY < tiltItem.getMax())) {
                        baseMaxY = tiltItem.getMax();
                    }
                    if (tiltItem.getAvg() != null) {
                        baseAvgY_count++;
                        baseAvgY_sum += tiltItem.getAvg();
                    }
                } else if ("塔筒顶".equals(tiltItem.getPosition()) && "X".equals(tiltItem.getDirect())) {
                    if (topMinX == null || (tiltItem.getMin() != null && topMinX > tiltItem.getMin())) {
                        topMinX = tiltItem.getMin();
                    }
                    if (topMaxX == null || (tiltItem.getMax() != null && topMaxX < tiltItem.getMax())) {
                        topMaxX = tiltItem.getMax();
                    }
                    if (tiltItem.getAvg() != null) {
                        topAvgX_count++;
                        topAvgX_sum += tiltItem.getAvg();
                    }
                } else if ("塔筒顶".equals(tiltItem.getPosition()) && "Y".equals(tiltItem.getDirect())) {
                    if (topMinY == null || (tiltItem.getMin() != null && topMinY > tiltItem.getMin())) {
                        topMinY = tiltItem.getMin();
                    }
                    if (topMaxY == null || (tiltItem.getMax() != null && topMaxY < tiltItem.getMax())) {
                        topMaxY = tiltItem.getMax();
                    }
                    if (tiltItem.getAvg() != null) {
                        topAvgY_count++;
                        topAvgY_sum += tiltItem.getAvg();
                    }
                }
            }
        }

        nonTypicalFanInfo.setBaseMinX(baseMinX);
        nonTypicalFanInfo.setBaseMaxX(baseMaxX);
        if (baseAvgX_count != 0) {
            nonTypicalFanInfo.setBaseAvgX(baseAvgX_sum / baseAvgX_count);
        }
        nonTypicalFanInfo.setBaseMinY(baseMinY);
        nonTypicalFanInfo.setBaseMaxY(baseMaxY);
        if (baseAvgY_count != 0) {
            nonTypicalFanInfo.setBaseAvgY(baseAvgY_sum / baseAvgY_count);
        }
        nonTypicalFanInfo.setTopMinX(topMinX);
        nonTypicalFanInfo.setTopMaxX(topMaxX);
        if (topAvgX_count != 0) {
            nonTypicalFanInfo.setTopAvgX(topAvgX_sum / topAvgX_count);
        }
        nonTypicalFanInfo.setTopMinY(topMinY);
        nonTypicalFanInfo.setTopMaxY(topMaxY);
        if (topAvgY_count != 0) {
            nonTypicalFanInfo.setTopAvgY(topAvgY_sum / topAvgY_count);
        }
    }

    public static String formatDouble(Double value) {
        if (value == null) {
            return null;
        }
        BigDecimal bd = new BigDecimal(value);
        bd = bd.setScale(3, RoundingMode.HALF_UP);
        return bd.toString();
    }

    private void fillCorrode(SeaFacilityCommon typicalFan, ReportTypicalFanInfo typicalFanInfo, Map<String, List<StatItem>> corrodeStatItemsMap,
                             Map<Integer, PointCommon> pointMap, Map<String, List<ReportPic>> picsMap) {
        List<StatItem> items = corrodeStatItemsMap.get(typicalFan.getId());
        if (ObjectUtils.isEmpty(items)) {
            return;
        }

        int count = 0;
        StringBuilder pointNoSb = new StringBuilder();
        StringBuilder pointAvgValSb = new StringBuilder();
        for (StatItem item : items) {
            if (item.getPointNo() == null) {
                continue;
            }
            count++;
            pointNoSb.append(item.getPointNo() + "、");
            pointAvgValSb.append(formatDouble(item.getAvg()) + "、");
        }
        if (count > 0) {
            pointNoSb.deleteCharAt(pointNoSb.length() - 1);
            pointAvgValSb.deleteCharAt(pointAvgValSb.length() - 1);
        }
        String spe = null;
        if (count > 2) {
            // 两个测点用顿号，三个及以上测点用~
            String start = null;
            String end = null;
            String[] arr = pointNoSb.toString().split("、");
            String[] split = arr[0].split("-");
            if (split.length == 2) {
                start = arr[0];
            }
            String[] lastSplit = arr[arr.length - 1].split("-");
            if (lastSplit.length == 2) {
                end = lastSplit[1];
            }
            if (start != null && end != null) {
                spe = start + "~" + end;
            }
        }
        typicalFanInfo.setCorrodeSize(count);

        if (spe == null) {
            typicalFanInfo.setCorrodePointNos(pointNoSb.toString());
        } else {
            typicalFanInfo.setCorrodePointNos(spe);
        }
        typicalFanInfo.setCorrodePointValues(pointAvgValSb.toString());

        // 设置图片
        List<ReportPic> reportPics = picsMap.get(typicalFan.getId());
        if (!ObjectUtils.isEmpty(reportPics)) {
            ReportPic reportPic = reportPics.stream().filter((e -> "腐蚀".equals(e.getType()))).findFirst().orElse(null);
            if (reportPic != null) {
                typicalFanInfo.setCorrodePicBase64(reportPic.getBase64());
            }
        }
    }

    private void fillStress(SeaFacilityCommon typicalFan, ReportTypicalFanInfo typicalFanInfo, Map<String, List<StatItem>> stressStatItemsMap,
                            Map<Integer, PointCommon> pointMap, Map<String, List<ReportPic>> picsMap) {
        List<StatItem> stressStatItems = stressStatItemsMap.get(typicalFan.getId());
        if (ObjectUtils.isEmpty(stressStatItems)) {
            return;
        }

        /* 最小值、最大值 */
        Double min = null;
        long count = stressStatItems.stream().filter(e -> e.getMin() != null).count();
        if (count != 0) {
            min = stressStatItems.stream().filter(e -> e.getMin() != null).mapToDouble(StatItem::getMin).min().orElse(0);
        }
        Double max = null;
        if (count != 0) {
            max = stressStatItems.stream().filter(e -> e.getMax() != null).mapToDouble(StatItem::getMax).max().orElse(0);
        }
        typicalFanInfo.setStressMin(min);
        typicalFanInfo.setStressMax(max);

        /* 平均值的最小值、最大值 */
        Double avgMin = null;
        count = stressStatItems.stream().filter(e -> e.getAvg() != null).count();
        if (count != 0) {
            avgMin = stressStatItems.stream().filter(e -> e.getAvg() != null).mapToDouble(StatItem::getAvg).min().orElse(0);
        }
        Double avgMax = null;
        if (count != 0) {
            avgMax = stressStatItems.stream().filter(e -> e.getAvg() != null).mapToDouble(StatItem::getAvg).max().orElse(0);
        }
        typicalFanInfo.setStressAvgMin(avgMin);
        typicalFanInfo.setStressAvgMax(avgMax);

        List<StressPoint> stressTables = new ArrayList<>();
        for (StatItem item : stressStatItems) {
            if (item.getPointId() == null) {
                continue;
            }
            PointCommon point = pointMap.get(item.getPointId());
            if (point != null) {
                stressTables.add(new StressPoint(item.getPointNo(), point.getInstallElevation(), point.getInstallOrient(), item.getMin(), item.getMax(), item.getAvg()));
            }
        }
        typicalFanInfo.setStressTables(stressTables);

        // 设置图片
        List<ReportPic> reportPics = picsMap.get(typicalFan.getId());
        if (!ObjectUtils.isEmpty(reportPics)) {
            ReportPic reportPic = reportPics.stream().filter((e -> "钢板应力".equals(e.getType()))).findFirst().orElse(null);
            if (reportPic != null) {
                typicalFanInfo.setStressPicBase64(reportPic.getBase64());
            }
        }
    }

    private void fillShock(SeaFacilityCommon typicalFan, ReportTypicalFanInfo typicalFanInfo, Map<String, List<StatItem>> shockStatItemsMap) {
        List<StatItem> items = shockStatItemsMap.get(typicalFan.getId());
        if (ObjectUtils.isEmpty(items)) {
            return;
        }

        Double xMax = null;
        Double yMax = null;
        for (StatItem item : items) {
            if ("基础顶".equals(item.getPosition()) && "X".equals(item.getDirect())) {
                if (xMax == null || xMax < item.getMax()) {
                    xMax = item.getMax();
                }
            } else if ("基础顶".equals(item.getPosition()) && "Y".equals(item.getDirect())) {
                if (yMax == null || yMax < item.getMax()) {
                    yMax = item.getMax();
                }
            }
        }
        typicalFanInfo.setShockMaxX(xMax);
        typicalFanInfo.setShockMaxY(yMax);
    }


    private void fillTilt(SeaFacilityCommon typicalFan, ReportTypicalFanInfo typicalFanInfo, Map<String, List<StatItem>> tiltStatItemsMap) {
        List<StatItem> tiltItems = tiltStatItemsMap.get(typicalFan.getId());
        if (ObjectUtils.isEmpty(tiltItems)) {
            return;
        }

        for (StatItem tiltItem : tiltItems) {
            if ("基础顶".equals(tiltItem.getPosition()) && "X".equals(tiltItem.getDirect())) {
                typicalFanInfo.setBaseMinX(tiltItem.getMin());
                typicalFanInfo.setBaseMaxX(tiltItem.getMax());
                typicalFanInfo.setBaseAvgX(tiltItem.getAvg());
            } else if ("基础顶".equals(tiltItem.getPosition()) && "Y".equals(tiltItem.getDirect())) {
                typicalFanInfo.setBaseMinY(tiltItem.getMin());
                typicalFanInfo.setBaseMaxY(tiltItem.getMax());
                typicalFanInfo.setBaseAvgY(tiltItem.getAvg());
            } else if ("塔筒顶".equals(tiltItem.getPosition()) && "X".equals(tiltItem.getDirect())) {
                typicalFanInfo.setTopMinX(tiltItem.getMin());
                typicalFanInfo.setTopMaxX(tiltItem.getMax());
                typicalFanInfo.setTopAvgX(tiltItem.getAvg());
            } else if ("塔筒顶".equals(tiltItem.getPosition()) && "Y".equals(tiltItem.getDirect())) {
                typicalFanInfo.setTopMinY(tiltItem.getMin());
                typicalFanInfo.setTopMaxY(tiltItem.getMax());
                typicalFanInfo.setTopAvgY(tiltItem.getAvg());
            }
        }
    }

    /**
     * 设置报告编写时间
     */
    private void configReportWriteTime(ReportInfo reportInfo, String writeTime) {
        String[] timeArr = writeTime.split("-");
        reportInfo.setWriteTimeYear(Integer.parseInt(timeArr[0]));
        reportInfo.setWriteTimeMonth(Integer.parseInt(timeArr[1]));
        reportInfo.setWriteTimeDay(Integer.parseInt(timeArr[2]));
    }

    /**
     * 设置报告年份和期数
     */
    private void configReportPhase(ReportInfo reportInfo, Integer year, Integer phaseNum, String startTime) {
        reportInfo.setYear(year);
        reportInfo.setTotalPhaseNum(phaseNum);

        String[] arr = startTime.split("-");
        if (arr != null && arr.length > 1) {
            reportInfo.setPhaseNum(Integer.parseInt(arr[1]));
        }
    }

    /**
     * 设置报告名称
     */
    private void configReportName(ReportInfo reportInfo, String fieldNum) {
        String fieldName = windService.getFieldNameByFieldNum(fieldNum);
        reportInfo.setFieldName(fieldName);
    }

    /***
     * 设置报告时间
     */
    private void configReportTime(ReportInfo reportInfo, String startTime, String endTime) {
        // 开始时间
        String[] startTimeArr = startTime.split("-");
        reportInfo.setStartYear(Integer.parseInt(startTimeArr[0]));
        reportInfo.setStartMonth(Integer.parseInt(startTimeArr[1]));
        reportInfo.setStartDay(Integer.parseInt(startTimeArr[2]));

        // 结束时间
        String[] endTimeArr = endTime.split("-");
        reportInfo.setEndYear(Integer.parseInt(endTimeArr[0]));
        reportInfo.setEndMonth(Integer.parseInt(endTimeArr[1]));
        reportInfo.setEndDay(Integer.parseInt(endTimeArr[2]));
    }

    private List<StatItem> getTiltInfo(List<InstCommon> fieldInsts, Map<String, List<PointCommon>> seaPointMap,
                                       Map<Integer, PointCommon> pointMap, List<SeaFacilityCommon> fieldSeaFacilities,
                                       List<AttrCommon> fieldAttrs, String startTime, String endTime, List<ReportPic> pics,
                                       String monitorItem, String attrName, String direct) {
        Integer instId = getInstId(fieldInsts, monitorItem, direct);
        if (instId == null || ObjectUtils.isEmpty(fieldSeaFacilities)) {
            return Collections.emptyList();
        }
        log.info("{}仪器ID: {}", monitorItem, instId);

        List<StatItem> statItems = new ArrayList<>();
        for (SeaFacilityCommon seaFacility : fieldSeaFacilities) {
            /* 获取该海上设施下倾斜测点（基础顶、塔筒顶） */
            List<Integer> basePointIds = getRelatePointIds(instId, seaFacility.getId(), seaPointMap, false);
            List<Integer> topPointIds = getRelatePointIds(instId, seaFacility.getId(), seaPointMap, true);

            Integer attrId = getAttrId(fieldAttrs, instId, attrName);
            if (attrId == null) {
                return Collections.emptyList();
            }

            /* 获取基础顶、塔筒顶数据 */
            List<Datum> baseData = getData(instId, attrId, startTime, endTime, basePointIds);
            List<Datum> topData = getData(instId, attrId, startTime, endTime, topPointIds);

            // 生成图片
            genPic(seaFacility.getId(), baseData, topData, basePointIds, topPointIds, seaFacility, pointMap, pics, monitorItem, statItems, direct, attrName);

            List<Datum> baseDataX = baseData.stream().filter(e -> e.getDirection() == 1).collect(Collectors.toList());
            List<Datum> baseDataY = baseData.stream().filter(e -> e.getDirection() == 2).collect(Collectors.toList());
            List<Datum> baseDataZ = baseData.stream().filter(e -> e.getDirection() == 3).collect(Collectors.toList());
            List<Datum> topDataX = topData.stream().filter(e -> e.getDirection() == 1).collect(Collectors.toList());
            List<Datum> topDataY = topData.stream().filter(e -> e.getDirection() == 2).collect(Collectors.toList());
            List<Datum> topDataZ = topData.stream().filter(e -> e.getDirection() == 3).collect(Collectors.toList());

            Double baseMinX = null;
            long baseDataXCount = baseDataX.stream().filter(e -> e.getAttrVal() != null).count();
            if (baseDataXCount != 0) {
                baseMinX = baseDataX.stream().filter(e -> e.getAttrVal() != null).mapToDouble(Datum::getAttrVal).min().orElse(0);
            }
            Double baseMaxX = null;
            if (baseDataXCount != 0) {
                baseMaxX = baseDataX.stream().filter(e -> e.getAttrVal() != null).mapToDouble(Datum::getAttrVal).max().orElse(0);
            }
            Double baseAvgX = null;
            if (baseDataXCount != 0) {
                baseAvgX = baseDataX.stream().filter(e -> e.getAttrVal() != null).mapToDouble(Datum::getAttrVal).average().orElse(0);
            }
            statItems.add(new StatItem(monitorItem, seaFacility.getId(), seaFacility.getName(), "基础顶", "X", baseMinX, baseMaxX, baseAvgX));

            Double baseMinY = null;
            long baseDataYCount = baseDataY.stream().filter(e -> e.getAttrVal() != null).count();
            if (baseDataYCount != 0) {
                baseMinY = baseDataY.stream().filter(e -> e.getAttrVal() != null).mapToDouble(Datum::getAttrVal).min().orElse(0);
            }
            Double baseMaxY = null;
            if (baseDataYCount != 0) {
                baseMaxY = baseDataY.stream().filter(e -> e.getAttrVal() != null).mapToDouble(Datum::getAttrVal).max().orElse(0);
            }
            Double baseAvgY = null;
            if (baseDataYCount != 0) {
                baseAvgY = baseDataY.stream().filter(e -> e.getAttrVal() != null).mapToDouble(Datum::getAttrVal).average().orElse(0);
            }
            statItems.add(new StatItem(monitorItem, seaFacility.getId(), seaFacility.getName(), "基础顶", "Y", baseMinY, baseMaxY, baseAvgY));

            Double baseMinZ = null;
            long baseDataZCount = baseDataZ.stream().filter(e -> e.getAttrVal() != null).count();
            if (baseDataZCount != 0) {
                baseMinZ = baseDataZ.stream().filter(e -> e.getAttrVal() != null).mapToDouble(Datum::getAttrVal).min().orElse(0);
            }
            Double baseMaxZ = null;
            if (baseDataZCount != 0) {
                baseMaxZ = baseDataZ.stream().filter(e -> e.getAttrVal() != null).mapToDouble(Datum::getAttrVal).max().orElse(0);
            }
            Double baseAvgZ = null;
            if (baseDataZCount != 0) {
                baseAvgZ = baseDataZ.stream().filter(e -> e.getAttrVal() != null).mapToDouble(Datum::getAttrVal).average().orElse(0);
            }
            statItems.add(new StatItem(monitorItem, seaFacility.getId(), seaFacility.getName(), "基础顶", "Z", baseMinZ, baseMaxZ, baseAvgZ));

            Double topMinX = null;
            long topDataXCount = topDataX.stream().filter(e -> e.getAttrVal() != null).count();
            if (topDataXCount != 0) {
                topMinX = topDataX.stream().filter(e -> e.getAttrVal() != null).mapToDouble(Datum::getAttrVal).min().orElse(0);
            }
            Double topMaxX = null;
            if (topDataXCount != 0) {
                topMaxX = topDataX.stream().filter(e -> e.getAttrVal() != null).mapToDouble(Datum::getAttrVal).max().orElse(0);
            }
            Double topAvgX = null;
            if (topDataXCount != 0) {
                topAvgX = topDataX.stream().filter(e -> e.getAttrVal() != null).mapToDouble(Datum::getAttrVal).average().orElse(0);
            }
            statItems.add(new StatItem(monitorItem, seaFacility.getId(), seaFacility.getName(), "塔筒顶", "X", topMinX, topMaxX, topAvgX));

            Double topMinY = null;
            long topDataYCount = topDataY.stream().filter(e -> e.getAttrVal() != null).count();
            if (topDataYCount != 0) {
                topMinY = topDataY.stream().filter(e -> e.getAttrVal() != null).mapToDouble(Datum::getAttrVal).min().orElse(0);
            }
            Double topMaxY = null;
            if (topDataYCount != 0) {
                topMaxY = topDataY.stream().filter(e -> e.getAttrVal() != null).mapToDouble(Datum::getAttrVal).max().orElse(0);
            }
            Double topAvgY = null;
            if (topDataYCount != 0) {
                topAvgY = topDataY.stream().filter(e -> e.getAttrVal() != null).mapToDouble(Datum::getAttrVal).average().orElse(0);
            }
            statItems.add(new StatItem(monitorItem, seaFacility.getId(), seaFacility.getName(), "塔筒顶", "Y", topMinY, topMaxY, topAvgY));

            Double topMinZ = null;
            long topDataZCount = topDataZ.stream().filter(e -> e.getAttrVal() != null).count();
            if (topDataZCount != 0) {
                topMinZ = topDataZ.stream().filter(e -> e.getAttrVal() != null).mapToDouble(Datum::getAttrVal).min().orElse(0);
            }
            Double topMaxZ = null;
            if (topDataZCount != 0) {
                topMaxZ = topDataZ.stream().filter(e -> e.getAttrVal() != null).mapToDouble(Datum::getAttrVal).max().orElse(0);
            }
            Double topAvgZ = null;
            if (topDataZCount != 0) {
                topAvgZ = topDataZ.stream().filter(e -> e.getAttrVal() != null).mapToDouble(Datum::getAttrVal).average().orElse(0);
            }
            statItems.add(new StatItem(monitorItem, seaFacility.getId(), seaFacility.getName(), "塔筒顶", "Z", topMinZ, topMaxZ, topAvgZ));
        }
        return statItems;
    }

    private List<SeaStatItem> getSeaTiltInfo(List<InstCommon> fieldInsts, Map<String, List<PointCommon>> seaPointMap,
                                             Map<Integer, PointCommon> pointMap, List<SeaFacilityCommon> fieldSeaFacilities,
                                             List<AttrCommon> fieldAttrs, String startTime, String endTime, List<ReportPic> pics,
                                             String monitorItem, String attrName, String direct) {
        Integer instId = getInstId(fieldInsts, monitorItem, direct);
        if (instId == null || ObjectUtils.isEmpty(fieldSeaFacilities)) {
            return Collections.emptyList();
        }

        List<SeaStatItem> statItems = new ArrayList<>();
        for (SeaFacilityCommon seaFacility : fieldSeaFacilities) {
            if (!seaFacility.getId().startsWith("2-")) {
                continue;
            }
            /* 获取该海上设施下倾斜测点（基础顶、塔筒顶） */
            List<Integer> basePointIds = getRelatePointIds(instId, seaFacility.getId(), seaPointMap, false);
            List<Integer> topPointIds = getRelatePointIds(instId, seaFacility.getId(), seaPointMap, true);

            Integer attrId = getAttrId(fieldAttrs, instId, attrName);
            if (attrId == null) {
                return Collections.emptyList();
            }

            Set<Integer> allPoints = new HashSet<>();
            allPoints.addAll(basePointIds);
            allPoints.addAll(topPointIds);

            for (Integer point : allPoints) {
                List<Integer> onePoint = new ArrayList<>(1);
                onePoint.add(point);
                List<Datum> onePointDate = getData(instId, attrId, startTime, endTime, onePoint);

                List<Datum> baseDataX = onePointDate.stream().filter(e -> e.getDirection() == 1).collect(Collectors.toList());
                List<Datum> baseDataY = onePointDate.stream().filter(e -> e.getDirection() == 2).collect(Collectors.toList());
                List<Datum> baseDataZ = onePointDate.stream().filter(e -> e.getDirection() == 3).collect(Collectors.toList());

                Double minX = null;
                long baseDataXCount = baseDataX.stream().filter(e -> e.getAttrVal() != null).count();
                if (baseDataXCount != 0) {
                    minX = baseDataX.stream().filter(e -> e.getAttrVal() != null).mapToDouble(Datum::getAttrVal).min().orElse(0);
                }
                Double maxX = null;
                if (baseDataXCount != 0) {
                    maxX = baseDataX.stream().filter(e -> e.getAttrVal() != null).mapToDouble(Datum::getAttrVal).max().orElse(0);
                }
                Double avgX = null;
                if (baseDataXCount != 0) {
                    avgX = baseDataX.stream().filter(e -> e.getAttrVal() != null).mapToDouble(Datum::getAttrVal).average().orElse(0);
                }

                Double minY = null;
                long baseDataYCount = baseDataY.stream().filter(e -> e.getAttrVal() != null).count();
                if (baseDataYCount != 0) {
                    minY = baseDataY.stream().filter(e -> e.getAttrVal() != null).mapToDouble(Datum::getAttrVal).min().orElse(0);
                }
                Double maxY = null;
                if (baseDataYCount != 0) {
                    maxY = baseDataY.stream().filter(e -> e.getAttrVal() != null).mapToDouble(Datum::getAttrVal).max().orElse(0);
                }
                Double avgY = null;
                if (baseDataYCount != 0) {
                    avgY = baseDataY.stream().filter(e -> e.getAttrVal() != null).mapToDouble(Datum::getAttrVal).average().orElse(0);
                }

                Double minZ = null;
                long baseDataZCount = baseDataZ.stream().filter(e -> e.getAttrVal() != null).count();
                if (baseDataZCount != 0) {
                    minZ = baseDataZ.stream().filter(e -> e.getAttrVal() != null).mapToDouble(Datum::getAttrVal).min().orElse(0);
                }
                Double maxZ = null;
                if (baseDataZCount != 0) {
                    maxZ = baseDataZ.stream().filter(e -> e.getAttrVal() != null).mapToDouble(Datum::getAttrVal).max().orElse(0);
                }
                Double avgZ = null;
                if (baseDataZCount != 0) {
                    avgZ = baseDataZ.stream().filter(e -> e.getAttrVal() != null).mapToDouble(Datum::getAttrVal).average().orElse(0);
                }

                String no = null;
                String install = null;
                PointCommon pointCommon = pointMap.get(point);
                if (pointCommon != null) {
                    no = pointCommon.getNo();
                    install = pointCommon.getInstallElevation();
                }
                statItems.add(new SeaStatItem(point, no, install, minX, maxX, avgX, minY, maxY, avgY, minZ, maxZ, avgZ));
            }
        }
        return statItems;
    }

    /**
     * 生成过程线图片
     */
    private void genPic(String seaFacilityId, List<Datum> baseData, List<Datum> topData, List<Integer> basePointIds, List<Integer> topPointIds,
                        SeaFacilityCommon seaFacility, Map<Integer, PointCommon> pointMap, List<ReportPic> pics, String monitorItem, List<StatItem> statItems, String direct, String attrName) {
        List<Datum> list = new ArrayList<>();
        list.addAll(baseData);
        list.addAll(topData);
        basePointIds.addAll(topPointIds);

        List<PngInfo> pngInfos = new ArrayList<>();
        for (Integer pointId : basePointIds) {
            // 分离X方向、Y方向、Z方向数据
            List<Datum> dataX = new ArrayList<>();
            List<Datum> dataY = new ArrayList<>();
            List<Datum> dataZ = new ArrayList<>();
            for (Datum datum : list) {
                if (pointId.equals(datum.getPointId())) {
                    if (datum.getDirection() == 1) {
                        dataX.add(datum);
                    } else if (datum.getDirection() == 2) {
                        dataY.add(datum);
                    } else {
                        dataZ.add(datum);
                    }
                }
            }

            String pointNo = pointMap.get(pointId) == null ? "" : pointMap.get(pointId).getNo();

            if (!ObjectUtils.isEmpty(dataX)) {
                pngInfos.add(new PngInfo(pointId, pointNo, "X", dataX, monitorItem));
                /* 钢板应力和腐蚀需以测点为单位进行统计 */
                if ("钢板应力".equals(monitorItem) || "腐蚀".equals(monitorItem) || "静力水准".equals(monitorItem)) {
                    double minX = dataX.stream().mapToDouble(Datum::getAttrVal).min().orElse(0);
                    double maxX = dataX.stream().mapToDouble(Datum::getAttrVal).max().orElse(0);
                    double avgX = dataX.stream().mapToDouble(Datum::getAttrVal).average().orElse(0);
                    statItems.add(new StatItem(monitorItem, seaFacility.getId(), seaFacility.getName(), pointId, pointNo, "X", minX, maxX, avgX));
                }
            }

            if (!ObjectUtils.isEmpty(dataY)) {
                pngInfos.add(new PngInfo(pointId, pointNo, "Y", dataY, monitorItem));
            }

            if (!ObjectUtils.isEmpty(dataZ)) {
                pngInfos.add(new PngInfo(pointId, pointNo, "Z", dataZ, monitorItem));
            }
        }

        /* 生成图片 */
        List<Series> series = new ArrayList<>();
        if ("钢板应力".equals(monitorItem) || "腐蚀".equals(monitorItem) || "静力水准".equals(monitorItem)) {
            // 只生成一张图片
            for (PngInfo info : pngInfos) {
                series.add(new Series(info.getPointNo(), info.getMonitorItem(), info.getData()));
            }
            pngData(seaFacilityId, series, "X", monitorItem, pics, direct, attrName);
        } else {
            for (PngInfo info : pngInfos) {
                series.add(new Series(info.getPointNo(), info.getMonitorItem(), info.getData()));
                pngData(seaFacilityId, series, info.getDirect(), monitorItem, pics, direct, attrName);
                series.clear();
            }
        }
    }

    static int x = 1000;

    private void pngData(String seaFacilityId, List<Series> series, String direct, String monitorItem, List<ReportPic> pics, String directStr, String attrName) {
        if (ObjectUtils.isEmpty(series)) {
            return;
        }
        String arrStr = serializeSeries(series);
        String pointNo = series.stream().map(Series::getName).collect(Collectors.joining(","));
        String json = null;
        try {
            json = CommonUtil.readResources("data/highcharts-example-chart.json");
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (json == null) {
            return;
        }
        json = json.replace("标题", series.size() == 1 ? pointNo + "-" + direct : pointNo);
        json = json.replace("element", arrStr);
        json = json.replace("value", attrName);
        String jsonPath = "/data/highcharts-export/infile_" + pointNo + "-" + direct + ".json";
        try {
            FileUtil.writeFile(jsonPath, json);
        } catch (Exception e) {
            e.printStackTrace();
        }
        String outPngPath = "/data/highcharts-export/outfile_" + pointNo + "-" + direct + ".png";
        executeCMD("highcharts-export-server -infile " + jsonPath + " -outfile " + outPngPath);
        System.out.println("正在生成图片：" + outPngPath);
        pics.add(new ReportPic(String.valueOf(x++), seaFacilityId, monitorItem, pointNo, getImgBase64(outPngPath), directStr));
    }

    private static String serializeSeries(List<Series> series) {
        if (ObjectUtils.isEmpty(series)) {
            return null;
        }

        StringBuilder sb = new StringBuilder();
        for (Series s : series) {
            sb.append("{");
            sb.append("\"lineWidth\":");
            sb.append(s.getLineWidth());
            sb.append(",");

            sb.append("\"type\":\"");
            sb.append(s.getType());
            sb.append("\",");

            sb.append("\"name\":\"");
            sb.append(s.getName());
            sb.append("\",");

            sb.append("\"data\":[");
            boolean flag = false;
            for (Datum datum : s.getData()) {
                sb.append("[" + datum.getTime().getTime() + "," + datum.getAttrVal() + "],");
                flag = true;
            }
            if (flag) {
                sb.deleteCharAt(sb.length() - 1);
            }
            sb.append("]},");
        }
        if (sb.charAt(sb.length() - 1) == ',') {
            sb.deleteCharAt(sb.length() - 1);
        }
        return sb.toString();
    }

    /**
     * 将图片转换成Base64编码
     *
     * @param imgFile 待处理图片地址
     * @return
     */
    public static String getImgBase64(String imgFile) {
        // 将图片文件转化为二进制流
        InputStream in = null;
        byte[] data = null;
        // 读取图片字节数组
        try {
            in = new FileInputStream(imgFile);
            data = new byte[in.available()];
            in.read(data);
            in.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Base64.getEncoder().encodeToString(data);
    }


    public static void executeCMD(String cmd) {
//        System.out.println(cmd);
        try {
            Runtime mt = Runtime.getRuntime();
            Process pro = mt.exec(cmd);
            InputStream ers = pro.getErrorStream();
            pro.waitFor();
        } catch (IOException ioe) {
            ioe.printStackTrace();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    private Integer getAttrId(List<AttrCommon> fieldAttrs, Integer instId, String attrName) {
        if (ObjectUtils.isEmpty(fieldAttrs)) {
            return null;
        }

        for (AttrCommon attr : fieldAttrs) {
            if (instId.equals(attr.getInstId()) && attr.getName().startsWith(attrName)) {
                return attr.getId();
            }
        }
        return null;
    }

    /**
     * 获取倾斜仪器ID
     */
    private Integer getInstId(List<InstCommon> fieldInsts, String type, String direct) {
        if (ObjectUtils.isEmpty(fieldInsts)) {
            return null;
        }

        for (InstCommon inst : fieldInsts) {
            if (direct != null) {
                if (inst.getMonitorName().equals(type) && inst.getDirect().equals(direct)) {
                    return inst.getId();
                }
            } else {
                if (inst.getMonitorName().equals(type)) {
                    return inst.getId();
                }
            }
        }
        return null;
    }

    private List<Datum> getData(Integer instId, Integer attrId, String startTime, String endTime, List<Integer> pointIds) {
        if (ObjectUtils.isEmpty(pointIds)) {
            return Collections.emptyList();
        }

        String tabName = "inst_" + instId;
        String colName = "c_" + attrId;
        StringBuilder sql = new StringBuilder("select ts, point, direct, " + colName + " from " + tabName + " where ts between '" + startTime + " 00:00:00' and '" + endTime + " 23:59:59'");
        // 测点范围
        sql.append(" and point in " + getStrPointIds(pointIds));

        List<Map<String, Object>> records = tdBaseService.selectMulti(sql.toString(), Constant.RATE_HOUR);
        if (records.size() == 0) {
            return Collections.emptyList();
        }

        List<Datum> data = new ArrayList<>();
        for (Map<String, Object> record : records) {
            Date ts = (Date) record.get("ts");
            Integer point = (Integer) record.get("point");
            Integer direct = (Integer) record.get("direct");
            Float attrVal = (Float) record.get(colName);
            if (attrVal != null) {
                data.add(new Datum(point, direct, ts, attrId, attrVal));
            }
        }
        return data;
    }

    private String getStrPointIds(List<Integer> pointIds) {
        StringBuilder sb = new StringBuilder("(");
        for (Integer pointId : pointIds) {
            sb.append(pointId);
            sb.append(",");
        }
        sb.deleteCharAt(sb.length() - 1);
        sb.append(")");
        return sb.toString();
    }

    /**
     * 获取相关测点
     */
    private List<Integer> getRelatePointIds(Integer instId, String seaId, Map<String, List<PointCommon>> seaPointMap, boolean isTop) {
        List<PointCommon> points = seaPointMap.get(seaId);
        if (ObjectUtils.isEmpty(points)) {
            return Collections.emptyList();
        }

        return points.stream().filter(e -> {
            if (!instId.equals(e.getInstId())) {
                return false;
            }
            if (isTop) {
                return e.getInstallElevation().startsWith("塔筒顶");
            }
            return !e.getInstallElevation().startsWith("塔筒顶");
        }).map(PointCommon::getId).collect(Collectors.toList());
    }

    /**
     * 获取某风场下所有测点，并按海上设施分类
     */
    private Map<String, List<PointCommon>> toSeaPointMap(List<PointCommon> fieldPoints) {
        if (ObjectUtils.isEmpty(fieldPoints)) {
            return Collections.emptyMap();
        }

        return fieldPoints.stream().collect(groupingBy(PointCommon::getSeaFacilityId));
    }

    /**
     * 获取某风场下所有测点，并按海上设施分类
     */
    private Map<Integer, PointCommon> toPointMap(List<PointCommon> fieldPoints) {
        if (ObjectUtils.isEmpty(fieldPoints)) {
            return Collections.emptyMap();
        }

        Map<Integer, PointCommon> map = new HashMap<>(fieldPoints.size());
        for (PointCommon point : fieldPoints) {
            map.put(point.getId(), point);
        }
        return map;
    }

    /**
     * 生成图表
     */
    public void genCharts(ChartsQo qo) {
        /* 获取该风场名称、所有仪器、测点、海上设施、属性 */
        Map<String, List<StatItem>> tiltStatItemsMap;
        Map<String, List<StatItem>> shockStatItemsMap;
        Map<String, List<StatItem>> shockBoosterStatItemsMap;
        Map<String, List<StatItem>> stressStatItemsMap;
        Map<String, List<StatItem>> corrodeStatItemsMap;
        Map<String, List<StatItem>> subsideStatItemsMap;
        Map<String, List<ReportPic>> picsMap;
        Map<Integer, PointCommon> pointMap;
        List<SeaFacilityCommon> fieldSeaFacilities;
        List<SeaStatItem> seaTiltStatItems;
        List<SeaStatItem> seaShockStatItems;
        List<StatItem> tiltStatItems;
        List<StatItem> shockStatItems;
        List<StatItem> stressStatItems;
        List<ReportPic> pics = new ArrayList<>();

        try {
            List<InstCommon> fieldInsts = monitorService.allInstByField(qo.getFieldNum());
            List<PointCommon> fieldPoints = monitorService.getPointsByFieldNum(qo.getFieldNum());
            Map<String, List<PointCommon>> seaPointMap = toSeaPointMap(fieldPoints);
            pointMap = toPointMap(fieldPoints);
            fieldSeaFacilities = windService.allSeaFacility(qo.getFieldNum());
            List<AttrCommon> fieldAttrs = monitorService.allAttrByField(qo.getFieldNum());

            /* 获取倾斜仪器ID */
            tiltStatItems = getTiltInfo(fieldInsts, seaPointMap, pointMap, fieldSeaFacilities, fieldAttrs,
                    qo.getStartTime(), qo.getEndTime(), pics, "倾斜", "倾角", null);

            shockStatItems = getTiltInfo(fieldInsts, seaPointMap, pointMap, fieldSeaFacilities, fieldAttrs,
                    qo.getStartTime(), qo.getEndTime(), pics, "振动", "有效值", "双向");

            List<StatItem> shockBoosterStatItems = getTiltInfo(fieldInsts, seaPointMap, pointMap, fieldSeaFacilities, fieldAttrs,
                    qo.getStartTime(), qo.getEndTime(), pics, "振动", "有效值", "三向");

            stressStatItems = getTiltInfo(fieldInsts, seaPointMap, pointMap, fieldSeaFacilities, fieldAttrs,
                    qo.getStartTime(), qo.getEndTime(), pics, "钢板应力", "应力", null);

            List<StatItem> corrodeStatItems = getTiltInfo(fieldInsts, seaPointMap, pointMap, fieldSeaFacilities, fieldAttrs,
                    qo.getStartTime(), qo.getEndTime(), pics, "腐蚀", "电位", null);

            List<StatItem> subsideStatItems = getTiltInfo(fieldInsts, seaPointMap, pointMap, fieldSeaFacilities, fieldAttrs,
                    qo.getStartTime(), qo.getEndTime(), pics, "静力水准", "相对沉降", null);

            seaTiltStatItems = getSeaTiltInfo(fieldInsts, seaPointMap, pointMap, fieldSeaFacilities, fieldAttrs,
                    qo.getStartTime(), qo.getEndTime(), pics, "倾斜", "倾角", null);

            seaShockStatItems = getSeaTiltInfo(fieldInsts, seaPointMap, pointMap, fieldSeaFacilities, fieldAttrs,
                    qo.getStartTime(), qo.getEndTime(), pics, "振动", "有效值", "三向");

            picsMap = pics.stream().collect(groupingBy(ReportPic::getSeaFacilityId));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static Map<String, Timer> userTimes = new HashMap<>();

    /**
     * 查询自动化设备最新数据
     */
    public void queryAutoLatestData(String userId, LatestDataQo qo) {
        qo.setInterval(getSuitableInterval(qo.getInterval()));

        // 清除定时器
        Timer timer = clearAndNewTimer(userId, qo);
        Cache.userIdTimerTsMap.put(userId, new Date().getTime());

        handleDataTask(qo, timer, userId);

        /* 任务体 */
        TimerTask task = new TimerTask() {
            @Override
            public void run() {
                handleDataTask(qo, timer, userId);
            }
        };
        try {
            timer.schedule(task, qo.getInterval(), qo.getInterval());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("userId:{}, schedule有异常，主动取消定时器", userId);
            timer.cancel();
        }
    }

    /**
     * 清除旧任务，开启新任务
     */
    private Timer clearAndNewTimer(String userId, LatestDataQo qo) {
        /* 取消旧任务 */
        Timer oldTimer = userTimes.get(userId);
        if (oldTimer != null) {
            oldTimer.cancel();
        }

        /* 开启新任务 */
        Timer timer = new Timer();
        userTimes.put(userId, timer);

        for (String id : qo.getIds()) {
            localCache.delete(RedisKey.FFT + id);
            int pointId = Integer.parseInt(id.split("-")[0]);
            localCache.delete(RedisKey.SLOSH + pointId);
        }
        return timer;
    }

    /**
     * 处理数据任务
     */
    @SneakyThrows
    private void handleDataTask(LatestDataQo qo, Timer timer, String userId) {
        try {
            String json = null;
            try {
                List<LatestDataVo> vos = tdService.queryAutoLatestData(qo, userId);
                if (!ObjectUtils.isEmpty(vos)) {
                    json = objMapper.writeValueAsString(vos);

//                    myPrint(vos, json);
                }

                /* 最多推送5小时后停止推送 */
                Long startTs = Cache.userIdTimerTsMap.get(userId);
                long expectTs = new Date().getTime() - 5 * 60 * 60 * 1000;
                if (startTs == null || startTs < expectTs) {
                    timer.cancel();
                }
            } catch (Exception e) {
                log.info("有错误{}", e);
            }
            if (json != null) {
                SseUtil.sendMessage(userId, json);
            }
        } catch (IOException e) {
            log.error("userId:{}发生异常，关闭定时器", userId);
            timer.cancel();
        }
    }

    private void myPrint(List<LatestDataVo> vos, String json) {
        if (Math.random() > 0.95) {
            if ("17630-3628-1".equals(vos.get(0).getId())) {
                log.error("取-------");
                log.error(json);
                log.error("\n");
            }
        }
    }

    /**
     * 获取合适的时间间隔
     */
    private Integer getSuitableInterval(int interval) {
        if (interval == 2000) {
            return 1800;
        }
        return 260;
    }

    /**
     * 导出任务列表
     */
    public List<ExportTask> exportTaskList(String fieldNum) {
        return dataMapper.exportTaskList(fieldNum);
    }

    /**
     * 新增导出任务
     */
    public void exportTaskAdd(String sessionId, ExportTask task) {
        /* 获取用户信息 */
        ResourceCommon resource = (ResourceCommon) redisTemplate.opsForValue().get(sessionId);
        if (resource == null) {
            return;
        }

        task.setUserId(resource.getUserId());
        task.setUsername(resource.getUsername());
        task.setPointNo(Joiner.on(",").join(task.getPointNos()));
        task.setApprovals(getApprovals(task.getDirect(), task.getApproval()));
        task.setProgress("0");

        long millis = System.currentTimeMillis();
        String outPath = (CommonUtil.isLinux() ? linuxPath : windowPath) + "/exportTask/" + millis + ".xlsx";
        String visitPath = visitMapping + "/exportTask/" + millis + ".xlsx";
        if (Constant.RATE_HIGH.equals(task.getRate())) {
            visitPath = visitPath.replace(".xlsx", ".zip");
        }
        new File(outPath).getParentFile().mkdirs();
        task.setUrl(visitPath);

        dataMapper.exportTaskAdd(task);

        /* 异步启动导出线程 */
        new Thread(() -> {
            try {
                if (Constant.RATE_HIGH.equals(task.getRate())) {
                    startExportTaskHighRate(task, outPath, millis);
                } else {
                    startExportTask(task, outPath);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }).start();
    }

    /**
     * 开始导出任务(高频)
     */
    private void startExportTaskHighRate(ExportTask task, String outPath, long millis) throws Exception {
        String zipPath = outPath.substring(0, outPath.lastIndexOf(".")) + ".zip";
        String dir = outPath.substring(0, outPath.lastIndexOf("/") + 1);
        InstDirectAttr instInfo = monitorService.getInstAttrs(task.getPointIds().get(0));
        InstDirectAttr directInfo = monitorService.getInstDirectAttr(task.getPointIds().get(0));

        int timeSpanMinutes = 2;
        int sleepMillis = 500;

        List<String> times = TimeUtil.splitBySpan(TimeUtil.completeStart(task.getStartTime()), TimeUtil.completeEnd(task.getEndTime()), timeSpanMinutes);
        int total = times.size() * task.getPointIds().size();

        int idx = 0;
        List<String> txtPaths = new ArrayList<>();
        for (int j = 0; j < task.getPointIds().size(); j++) {
            Integer pointId = task.getPointIds().get(j);
            String pointNo = task.getPointNos().get(j);

            String[] keys = new String[0];
            String[] titles = new String[0];
            List<Map<String, Object>> list = new ArrayList<>();
            for (String time : times) {
                DataTableVo vo = tdService.selectData(task.getFieldNum(), instInfo.getInstId(), directInfo.getDirects(),
                        time.split(",")[0], time.split(",")[1],
                        new Integer[]{pointId}, new String[]{pointNo},
                        1, Integer.MAX_VALUE, "ts", "asc", task.getApproval(), instInfo.getRateAttrs().get(task.getRate()),
                        task.getRate(), false, task.getDirect(), "export");
                /* 更新进度 */
                dataMapper.updateExportTaskProgress(task.getId(), df.format((idx++ * 1.0 / total * 100)));
                TimeUnit.MILLISECONDS.sleep(sleepMillis);
                if (ObjectUtils.isEmpty(vo.getBody())) {
                    continue;
                }
                TimeUnit.MILLISECONDS.sleep(sleepMillis * 2);

                TableHead[] heads = vo.getHead();
                keys = new String[heads.length];
                titles = new String[heads.length];
                for (int i = 0; i < heads.length; i++) {
                    keys[i] = heads[i].getName();
                    titles[i] = heads[i].getShowName();
                }

                List<List<CellVal>> body = vo.getBody();
                if (!ObjectUtils.isEmpty(body)) {
                    for (List<CellVal> cells : body) {
                        Map<String, Object> oneData = new HashMap<>();
                        for (int i = 0; i < keys.length; i++) {
                            oneData.put(keys[i], cells.get(i).getVal());
                        }
                        list.add(oneData);
                    }
                }
            }

            /* 将该测点的数据放入单独的文件中 */
            String txtPath = dir + millis + "-" + pointNo + ".txt";
            putData2Txt(txtPath, keys, titles, list);
            txtPaths.add(txtPath);
        }
        zipFiles(txtPaths, zipPath);

        for (String txtPath : txtPaths) {
            new File(txtPath).delete();
        }
        dataMapper.updateExportTaskProgress(task.getId(), "100");
    }

    public static void zipFiles(List<String> txtPaths, String outputZipPath) {
        try (FileOutputStream fos = new FileOutputStream(outputZipPath);
             ZipOutputStream zos = new ZipOutputStream(fos)) {

            for (String txtPath : txtPaths) {
                File file = new File(txtPath);
                if (!file.exists()) {
                    System.err.println("文件不存在: " + txtPath);
                    continue;
                }

                // 创建Zip条目
                ZipEntry zipEntry = new ZipEntry(file.getName());
                zos.putNextEntry(zipEntry);

                // 将文件内容写入ZIP条目
                try (FileInputStream fis = new FileInputStream(file)) {
                    byte[] buffer = new byte[1024];
                    int length;
                    while ((length = fis.read(buffer)) >= 0) {
                        zos.write(buffer, 0, length);
                    }
                }
                zos.closeEntry();
            }

            System.out.println("ZIP文件已成功创建: " + outputZipPath);

        } catch (IOException e) {
            System.err.println("创建ZIP文件时发生错误: " + e.getMessage());
        }
    }

    public static void putData2Txt(String filePath, String[] keys, String[] titles, List<Map<String, Object>> list) {
        try (FileWriter writer = new FileWriter(filePath)) {
            // 写入列名
            StringBuilder header = new StringBuilder();
            for (int i = 0; i < titles.length; i++) {
                header.append(titles[i]);
                if (i < titles.length - 1) {
                    header.append("\t"); // 使用制表符分隔列名
                }
            }
            writer.write(header.toString() + "\n");

            // 写入数据
            for (Map<String, Object> row : list) {
                StringBuilder dataLine = new StringBuilder();
                for (int i = 0; i < titles.length; i++) {
                    Object value = row.get(keys[i]);
                    dataLine.append(value != null ? value.toString() : "null");
                    if (i < titles.length - 1) {
                        dataLine.append("\t"); // 使用制表符分隔数据
                    }
                }
                writer.write(dataLine.toString() + "\n");
            }

            System.out.println("数据已成功导出到文件: " + filePath);

        } catch (IOException e) {
            System.err.println("导出数据时发生错误: " + e.getMessage());
        }
    }


    /**
     * 开始导出任务
     */
    private void startExportTask(ExportTask task, String outPath) throws Exception {
        InstDirectAttr instInfo = monitorService.getInstAttrs(task.getPointIds().get(0));
        InstDirectAttr directInfo = monitorService.getInstDirectAttr(task.getPointIds().get(0));

        Set<Integer> set = getApprovalSet(task.getApproval());

        int sleepMillis = 10;
        int timeSpanMinutes = 60 * 24 * 5;

        List<String> times = TimeUtil.splitBySpan(TimeUtil.completeStart(task.getStartTime()), TimeUtil.completeEnd(task.getEndTime()), timeSpanMinutes);
        int total = times.size() * task.getPointIds().size();

        Map<String, ExcelSheet> excelSheetMap = new HashMap<>();
        int idx = 0;
        for (int j = 0; j < task.getPointIds().size(); j++) {
            Integer pointId = task.getPointIds().get(j);
            String pointNo = task.getPointNos().get(j);

            String[] keys = new String[0];
            String[] titles = new String[0];
            List<Map<String, Object>> list = new ArrayList<>();
            for (String time : times) {
                System.out.println(time);
                DataTableVo vo = tdService.selectDataForExport(task.getFieldNum(), instInfo.getInstId(), directInfo.getDirects(),
                        time.split(",")[0], time.split(",")[1],
                        new Integer[]{pointId}, new String[]{pointNo},
                        1, Integer.MAX_VALUE, "ts", "asc", instInfo.getRateAttrs().get(task.getRate()),
                        task.getRate(), false, "export");
                /* 更新进度 */
                dataMapper.updateExportTaskProgress(task.getId(), df.format((idx++ * 1.0 / total * 100)));
                TimeUnit.MILLISECONDS.sleep(sleepMillis);
                if (ObjectUtils.isEmpty(vo.getBody())) {
                    continue;
                }
                TimeUnit.MILLISECONDS.sleep(sleepMillis * 2);

                TableHead[] heads = vo.getHead();
                keys = new String[heads.length];
                titles = new String[heads.length];
                for (int i = 0; i < heads.length; i++) {
                    keys[i] = heads[i].getName();
                    titles[i] = heads[i].getShowName();
                }

                List<List<CellVal>> body = vo.getBody();
                if (!ObjectUtils.isEmpty(body)) {
                    for (List<CellVal> cells : body) {
                        Map<String, Object> oneData = new HashMap<>();
                        for (int i = 0; i < keys.length; i++) {
                            CellVal cell = cells.get(i);
                            if ("point".equals(keys[i]) || "ts".equals(keys[i])) {
                                oneData.put(keys[i], cell.getVal());
                            } else if (set.contains(cell.getStatus())) {
                                oneData.put(keys[i], cell.getVal());
                            }
                        }
                        if (hasData(oneData)) {
                            list.add(oneData);
                        }
                    }
                }
            }
            excelSheetMap.put(pointNo, new ExcelSheet(keys, titles, list));
        }

        ExcelUtil.createNBig2File("数据.xlsx", excelSheetMap, outPath);
        dataMapper.updateExportTaskProgress(task.getId(), "100");
    }

    private boolean hasData(Map<String, Object> oneData) {
        if (oneData == null || oneData.isEmpty()) {
            return false;
        }

        for (Map.Entry<String, Object> en : oneData.entrySet()) {
            String key = en.getKey();
            Object value = en.getValue();
            if (key.contains("attr") && value != null) {
                return true;
            }
        }
        return false;
    }

    private Set<Integer> getApprovalSet(Integer[] approval) {
        if (ObjectUtils.isEmpty(approval)) {
            return Collections.emptySet();
        }

        Set<Integer> set = new HashSet<>();
        for (Integer integer : approval) {
            set.add(integer);
        }
        return set;
    }

    private int getMinutes(String rate) {
        if (Constant.RATE_HIGH.equals(rate)) {
            return 60;
        }
        return 60;
    }

    private String getApprovals(Integer direct, Integer[] approval) {
        StringBuilder sb = new StringBuilder();
        if (ObjectUtils.isEmpty(MonitorUtil.transDirection(direct))) {
            sb.append("所有方向，");
        } else {
            sb.append(MonitorUtil.transDirection(direct) + "，");
        }

        if (approval.length == 3) {
            sb.append("未审核/审核通过/审核未通过");
            return sb.toString();
        }

        for (Integer i : approval) {
            if (i == 0) {
                sb.append("未审核，");
            } else if (i == 1) {
                sb.append("审核通过，");
            } else {
                sb.append("审核未通过，");
            }
        }
        sb.deleteCharAt(sb.length() - 1);
        return sb.toString();
    }

    /**
     * 删除导出任务
     */
    public void exportTaskDel(Integer id) {
        dataMapper.exportTaskDel(id);
    }

    public InstDirectAttr getQueryAttrByPoint(String fieldNum, Integer point) {

        InstDirectAttr attr = monitorService.getInstDirectAttr(point);
        if (attr == null) {
            return null;
        }
        Integer instId = attr.getInstId();
        if (instId == null) {
            log.error("数据查询-根据测点获取可查询的分量-仪器ID为空:{}", attr);
            attr.setAttrs(Collections.emptyList());
            return attr;
        }
        InstCommon inst = monitorService.allInstByField(fieldNum).stream()
                .filter(e -> Objects.equals(e.getId(), instId))
                .findFirst().orElse(null);
        if (inst == null || inst.getClassify() == null) {
            log.error("数据查询-根据测点获取可查询的分量-未查询到仪器信息:{}-{}", attr, inst);
            attr.setAttrs(Collections.emptyList());
            return null;
        }
        List<AttrCommon> attrs = attr.getAttrs() == null ? Collections.emptyList() : attr.getAttrs();
        List<Direct> directs = attr.getDirects() == null ? Collections.emptyList() : attr.getDirects();
        attr.setDefaultShowAttrs(attrs);
        List<AttrCommon> as = attrs.stream()
                .flatMap(e -> {
                    AtomicLong bid = new AtomicLong(1);
                    return directs.stream().map(d -> {
                        AttrCommon a = new AttrCommon();
                        BeanUtils.copyProperties(e, a);
                        String name = e.getIsPolar() ? e.getName() : d.getDirectName() + "方向" + e.getName();
                        a.setName(name);
                        a.setBId(e.getId() + "-" + bid);
                        return a;
                    });
                }).collect(Collectors.toList());
        attr.setAttrs(as);
        return attr;
    }
}

