package com.hdec.data.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class CellVal {

    private String id;

    private Object val;

    private Object oldVal;

    private Integer status;

    public CellVal(String id, Object val, Object oldVal) {
        this.id = id;
        this.val = val;
        this.oldVal = oldVal;
    }

    public CellVal(String id, Object val, Object oldVal, Integer status) {
        this.id = id;
        this.val = val;
        this.oldVal = oldVal;
        this.status = status;
    }

    //    public CellVal(String id, Object val, Object oldVal) {
//        this.id = id;
//
//        if (val == null) {
//            this.val = null;
//        } else {
//            if (val instanceof Float) {
//                Float aFloat = (Float) val;
//                if (aFloat != null && aFloat > -0.0000001 && aFloat < 0.0000001) {
//                    this.val = null;
//                } else {
//                    this.val = val;
//                }
//            } else {
//                this.val = val;
//            }
//        }
//
//        if (oldVal == null) {
//            this.oldVal = null;
//        } else {
//            if (oldVal instanceof Float) {
//                Float aFloat = (Float) oldVal;
//                if (aFloat != null && aFloat > -0.0000001 && aFloat < 0.0000001) {
//                    this.oldVal = null;
//                } else {
//                    this.oldVal = oldVal;
//                }
//            } else {
//                this.oldVal = oldVal;
//            }
//        }
//    }
}
