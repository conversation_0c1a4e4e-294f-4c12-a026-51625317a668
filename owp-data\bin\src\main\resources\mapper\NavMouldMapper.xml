<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hdec.data.mapper.NavMouldMapper">

    <delete id="deleteByNavId" parameterType="int">
        DELETE FROM
            owp_report_nav_mould
        WHERE
            nav_id = #{navId}
    </delete>

    <select id="getOutlineByMould" resultType="int">
        SELECT
            nav_id
        FROM
            owp_report_nav_mould
        WHERE
            mould_id = #{mouldId}
    </select>

    <select id="getByNavMould" resultType="int">
        SELECT
            id
        FROM
            owp_report_nav_mould
        WHERE
            nav_id = #{navId} AND mould_id = #{mouldId}
        limit 1
    </select>

    <insert id="addMould2navId">
        INSERT INTO owp_report_nav_mould (`nav_id`, `mould_id`, `fan_val`)
        VALUES (
            #{navId}, #{mouldId}, #{fanVal}
        )
    </insert>

    <insert id="removeMould2Nav">
        DELETE FROM
            owp_report_nav_mould
        WHERE
            nav_id = #{navId} AND mould_id = #{mouldId}
    </insert>

    <select id="getNavMouldValue" resultType="com.hdec.data.qo.MouldVal">
        SELECT
            fan_val, fan_val_name, ref_val
        FROM
            owp_report_nav_mould
        WHERE
            nav_id = #{navId} AND mould_id = #{mouldId}
    </select>

    <update id="setMouldValue" parameterType="com.hdec.data.qo.MouldVal">
        UPDATE
            owp_report_nav_mould
        <set>
            <if test="mouldVal.fanVal != null">
                fan_val = #{mouldVal.fanVal},
            </if>
            <if test="mouldVal.fanValName != null">
                fan_val_name = #{mouldVal.fanValName},
            </if>
            <if test="mouldVal.refVal != null">
                ref_val = #{mouldVal.refVal},
            </if>
        </set>
        WHERE
            nav_id = #{mouldVal.navId} AND mould_id = #{mouldVal.mouldId}
    </update>

    <delete id="delByRefId">
        DELETE
        FROM
            owp_report_nav_mould
        WHERE
            ref_val LIKE CONCAT('%u-', #{mouldId}, '-var-chartNum-%')
    </delete>

    <select id="selectRefInfo" resultType="com.hdec.data.qo.MouldVal">
        SELECT
            *
        FROM
            owp_report_nav_mould
        WHERE
            ref_val LIKE CONCAT('%', #{navId}, '_u-', #{mouldId}, '-var-chartNum-%')
    </select>

    <select id="getNavMouldByRefId" resultType="com.hdec.data.qo.MouldVal">
        SELECT
            *
        FROM
            owp_report_nav_mould
        WHERE
            ref_val LIKE concat('%"refId":"u-', #{mouldId}, '-%')
    </select>

    <select id="getNavMouldValueByMouldId" resultType="com.hdec.data.qo.MouldVal">
        SELECT
            *
        FROM
            owp_report_nav_mould
        WHERE
            mould_id = #{mouldId}
    </select>

    <select id="list" resultType="com.hdec.data.qo.MouldVal">
        SELECT
            *
        FROM
            owp_report_nav_mould
        WHERE
            nav_id IN (
                SELECT
                    id
                FROM
                    owp_report_outline_nav
                WHERE
                    outline_id = #{outlineId}
            )
    </select>

    <insert id="addNavMould">
        INSERT INTO owp_report_nav_mould (`nav_id`, `mould_id`, `fan_val`,`fan_val_name`, `ref_val`)
        VALUES (#{mould.navId}, #{mould.mouldId}, #{mould.fanVal}, #{mould.fanValName}, #{mould.refVal})
    </insert>

    <update id="updateRef">
        update owp_report_nav_mould set ref_val = #{ref} where id = #{id}
    </update>

</mapper>