package com.hdec.data.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * 加速度数据返回类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class AccVo {

    /** 时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date time;

    /** 值 */
    private Float val;

    public AccVo(Date time, Float val) {
        this.time = time;
        this.val = val;
    }
}
