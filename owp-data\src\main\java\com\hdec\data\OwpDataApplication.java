package com.hdec.data;

import com.alibaba.fastjson.parser.ParserConfig;
import com.github.xiaoymin.knife4j.spring.annotations.EnableKnife4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import springfox.documentation.swagger2.annotations.EnableSwagger2;


/**
 * 风场服务启动类
 *
 * <AUTHOR>
 */
@EnableSwagger2
@EnableKnife4j
@EnableFeignClients
@EnableDiscoveryClient
@SpringBootApplication(scanBasePackages = "com.hdec")
public class OwpDataApplication {
    private static Logger log = LoggerFactory.getLogger(OwpDataApplication.class);

    public static void main(String[] args) {
        SpringApplication.run(OwpDataApplication.class, args);
        ParserConfig.getGlobalInstance().setSafeMode(true);
        log.info("数据服务启动成功");
    }

}
