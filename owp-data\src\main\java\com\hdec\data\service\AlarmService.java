package com.hdec.data.service;

import com.alibaba.fastjson.JSON;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.hdec.common.constant.Constant;
import com.hdec.common.constant.RedisKey;
import com.hdec.common.domain.*;
import com.hdec.common.formula.AttrVal;
import com.hdec.common.util.CommonUtil;
import com.hdec.common.util.DataServiceUtil;
import com.hdec.common.util.MonitorUtil;
import com.hdec.common.util.TimeUtil;
import com.hdec.common.vo.AlarmAdvancedRuleVo;
import com.hdec.data.cache.Cache;
import com.hdec.data.cache.LocalCacheManager;
import com.hdec.data.config.LocalCache;
import com.hdec.data.feign.MonitorService;
import com.hdec.data.util.AlarmWindowUtil;
import com.hdec.data.vo.AlarmWindowVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 告警业务类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@EnableScheduling
public class AlarmService {

    @Autowired
    private LocalCache localCache;

    @Lazy
    @Autowired
    private TdService tdService;

    @Lazy
    @Autowired
    private TdBaseService tdBaseService;

    @Autowired
    private MonitorService monitorService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private LocalCacheManager localCacheManager;

    /**
     * 处理告警
     *
     * @param values 数据
     */
    public void processAlarm(List<AttrVal> values, String fieldNum) {
        long t0 = System.currentTimeMillis();
        List<Alarm> alarms = handleAlarm(values, fieldNum);
        long t1 = System.currentTimeMillis();
        if (!alarms.isEmpty()) {
            Lists.partition(alarms, 100).forEach(l -> monitorService.saveAlarms(l));
            com.hdec.data.cache.Cache.hasAlarmFlag = true;
        }
        long t2 = System.currentTimeMillis();
        if (t2 - t0 > 200) {
            log.info("处理告警总耗时:{}ms 告警处理:{}ms 告警入库:{}ms 告警数量:{}",
                    t2 - t0, t1 - t0, t2 - t1, alarms.size());
        }
    }

    public List<Alarm> handleAlarm(List<AttrVal> values, String fieldNum) {
        List<Alarm> alarms = Lists.newArrayList();
        try {
            values = values.stream().filter(value->value.getTs() != null && value.getVal() != null).collect(Collectors.toList());
            Map<String, List<AttrVal>> groups = values.stream().collect(Collectors.groupingBy(e -> e.getPoint() + "-" + e.getAttr()));
            groups.forEach((group,groupValues)->{
                AlarmRuleVo rule = localCacheManager.getAlarmRuleCache(group);
                PointCommon point = localCacheManager.getPointCache(Integer.parseInt(group.split("-")[0]));
                String pointNo = point == null ? "未缓存测点" : point.getNo();
                if (rule!=null){
                    groupValues.forEach(value->{
                        List<Alarm> normalAlarms = normalAlarm(value.getTs(), Integer.valueOf(value.getPoint()), pointNo,
                                Integer.valueOf(value.getAttr()), Integer.valueOf(value.getDirect()), value.getVal(), rule, fieldNum);
                        if (!normalAlarms.isEmpty()) {
                            alarms.addAll(normalAlarms);
                        }
                    });
                }
            });
        } catch (Exception e) {
            log.error("告警处理出错：", e);
        }
        return alarms;
    }

    /**
     * 阈值告警
     *
     * @param ts       时间
     * @param pointId  测点ID
     * @param pointNo  测点NO
     * @param attrId   分量ID
     * @param direct   分量方向
     * @param attrVal  值
     * @param rule     规则
     * @param fieldNum 项目ID
     * @return {@link List }<{@link Alarm }>
     */
    public List<Alarm> normalAlarm(Date ts, Integer pointId, String pointNo, Integer attrId, Integer direct, Double attrVal, AlarmRuleVo rule, String fieldNum) {
        List<Alarm> alarms = new ArrayList<>();
        AttrCommon attr = localCacheManager.getAttrCache(attrId);
        /* 告警 */
        if (isAbsValBetween(attrVal.floatValue(), rule.getTinyExceptLowLimit(), rule.getTinyExceptUpLimit())) {
            Integer coolDownTime = rule.getCoolDownTime();
            if (coolDownTime != null && coolDownTime > 0) {
                AlarmWindowUtil.normalCountUp(pointId, attrId, direct, attrVal, ts, coolDownTime.longValue());
            } else {
                /*  直接产生告警  */
                alarms.add(new Alarm(pointId, attrId, direct, MonitorUtil.transDirection(direct), "告警中", TimeUtil.format2Ms(ts),
                        "轻微", pointNo + " " + attr.getName() + " " + attrVal + " 触发告警", fieldNum));
            }
        }
        /*  窗口清盘  */
        AlarmWindowVo window = AlarmWindowUtil.normalWindup(pointId, attrId, direct, ts);
        if (window != null) {
            Date time = window.getTime();
            Double value = window.getValue();
            alarms.add(new Alarm(pointId, attrId, direct, MonitorUtil.transDirection(direct), "告警中", TimeUtil.format2Ms(time),
                    "轻微", pointNo + " " + attr.getName() + " " + value + " 触发告警", fieldNum));
        }
        return alarms;
    }

    /**
     * 处理告警
     */
    public void handleAlarm(Map<Integer, PointCommon> fieldIdPoints, Map<String, AlarmRuleVo> ruleMap, Map<String, AlarmAdvancedRuleVo> advancedRuleMap, Map<Integer, AttrCommon> fieldAttrs, String time, Integer pointId, Integer direct, Integer attrId, Float attrVal, String fieldNum, List<Alarm> alarmRecords) {
        try {
            Date date = parseDate(time);
            if (date == null || attrVal == null) {
                return;
            }

            AlarmRuleVo rule = ruleMap.get(pointId + "-" + attrId);
            if (rule != null) {
                normalAlarm(fieldIdPoints, fieldAttrs, pointId, direct, attrId, attrVal, fieldNum, alarmRecords, date, rule);
            }

            /* 高级告警A */
            AlarmAdvancedRuleVo rulesA = advancedRuleMap.get(CommonUtil.join("-", pointId, attrId, 1));
            if (rulesA != null) {
                advancedAlarm_A(fieldIdPoints, rulesA, pointId, direct, attrId, attrVal, fieldNum, alarmRecords, date, fieldAttrs);
            }

            /* 高级告警B */
            AlarmAdvancedRuleVo rulesB = advancedRuleMap.get(CommonUtil.join("-", pointId, attrId, 2));
            if (rulesB != null) {
                advancedAlarm_B(fieldIdPoints, rulesB, pointId, direct, attrId, attrVal, fieldNum, alarmRecords, date, fieldAttrs);
            }

            /* 高级告警：延时双重检测 */
            Alarm alarm = alarmProcessDdc(fieldNum, pointId, attrId, direct, Double.valueOf(attrVal), date);
            if (alarm != null) {
                alarmRecords.add(alarm);
            }
        } catch (Exception e) {
            log.error("告警出错：", e);
        }
    }

    /**
     * 阈值告警
     */
    private void normalAlarm(Map<Integer, PointCommon> fieldIdPoints, Map<Integer, AttrCommon> fieldAttrs, Integer pointId, Integer direct, Integer attrId, Float attrVal, String fieldNum, List<Alarm> alarmRecords, Date date, AlarmRuleVo rule) {
        AttrCommon attr = fieldAttrs.get(attrId);

        /* 告警 */
        if (isAbsValBetween(attrVal, rule.getTinyExceptLowLimit(), rule.getTinyExceptUpLimit())) {
            Integer coolDownTime = rule.getCoolDownTime();
            if (coolDownTime != null && coolDownTime > 0) {
                AlarmWindowUtil.normalCountUp(pointId, attrId, direct, attrVal.doubleValue(), date, coolDownTime.longValue());
            } else {
                /*  直接产生告警  */
                alarmRecords.add(new Alarm(pointId, attrId, direct, MonitorUtil.transDirection(direct), "告警中", TimeUtil.format2Ms(date),
                        "轻微", getPointNo(fieldIdPoints, pointId) + " " + attr.getName() + " " + attrVal + " 触发告警", fieldNum));
            }
        }
        /*  窗口清盘  */
        AlarmWindowVo window = AlarmWindowUtil.normalWindup(pointId, attrId, direct, date);
        if (window != null) {
            Date time = window.getTime();
            Double value = window.getValue();
            alarmRecords.add(new Alarm(pointId, attrId, direct, MonitorUtil.transDirection(direct), "告警中", TimeUtil.format2Ms(time),
                    "轻微", getPointNo(fieldIdPoints, pointId) + " " + attr.getName() + " " + value + " 触发告警", fieldNum));
        }

//        String key = RedisKey.ALARM_NORMAL + CommonUtil.join("_", pointId, direct, attrId);
//        if (localCache.get(key) != null) {
//            return;
//        }
//        if (isAbsValBetween(attrVal, rule.getTinyExceptLowLimit(), rule.getTinyExceptUpLimit())) {
//            alarmRecords.add(new Alarm(pointId, attrId, direct, MonitorUtil.transDirection(direct), "告警中", TimeUtil.format2Ms(date), "轻微", getPointNo(fieldIdPoints, pointId) + attr.getName() + attrVal + "触发告警", fieldNum));
//            Integer coolDownTime = rule.getCoolDownTime();
//            if (coolDownTime != null) {
//                localCache.set(key, true, coolDownTime, TimeUnit.SECONDS);
//            }
//        }
//        } else if (isAbsValBetween(attrVal, rule.getGeneralExceptLowLimit(), rule.getGeneralExceptUpLimit())) {
//            alarmRecords.add(new Alarm(pointId, attrId, direct, MonitorUtil.transDirection(direct), "告警中", TimeUtil.format2Ms(date), "一般", getPointNo(fieldIdPoints, pointId) + attr.getName() + attrVal + "触发一般告警", fieldNum));
//            localCache.set(key, true, 10, TimeUnit.MINUTES);
//        } else if (isAbsValBetween(attrVal, rule.getSeriousExceptLowLimit(), rule.getSeriousExceptUpLimit())) {
//            alarmRecords.add(new Alarm(pointId, attrId, direct, MonitorUtil.transDirection(direct), "告警中", TimeUtil.format2Ms(date), "严重", getPointNo(fieldIdPoints, pointId) + attr.getName() + attrVal + "触发严重告警", fieldNum));
//            localCache.set(key, true, 10, TimeUnit.MINUTES);
//        }
    }

    private String getPointNo(Map<Integer, PointCommon> fieldIdPoints, Integer pointId) {
        if (fieldIdPoints == null) {
            return "";
        }
        PointCommon point = fieldIdPoints.get(pointId);
        if (point == null) {
            return "";
        }
        return point.getNo();
    }

    private Date parseDate(String time) {
        if (time.contains(".")) {
            return TimeUtil.parse2Ms(time);
        }
        return TimeUtil.parse2Second(time);
    }

    /**
     * 高级告警A
     */
    private void advancedAlarm_A(Map<Integer, PointCommon> fieldIdPoints, AlarmAdvancedRuleVo rule, Integer pointId, Integer direct, Integer attrId, Float attrVal, String fieldNum, List<Alarm> alarmRecords, Date time, Map<Integer, AttrCommon> fieldAttrs) {
        String key = RedisKey.ALARM_MODEL_A + CommonUtil.join("_", pointId, direct, attrId);
        if (redisTemplate.opsForValue().get(key) != null) {
            return;
        }
        if (rule.getThreshold1() == null || attrVal < rule.getThreshold1()) {
            return;
        }

        AttrCommon attr2 = fieldAttrs.get(rule.getAttrId2());
        if (attr2 == null) {
            return;
        }

        String tableName = DataServiceUtil.buildTableName(attr2.getInstId(), attr2.getRate(), pointId);
        String colName = tdService.enColName(attr2.getId(), direct);
        String sql = "select " + colName + " from " + tableName + " where ts <= '" + TimeUtil.format2Ms(time) + "' order by ts desc limit " + rule.getGroupNum();
        List<Map<String, Object>> records = tdBaseService.selectMulti(sql, attr2.getRate());
        log.info("告警模型A的sql:{}", sql);

        List<Float> histories = parseRecords(records, colName);
        double radio = exceedLimitRate(histories, rule.getThreshold2());
        if (radio > rule.getRatio() / 100) {
            // 报警，超限记录进日志
            alarmRecords.add(new Alarm(pointId, attr2.getId(), direct, MonitorUtil.transDirection(direct), "告警中", TimeUtil.format2Ms(time), "持续超限告警",
                    getPointNo(fieldIdPoints, pointId) + "低阈值超限且达到告警比例，触发持续超限告警模型A（取过去" + rule.getGroupNum() + "组数据，超限比例" + Math.round(radio * 100) + "%)", fieldNum, rule.getGroupNum(), "A"));

            // 未来10分钟无需再次告警
            redisTemplate.opsForValue().set(key, true, 10, TimeUnit.MINUTES);
        } else {
            // 不报警，超限记录进日志
            alarmRecords.add(new Alarm(pointId, attr2.getId(), direct, MonitorUtil.transDirection(direct), "自动废弃", TimeUtil.format2Ms(time), "持续超限告警",
                    getPointNo(fieldIdPoints, pointId) + "低阈值超限但未达到告警比例，触发持续超限告警模型A（取过去：" + rule.getGroupNum() + "组数据，超限比例" + Math.round(radio * 100) + "%)", fieldNum, rule.getGroupNum(), "A"));

            // 未来10分钟无需再次告警
            redisTemplate.opsForValue().set(key, true, 10, TimeUnit.MINUTES);
        }
    }

    private String getUnitByRate(String rate) {
        if ("分钟级".equals(rate)) {
            return "m";
        } else if ("小时级".equals(rate)) {
            return "h";
        }
        return "s";
    }

    /**
     * 高级告警B
     */
    private void advancedAlarm_B(Map<Integer, PointCommon> fieldIdPoints, AlarmAdvancedRuleVo rule, Integer pointId, Integer direct, Integer attrId,
                                 Float attrVal, String fieldNum, List<Alarm> alarmRecords, Date date, Map<Integer, AttrCommon> fieldAttrs) {
        String key = RedisKey.ALARM_MODEL_B + CommonUtil.join("_", pointId, direct, attrId);
        Object obj = redisTemplate.opsForValue().get(key);
        if (obj != null) {
            return;
        }

        if (rule.getThreshold1() == null || attrVal < rule.getThreshold1()) {
            return;
        }

        /* 超限后直接在Redis中记录即可 */
        AttrCommon attr2 = fieldAttrs.get(rule.getAttrId2());
        if (attr2 == null) {
            return;
        }

        String targetEndTime = getTargetEndTime(date, attr2.getRate(), rule.getGroupNum());
        String value = CommonUtil.join(Constant.SPLIT_STR, pointId, direct, targetEndTime, fieldNum, JSON.toJSONString(rule), getPointNo(fieldIdPoints, pointId), TimeUtil.format2Ms(date));
        log.info("模型B有记录：" + value);

        /* 无需再次判断 */
        redisTemplate.opsForValue().set(key, value);
    }

    /**
     * 每10秒执行一次
     */
    @Scheduled(cron = "0/60 * * * * ?")
    public void doPer10Seconds() {
        Set<String> keys = redisTemplate.keys(RedisKey.ALARM_MODEL_B + "*");
        if (ObjectUtils.isEmpty(keys)) {
            return;
        }
        log.info("keys:{}", keys.toString());

        Date now = new Date();
        List<Alarm> alarmRecords = new ArrayList<>();
        for (String key : keys) {
            String[] arr = ((String) redisTemplate.opsForValue().get(key)).split(Constant.SPLIT_STR);
            Integer pointId = Integer.parseInt(arr[0]);
            Integer direct = Integer.parseInt(arr[1]);
            String targetEndTime = arr[2];
            String fieldNum = arr[3];
            AlarmAdvancedRuleVo rule = JSON.parseObject(arr[4], AlarmAdvancedRuleVo.class);
            String pointNo = arr[5];
            String time = arr[6];

            if (now.before(TimeUtil.parse2Second(targetEndTime))) {
                System.out.println("没有达到时间");
                continue;
            }
            AttrCommon attr2 = getAttrFromCache(rule.getAttrId2());
            if (attr2 == null) {
                return;
            }

            String tableName = DataServiceUtil.buildTableName(attr2.getInstId(), attr2.getRate(), pointId);
            String colName = tdService.enColName(attr2.getId(), direct);
            String sql = "select " + colName + " from " + tableName + " where ts >= '" + time + "' order by ts desc limit " + rule.getGroupNum();
            log.info("告警模型B的sql:{}", sql);

            List<Map<String, Object>> records = tdBaseService.selectMulti(sql, attr2.getRate());
            if (records.size() < rule.getGroupNum()) {
                // todo 按道理应该
                log.info("数量{}还不够", records.size());
                return;
            }

            System.out.println("已经达到时间且删除了key");
            redisTemplate.delete(key);

            List<Float> histories = parseRecords(records, colName);
            double radio = exceedLimitRate(histories, rule.getThreshold2());
            System.out.println(histories);
            System.out.println(radio);
            if (radio > rule.getRatio() / 100) {
                // 报警，超限记录进日志
                alarmRecords.add(new Alarm(pointId, attr2.getId(), direct, MonitorUtil.transDirection(direct), "告警中", time, "持续超限告警",
                        pointNo + "低阈值超限且达到告警比例，触发持续超限告警模型B（取未来" + rule.getGroupNum() + "组数据，超限比例" + Math.round(radio * 100) + "%)", fieldNum, rule.getGroupNum(), "B"));
            } else {
                // 不报警，超限记录进日志
                alarmRecords.add(new Alarm(pointId, attr2.getId(), direct, MonitorUtil.transDirection(direct), "自动废弃", time, "持续超限告警",
                        pointNo + "低阈值超限但未达到告警比例，触发持续超限告警模型B（取未来：" + rule.getGroupNum() + "组数据，超限比例" + Math.round(radio * 100) + "%)", fieldNum, rule.getGroupNum(), "B"));
            }
        }

        if (alarmRecords.size() > 0) {
            Lists.partition(alarmRecords, 100).forEach(l -> monitorService.saveAlarms(l));
            Cache.hasAlarmFlag = true;
        }
    }

    /**
     * 计算超限比例
     */
    private static double exceedLimitRate(List<Float> values, Float targetVal) {
        if (ObjectUtils.isEmpty(values) || targetVal == null) {
            return 0;
        }

        long exceedLimitCount = values.stream().filter(e -> e >= targetVal).count();
        return (double) exceedLimitCount / values.size();
    }

    /**
     * 判断某值的绝对值是否在某个范围
     */
    private boolean isAbsValBetween(Float val, Float low, Float up) {
        if (val == null || low == null || up == null) {
            return false;
        }
        return Math.abs(val) >= low && Math.abs(val) <= up;
    }

    private List<Float> parseRecords(List<Map<String, Object>> records, String colName) {
        if (ObjectUtils.isEmpty(records)) {
            return Collections.emptyList();
        }

        List<Float> list = new ArrayList<>(records.size());
        for (Map<String, Object> record : records) {
            list.add(Float.parseFloat(String.valueOf(record.get(colName))));
        }
        return list;
    }

    /**
     * 获取开始和结束时间
     */
    private static String[] getStartAndEndTime(Date time, String rate, int n) {
        String[] arr = new String[2];

        if ("分钟级".equals(rate)) {
            arr[0] = TimeUtil.round2Minute(TimeUtil.addMinutes(time, -n));
            arr[1] = TimeUtil.round2Minute(time);
        } else if ("小时级".equals(rate)) {
            arr[0] = TimeUtil.round2Hour(TimeUtil.addHours(time, -n));
            arr[1] = TimeUtil.round2Hour(time);
        } else {
            arr[0] = TimeUtil.round2Second(TimeUtil.addSeconds(time, -n));
            arr[1] = TimeUtil.round2Second(time);
        }
        return arr;
    }


    /**
     * 获取开始和结束时间
     */
    private static String getTargetEndTime(Date time, String rate, int n) {
        if ("分钟级".equals(rate)) {
            return TimeUtil.format2Second(TimeUtil.addMinutes(time, n + 1));
        } else if ("小时级".equals(rate)) {
            return TimeUtil.format2Second(TimeUtil.addHours(time, n + 1));
        }
        return TimeUtil.format2Second(TimeUtil.addSeconds(time, n + 1));
    }

    /**
     * 从缓存中获取分量
     */
    private AttrCommon getAttrFromCache(Integer attrId) {
        Object obj = redisTemplate.opsForValue().get(RedisKey.ATTR + attrId);
        if (obj != null) {
            return JSON.parseObject(obj.toString(), AttrCommon.class);
        }

        AttrCommon attr = monitorService.getProAttrById(attrId);
        redisTemplate.opsForValue().set(RedisKey.ATTR + attrId, JSON.toJSONString(attr), 10, TimeUnit.SECONDS);
        return attr;
    }

    /**
     * 告警处理延迟双重检查
     *
     * @param fieldNum 风场编码
     * @param pointId  测点ID
     * @param attrId   分量ID
     * @param direct   方向
     * @param value    值
     * @param time     时间
     */
    public Alarm alarmProcessDdc(String fieldNum, Integer pointId, Integer attrId, Integer direct, Double value, Date time) {
        /*  获取DDC告警配置  */
        AlarmDdcRuleCommon rule = getAlarmDdcRule(fieldNum, pointId);
        if (rule == null || !rule.isExist()) {
            return null;
        }
        Integer instId = rule.getInstId();
        Integer overAttrId = rule.getOverAttrId();
        Double overThreshold = rule.getOverThreshold();
        Integer overTime = rule.getOverTime();
        Integer retraceAttrId = rule.getRetraceAttrId();
        Double retraceThreshold = rule.getRetraceThreshold();
        Integer retraceTime = rule.getRetraceTime();
        Integer ratio = rule.getRatio();

        /*  校验参数 */
        if (instId == null || overAttrId == null || overThreshold == null || overTime == null
                || retraceAttrId == null || retraceThreshold == null || retraceTime == null
                || ratio == null) {
            return null;
        }

        if (Objects.equals(attrId, overAttrId) && Math.abs(value) >= overThreshold) {
            /*  触发超限警  */
            /*  窗口计数  */
            AlarmWindowUtil.ddcCountUp(pointId, attrId, direct, value, time, overTime.longValue());
        }

        /*  计算时间窗口需不需要清盘  */
        AlarmWindowVo window = AlarmWindowUtil.ddcWindup(pointId, attrId, direct, time);
        if (window != null && window.getCount().get() > 1) {
            /*  窗口清盘  */
            try {
                log.info("延时双重检测触发回溯-{}", TimeUtil.format2Second(time));
                /* 查询回溯数据 */
                Date start = TimeUtil.addMinutes(time, -retraceTime);
                List<Double> retraceRecord = queryMinuteRecord(instId, pointId, retraceAttrId, direct, start, time);
                long count = retraceRecord.stream().filter(val -> Math.abs(val) >= retraceThreshold).count();
                double exceedRatio = (double) count / retraceRecord.size() * 100;
                if (exceedRatio >= ratio) {
                    /*  触发超限告警  */
                    log.info("延时双重检测触发超限告警-{}：{}", time, retraceRecord);
                    String msg = "延时双重检测触发超限告警:回溯时间" + TimeUtil.format2Second(start) + " - " + TimeUtil.format2Second(time) + " 超限比例:" + exceedRatio + "%";
                    return new Alarm(pointId, attrId, direct, MonitorUtil.transDirection(direct), "告警中",
                            TimeUtil.format2Ms(time), "一般", msg, fieldNum);
                }
            } catch (Exception e) {
                log.error("延时双重检测超限告警异常：{}", e.getMessage());
            }
        }
        return null;
    }

    /**
     * 查询分钟记录
     *
     * @param instId  仪器ID
     * @param pointId 测点ID
     * @param attrId  分量ID
     * @param direct  方向
     * @param start   开始时间
     * @param end     结束时间
     * @return {@link List }<{@link Double }>
     */
    public List<Double> queryMinuteRecord(Integer instId, Integer pointId, Integer attrId, Integer direct, Date start, Date end) {
        String rate = "分钟";
        String tableName = DataServiceUtil.buildTableName(instId, rate, pointId);
        String colName = tdService.enColName(attrId, direct);
        String startTime = TimeUtil.format2Second(start);
        String endTime = TimeUtil.format2Second(end);
        String sql = "select " + colName + " from " + tableName + " where ts >= '" + startTime + "' and ts < '" + endTime + "' order by ts desc ";
        List<Map<String, Object>> records = tdBaseService.selectMulti(sql, rate);
        if (records == null || records.isEmpty()) {
            return Collections.emptyList();
        }
        return records.stream().map(e -> {
            if (e == null || e.get(colName) == null
                    || e.get(colName).toString().isEmpty()
                    || e.get(colName).toString().equals("null")) {
                return null;
            } else {
                return Double.parseDouble(e.get(colName).toString());
            }
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 告警规则延迟双重检查缓存
     */
    private final com.google.common.cache.Cache<Integer, AlarmDdcRuleCommon> ALARM_DDC_RULES = CacheBuilder.newBuilder()
            .expireAfterWrite(10, TimeUnit.SECONDS)
            .build();

    /**
     * 获取告警规则延迟双重检查
     *
     * @param fieldNum 风场编号
     * @param pointId  测点
     * @return {@link AlarmDdcRuleCommon }
     */
    public synchronized AlarmDdcRuleCommon getAlarmDdcRule(String fieldNum, Integer pointId) {
        /*  先读取本地缓存  */
        AlarmDdcRuleCommon rule = ALARM_DDC_RULES.getIfPresent(pointId);
        if (rule != null) {
            return rule;
        }

        /*  未命中加载Redis  */
        String key = RedisKey.ALARM_RULE_DDC + fieldNum;
        rule = (AlarmDdcRuleCommon) redisTemplate.opsForHash().get(key, pointId);
        if (rule != null) {
            ALARM_DDC_RULES.put(pointId, rule);
            return rule;
        }

        /*  未命中Redis加载http  */
        List<AlarmDdcRuleCommon> rules = monitorService.getAlarmDdcRules(fieldNum);
        if (rules == null || rules.isEmpty()) {
            /*  防止未配置导致的缓存穿透  */
            rule = new AlarmDdcRuleCommon(false);
            redisTemplate.opsForHash().put(key, pointId, rule);
            redisTemplate.expire(key, 1, TimeUnit.MINUTES);
            ALARM_DDC_RULES.put(pointId, rule);
        } else {
            Map<Integer, AlarmDdcRuleCommon> ruleMap = rules.stream().collect(Collectors.toMap(AlarmDdcRuleCommon::getPointId, e -> e));
            redisTemplate.opsForHash().putAll(key, ruleMap);
            redisTemplate.expire(key, 1, TimeUnit.MINUTES);
            ruleMap.forEach(ALARM_DDC_RULES::put);
            rule = ruleMap.get(pointId);
            /*  防止未配置导致的缓存穿透  */
            if (rule == null) {
                rule = new AlarmDdcRuleCommon(false);
                redisTemplate.opsForHash().put(key, pointId, rule);
                ALARM_DDC_RULES.put(pointId, rule);
            }
        }
        return rule;
    }
}
