package com.hdec.data.qo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * 自定义变量查询类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class VarQo {

    /** 名称 */
    private String name;

    /** 测点编号 */
    private List<Integer> pointIds;

    /** 风场编码 */
    private String  fieldNum;

    public VarQo(String name, List<Integer> pointIds) {
        this.name = name;
        this.pointIds = pointIds;
    }
}
