package com.hdec.data.qo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class AttrStatJsonQo {

    /** 海上设施类型 */
    private String seaType;

    /** 分量ID */
    private Integer attrId;

    /** 方向 */
    private String direct;

    /** 统计项 */
    private String statItem;

    /**
     * 测点ID
     * 仅在用户选择直接选择测点统计数据时使用
     */
    private Integer pointId;

}
