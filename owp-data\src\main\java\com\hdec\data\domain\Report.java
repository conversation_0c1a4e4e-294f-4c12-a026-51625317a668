package com.hdec.data.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * 报告实体类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class Report {

    /** 主键 */
    private Integer id;

    /** 主键 */
    private String type;

    /** 主键 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone="GMT+8")
    private Date periodStart;

    /** 主键 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone="GMT+8")
    private Date periodEnd;

    /** 哪一年 */
    private Integer issueYear;

    /** 第几期 */
    private Integer issueNum;

    /** 主键 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone="GMT+8")
    private Date genTime;

    /** 主键 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone="GMT+8")
    private Date writeTime;

    /** 主键 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone="GMT+8")
    private Date finalTime;

    /** 编写者 */
    private String editor;

    /** 校对者 */
    private String checker;

    /** 审核者 */
    private String reviewer;

    /** 主键 */
    private String version;

    /** 主键 */
    private String status;

    private String filepath;

    private String pdfPath;

    /** 风场编码 */
    private String fieldNum;

    /** 风场名称 */
    private String fieldName;

    public Report(String type, Date periodStart, Date periodEnd, Integer issueYear, Integer issueNum, Date genTime, Date writeTime, Date finalTime, String editor, String checker, String reviewer, String version, String status, String fieldNum) {
        this.type = type;
        this.periodStart = periodStart;
        this.periodEnd = periodEnd;
        this.issueYear = issueYear;
        this.issueNum = issueNum;
        this.genTime = genTime;
        this.writeTime = writeTime;
        this.finalTime = finalTime;
        this.editor = editor;
        this.checker = checker;
        this.reviewer = reviewer;
        this.version = version;
        this.status = status;
        this.fieldNum = fieldNum;
    }
}
