package com.hdec.data.netty;

import com.hdec.data.util.HexUtil;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.handler.codec.DelimiterBasedFrameDecoder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Netty服务端
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class NettyServerForDynamicV2DataHis {

    /**
     * 启动服务
     */
    public void startServer() throws InterruptedException {
        /* 创建两个NIO线程组，一个用于接收客户端连接，一个用于通道的读写 */
        EventLoopGroup bossGroup = new NioEventLoopGroup(1);
        EventLoopGroup workGroup = new NioEventLoopGroup();

        /* 设置配置信息 */
        ServerBootstrap bootstrap = new ServerBootstrap();
        bootstrap.group(bossGroup, workGroup)
                .channel(NioServerSocketChannel.class)
                .childHandler(new ChannelInitializer<SocketChannel>() {
                    @Override
                    protected void initChannel(SocketChannel socketChannel) {
                        socketChannel.pipeline().addLast(new DelimiterBasedFrameDecoder(2000, false, Unpooled.copiedBuffer(HexUtil.hexToBytes("0D0A0D0A"))));
                        socketChannel.pipeline().addLast(new NettyHandlerDynamicV2HisData());
                    }
                });
        try {
            /* 同步绑定端口，绑定成功后才返回 */
            ChannelFuture future = bootstrap.bind(9105).sync();
            log.info("动态V2启动，历史数据端口：{}", 9105);

            /* 同步阻塞，等待服务器链路关闭后才结束main方法 */
            future.channel().closeFuture().sync();
        } finally {
            /* 优雅退出，释放线程池资源 */
            bossGroup.shutdownGracefully();
            workGroup.shutdownGracefully();
        }
    }

}
