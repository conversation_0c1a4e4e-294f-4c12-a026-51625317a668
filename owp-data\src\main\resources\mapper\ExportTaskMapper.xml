<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hdec.data.mapper.ExportTaskMapper">

    <select id="page" resultType="com.hdec.data.domain.v2.ExportTask">
        select *
        from owp_export_task_v2
        <where>
            <if test="task.status != null">
                and status = #{task.status}
            </if>
            <if test="task.startTime != null and task.endTime != null">
                and create_time between #{task.startTime} and #{task.endTime}
            </if>
            <if test="task.rate!= null and task.rate!= ''">
                and rate = #{task.rate}
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="selectNextTask" resultType="com.hdec.data.domain.v2.ExportTask">
        select *
        from owp_export_task_v2
        where status = '0'
        order by create_time asc
        limit 1
    </select>

    <select id="selectById" parameterType="int" resultType="com.hdec.data.domain.v2.ExportTask">
        select *
        from owp_export_task_v2
        where id = #{id}
    </select>

    <insert id="insert" parameterType="com.hdec.data.domain.v2.ExportTask">
        insert into owp_export_task_v2
        (point_ids,attr_ids, rate, start_time, end_time, progress, url, status, field_num, creator, create_by, create_time)
        values (#{pointIds},#{attrIds}, #{rate}, #{startTime}, #{endTime}, #{progress}, #{url}, #{status}, #{fieldNum}, #{creator},
                #{createBy}, now())
    </insert>

    <delete id="delete" parameterType="int">
        delete from owp_export_task_v2
        where id = #{id}
    </delete>

    <update id="updateProgress">
        update owp_export_task_v2
        set progress = #{progress}
        where id = #{id}
    </update>

    <update id="updateUrl">
        update owp_export_task_v2
        set url = #{url}
        where id = #{id}
    </update>

    <update id="updateStatus">
        update owp_export_task_v2
        set status = #{status},
            description = #{description}
        where id = #{id}
    </update>

    <update id="exportStartUpdate">
        update owp_export_task_v2
        set status = 1,
            description = '执行中',
            export_start_time = now()
        where id = #{id}
    </update>

    <update id="exportEndUpdate">
        update owp_export_task_v2
        set status = 2,
            description = '完成',
            export_end_time = now()
        where id = #{id}
    </update>

</mapper>