package com.hdec.data.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class RedoJob implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 测点
     */
    private Integer point;

    /**
     * 时间
     */
    private Date ts;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    public RedoJob(Integer point, Date ts, Integer status) {
        this.point = point;
        this.ts = ts;
        this.status = status;
    }
}
