package com.hdec.data.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * 数据录入实体类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class Import {

    /** 自增主键 */
    private Integer id;

    /** 导入时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date time;

    /** 导入方式（1：预置格式录入  2：Excel表格录入） */
    private Integer type;

    /** 是否是批量录入 */
    private Boolean isBatch = false;

    /** 频率 */
    private String rate;

    /** 是否是单向 */
    private Boolean isOneWay;

    /** 是否覆盖历史数据 */
    private Boolean isOverride;

    /** 是否根据Sheet名称自动识别测点名称 */
    private Boolean isAutoPointName;

    /** 匹配关系 */
    private String mappings;

    /** 导入状态（0：进行中  1：已完成） */
    private Boolean status;

    /** 文件名 */
    private String filename;

    /** 原始文件名 */
    private String originalName;

    /** 文件路径 */
    private String filepath;

    /** 用户ID */
    private String userId;

    /** 用户名 */
    private String userName;

    private Double process;

    /** 风场编码 */
    private String fieldNum;

    /** 导入结果 */
    private String importRes;

    /** 收藏匹配关系 */
    private String savedMapping;

    /**
     * 仪器ID
     */
    private Integer instId;

    private List<ImportUrl> importUrls;

    public Import(String fieldNum) {
        this.fieldNum = fieldNum;
    }

    public Import(Integer id, String filepath) {
        this.id = id;
        this.filepath = filepath;
    }

    public Import(Integer type, String rate, Boolean isOverride, Boolean isAutoPointName, Boolean isOneWay, String mappings, Boolean status,
                  String filename, String filepath, String userId, String userName, String fieldNum, String savedMapping) {
        this.type = type;
        this.rate = rate;
        this.isOverride = isOverride;
        this.isAutoPointName = isAutoPointName;
        this.isOneWay = isOneWay;
        this.mappings = mappings;
        this.status = status;
        this.filename = filename;
        this.filepath = filepath;
        this.userId = userId;
        this.userName = userName;
        this.fieldNum = fieldNum;
        this.savedMapping = savedMapping;
    }

    public Import(Boolean isBatch, String rate, Integer type, Boolean isOverride, String filename, String originalName, Boolean status, String userId, String userName, String fieldNum) {
        this.isBatch = isBatch;
        this.rate = rate;
        this.type = type;
        this.isOverride = isOverride;
        this.filename = filename;
        this.originalName = originalName;
        this.status = status;
        this.userId = userId;
        this.userName = userName;
        this.fieldNum = fieldNum;
    }
}
