package com.hdec.data.config;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 本地缓存配置类
 */
@Configuration
public class CaffeineConfig {

    @Bean
    public Cache caffeineCache(){
        return Caffeine.newBuilder()
                // 过期时间
                .expireAfter(new CaffeineExpiry())
                // 初始容量和最大容量
                .initialCapacity(10)
                .maximumSize(2000)
                .build();
    }

}

