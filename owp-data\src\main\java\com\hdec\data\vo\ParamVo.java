package com.hdec.data.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/8/15
 */
@Data
@ToString
@NoArgsConstructor
public class ParamVo {

    /** 引用编号ID */
    private String id;

    private Integer refMouldId;

    /** 引用对象ID */
    private String refId;

    /** 引用对象内容 */
    private String refContent;

    public ParamVo(String id, String refId) {
        this.id = id;
        this.refId = refId;
    }

    public ParamVo(String id, String refId, String refContent) {
        this.id = id;
        this.refId = refId;
        this.refContent = refContent;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ParamVo paramVo = (ParamVo) o;
        return id.equals(paramVo.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
}
