package com.hdec.data.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * 数据实体类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class Datum {

    /** 自增主键 */
    private Integer id;

    /** 测点ID */
    private Integer pointId;

    /** 方向 */
    private Integer direction;

    /** 时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone="GMT+8")
    private Date time;

    private Boolean verify;

    /** 分量ID */
    private Integer attrId;

    /** 分量值 */
    private Float attrVal;

    private String attrName;

    public Datum(Integer pointId, Integer direction, Date time, Integer attrId, Float attrVal) {
        this.pointId = pointId;
        this.direction = direction;
        this.time = time;
        this.attrId = attrId;
        this.attrVal = attrVal;
    }

    public Datum(Integer direction, Date time, Integer attrId, Float attrVal) {
        this.direction = direction;
        this.time = time;
        this.attrId = attrId;
        this.attrVal = attrVal;
    }

}
