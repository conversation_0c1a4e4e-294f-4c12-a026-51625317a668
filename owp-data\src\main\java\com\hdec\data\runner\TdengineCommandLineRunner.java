package com.hdec.data.runner;

import com.hdec.common.constant.Constant;
import com.hdec.common.domain.AttrCommon;
import com.hdec.common.domain.InstCommon;
import com.hdec.common.domain.ProResourceCommon;
import com.hdec.common.vo.Direct;
import com.hdec.data.feign.MonitorService;
import com.hdec.data.feign.WindService;
import com.hdec.data.service.TaosService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Order(1)
public class TdengineCommandLineRunner implements CommandLineRunner {
    private static final Logger logger = LoggerFactory.getLogger(TdengineCommandLineRunner.class);

    @Resource
    private TaosService taosService;

    @Resource
    private WindService windService;
    @Resource
    private MonitorService monitorService;

    @Override
    public void run(String... args) throws Exception {
        /*  初始化数据库  */
        taosService.createOwpDatabase();
        logger.info("创建[owp]库完成");
        taosService.createOwpHighDatabase();
        logger.info("创建[owp_high]库完成");
        taosService.createOwpJobDatabase();
        logger.info("创建[owp_job]库完成");

        /*  创建重做任务表  */
        taosService.createRedoJobStable();
        logger.info("创建[重做任务]超级表完成");

        /*  初始化任务表  */
        taosService.createDailyCountJobStable();
        logger.info("创建[日统计]超级表完成");

        /*  初始化超级表  */
        List<ProResourceCommon> resources = windService.allFiled();
        resources.forEach(resource -> {
            String fieldNum = resource.getFieldNum();
            List<InstCommon> instList = monitorService.allInstByField(fieldNum);
            List<AttrCommon> attrs = monitorService.allAttrByField(fieldNum);
            instList.forEach(inst -> {
                Integer instId = inst.getId();
                String direct = monitorService.getInstDirectById(instId);
                Integer[] directs = transDirect(direct).stream()
                        .map(Direct::getDirectId)
                        .toArray(Integer[]::new);

                /*  初始化高频超级表  */
                List<AttrCommon> highAttrs = attrs.stream().filter(e -> Objects.equals(instId, e.getInstId())
                                && Objects.equals(Constant.RATE_HIGH, e.getRate()))
                        .collect(Collectors.toList());
                taosService.initializeHighSTable(instId, highAttrs, directs);
                /*  初始化抽稀超级表  */
                taosService.initializeHighSampleSTable(instId, highAttrs, directs);
                /*  初始化非高频超级表  */
                List<AttrCommon> normalAttrs = attrs.stream().filter(e -> Objects.equals(instId, e.getInstId())
                                && !Objects.equals(Constant.RATE_HIGH, e.getRate()))
                        .collect(Collectors.toList());
                taosService.initializeNormalSTable(instId, normalAttrs, directs);
            });
        });
    }

    private List<Direct> transDirect(String direct) {
        List<Direct> directs = new ArrayList<>();
        if ("双向".equals(direct)) {
            directs.add(new Direct(1, "X"));
            directs.add(new Direct(2, "Y"));
        } else if ("三向".equals(direct)) {
            directs.add(new Direct(1, "X"));
            directs.add(new Direct(2, "Y"));
            directs.add(new Direct(3, "Z"));
        } else {
            directs.add(new Direct(1, "X"));
        }
        return directs;
    }
}
