package com.hdec.data.service.impl;

import com.hdec.common.domain.CollectInstMountCommon;
import com.hdec.common.formula.AttrVal;
import com.hdec.common.util.TimeUtil;
import com.hdec.data.cache.LocalCacheManager;
import com.hdec.data.domain.AttrIdVal;
import com.hdec.data.domain.InsertRecord;
import com.hdec.data.service.AgentService;
import com.hdec.data.service.DataAccessService;
import com.hdec.data.vo.BkgLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class AgentServiceImpl implements AgentService {
    private static final Logger logger = LoggerFactory.getLogger(AgentServiceImpl.class);

    private static final String BKG_LOGGER_DEVICE_TYPE = "agent";

    @Resource
    private LocalCacheManager localCacheManager;

    @Resource
    private DataAccessService accessService;

    @Override
    public void bkgLogger(List<BkgLogger> loggers) {
        if (loggers == null || loggers.isEmpty()) {
            return;
        }
        List<AttrVal> values = new ArrayList<>();
        /*  根据采集仪器分组  */
        Map<String, List<BkgLogger>> deviceGroup = loggers.stream().collect(Collectors.groupingBy(BkgLogger::getDevNo));
        deviceGroup.forEach((devNo, devList) -> {
            /*  对时间进行分类  */
            Map<Date, List<BkgLogger>> timeGroup = devList.stream().collect(Collectors.groupingBy(BkgLogger::getDataTime));
            /*  根据通道挂载信息处理数据  */
            timeGroup.forEach((time, timeList) -> {
                Map<Integer, List<BkgLogger>> channelGroup = timeList.stream().collect(Collectors.groupingBy(BkgLogger::getChannel));
                channelGroup.forEach((channel, channelList) -> {
                    /*  处理数据  */
                    CollectInstMountCommon mount = localCacheManager.getMountCache(BKG_LOGGER_DEVICE_TYPE, devNo, channel);
                    if (mount == null) {
                        logger.warn("AGENT-未找到采集仪器挂载信息，devNo: {}, time: {}, channel: {}", devNo, time, channel);
                        return;
                    }
                    Integer instId = mount.getInstId();
                    Integer pointId = mount.getPointId();
                    Integer direct = mount.getDirect();
                    Integer[] attrIds = mount.getAttrIds();
                    String[] attrRates = mount.getAttrRates();
                    if (attrIds == null || attrRates == null) {
                        logger.warn("AGENT-未找到采集仪属性信息，devNo: {}, time: {}, channel: {}", devNo, time, channel);
                        return;
                    }
                    if (attrIds.length != attrRates.length) {
                        logger.warn("AGENT-采集仪属性信息长度不一致，devNo: {}, time: {}, channel: {}", devNo, time, channel);
                        return;
                    }
                    channelList.forEach(data -> {
                        for (int i = 0; i < attrIds.length; i++) {
                            if (attrIds[i] != null) {
                                String val = null;
                                switch (i) {
                                    case 0: {
                                        val = data.getS1();
                                        break;
                                    }
                                    case 1: {
                                        val = data.getS2();
                                        break;
                                    }
                                    case 2: {
                                        val = data.getR1();
                                        break;
                                    }
                                    case 3: {
                                        val = data.getR2();
                                        break;
                                    }
                                }
                                if (val == null || val.trim().isEmpty()) {
                                    continue;
                                }
                                Double value = Double.parseDouble(val);
                                AttrVal attrVal = new AttrVal(time, instId, pointId, attrIds[i], direct, attrRates[i], value);
                                values.add(attrVal);
                            }
                        }
                    });
                });
            });
        });
        if (!values.isEmpty()) {
            accessService.processRecord(values, false);
        }
    }

    private InsertRecord toRecord(Integer instId, Integer pointId, Integer direct,
                                  Integer attrId, String attrRate, Date time, Float value) {
        Set<AttrIdVal> attrValues = new HashSet<>(1);
        AttrIdVal attrIdVal = new AttrIdVal(attrId, value);
        attrValues.add(attrIdVal);
        return new InsertRecord(instId, TimeUtil.format2Ms(time), pointId, direct, attrIdVal, attrValues, attrRate);
    }
}
