package com.hdec.data.vo;

import com.hdec.data.domain.Task;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * 任务列表Vo类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class TaskVo {

    /** 进行中数量 */
    private Integer progressNum;

    /** 已完成数量 */
    private Integer completedNum;

    /** 失败数量 */
    private Integer failedNum;

    /** 任务列表 */
    private List<Task> tasks;

    public TaskVo(Integer progressNum, Integer completedNum, Integer failedNum) {
        this.progressNum = progressNum;
        this.completedNum = completedNum;
        this.failedNum = failedNum;
    }

    public TaskVo(Integer progressNum, Integer completedNum, Integer failedNum, List<Task> tasks) {
        this.progressNum = progressNum;
        this.completedNum = completedNum;
        this.failedNum = failedNum;
        this.tasks = tasks;
    }
}
