<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hdec.data.mapper.ReportMapper">

    <select id="proList" resultType="com.hdec.data.domain.Report">
        SELECT
            *
        FROM
            owp_report
        WHERE
            field_num = #{fieldNum}
        <if test="qo.monitorStartTime != null and qo.monitorStartTime != '' and qo.monitorEndTime != null and qo.monitorEndTime != ''">
            AND `period_start` &gt;= #{qo.monitorStartTime} and `period_end` &lt;= #{qo.monitorEndTime}
        </if>
        <if test="qo.genStartTime != null and qo.genStartTime != '' and qo.genEndTime != null and qo.genEndTime != ''">
            AND `gen_time` between #{qo.genStartTime} and #{qo.genEndTime}
        </if>
        <if test="qo.writeStartTime != null and qo.writeStartTime != '' and qo.writeEndTime != null and qo.writeEndTime != ''">
            AND `write_time` between #{qo.writeStartTime} and #{qo.writeEndTime}
        </if>
        <if test="qo.finalStartTime != null and qo.finalStartTime != '' and qo.finalEndTime != null and qo.finalEndTime != ''">
            AND `final_time` between #{qo.finalStartTime} and #{qo.finalEndTime}
        </if>
        <if test="qo.version != null and qo.version != ''">
            AND `version` = #{qo.version}
        </if>
        order by id desc
    </select>

    <select id="entList" resultType="com.hdec.data.domain.Report">
        SELECT
            *
        FROM
            owp_report
        WHERE
            `status` = '已完成'
        AND field_num IN
            <foreach item="fieldNum" collection="fieldNums" open="(" separator="," close=")">
                #{fieldNum}
            </foreach>
        <if test="qo.monitorStartTime != null and qo.monitorStartTime != '' and qo.monitorEndTime != null and qo.monitorEndTime != ''">
            AND `period_start` between #{qo.monitorStartTime} and #{qo.monitorEndTime}
        </if>
        <if test="qo.writeStartTime != null and qo.writeStartTime != '' and qo.writeEndTime != null and qo.writeEndTime != ''">
            AND `write_time` between #{qo.writeStartTime} and #{qo.writeEndTime}
        </if>
        <if test="qo.version != null and qo.version != ''">
            AND `version` = #{qo.version}
        </if>
        order by id desc
    </select>

    <insert id="save" parameterType="com.hdec.data.domain.Report" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO owp_report (
            `type`,
            period_start,
            period_end,
            issue_year,
            issue_num,
            gen_time,
            write_time,
            final_time,
            editor,
            checker,
            reviewer,
            version,
            `status`,
            field_num
        )
        VALUES (
            #{report.type},
            #{report.periodStart},
            #{report.periodEnd},
            #{report.issueYear},
            #{report.issueNum},
            #{report.genTime},
            #{report.writeTime},
            #{report.finalTime},
            #{report.editor},
            #{report.checker},
            #{report.reviewer},
            #{report.version},
            #{report.status},
            #{report.fieldNum}
        )
    </insert>

    <update id="updatePath">
        UPDATE owp_report
        SET `status` = #{status},
            `filepath` = #{filepath},
            `pdf_path` = #{pdfPath}
        WHERE
            id = #{reportId}
    </update>

    <update id="updateStatus">
        UPDATE owp_report
        SET `status` = #{status}
        WHERE
            id = #{reportId}
    </update>

    <update id="finalReport">
        UPDATE owp_report
        SET `final_time` = NOW(),
            `filepath` = #{filepath},
            `version` = '最终版'
        WHERE
            id = #{reportId}
    </update>

    <delete id="delete">
        DELETE
        FROM
            owp_report
        WHERE
            id IN
            <foreach collection="ids" item="id" separator=", " open="(" close=")">
                #{id}
            </foreach>
    </delete>

    <select id="entAllIssueNums" resultType="string">
        SELECT DISTINCT
	        issue_num
        FROM
            owp_report
        WHERE
            `status` = '已完成'
        AND field_num IN
        <foreach item="fieldNum" collection="fieldNums" open="(" separator="," close=")">
            #{fieldNum}
        </foreach>
    </select>

    <select id="proIssueNumByYear" resultType="int">
        SELECT
            count(*) + 1
        FROM
            owp_report
        WHERE
            field_num = #{fieldNum}
        AND issue_year = #{issueYear}
    </select>

</mapper>