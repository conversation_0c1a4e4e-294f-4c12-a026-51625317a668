package com.hdec.data.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/2
 */
@Data
@ToString
@NoArgsConstructor
public class TabResVo {

    private String jsonParams;

    private List<List<Td>> headerTrs;

    private List<List<Td>> bodyTrs;

    public TabResVo(String jsonParams, List<List<Td>> headerTrs, List<List<Td>> bodyTrs) {
        this.jsonParams = jsonParams;
        this.headerTrs = headerTrs;
        this.bodyTrs = bodyTrs;
    }

}
