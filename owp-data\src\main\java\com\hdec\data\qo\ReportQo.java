package com.hdec.data.qo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * 报告查询类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class ReportQo {

    /** 项目名称 */
    private String projectName;

    /** 当前页 */
    private Integer pageNum;

    /** 每页条数 */
    private Integer pageSize;

    /** 监测日期 */
    private String monitorStartTime;
    /** 监测日期 */
    private String monitorEndTime;

    /** 生成日期 */
    private String genStartTime;
    /** 生成日期 */
    private String genEndTime;

    /** 编写日期 */
    private String writeStartTime;
    /** 编写日期 */
    private String writeEndTime;

    /** 最终稿日期 */
    private String finalStartTime;
    /** 最终稿日期 */
    private String finalEndTime;

    /** 报告版本 */
    private String version;

    /** 项目名称 */
    private String fieldName;

    /** 期数 */
    private String issueNum;

}
