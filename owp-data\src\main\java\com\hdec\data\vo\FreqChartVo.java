package com.hdec.data.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * 频谱分析甘特图结果类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class FreqChartVo {

    /** 测点编号 */
    private String pointNo;

    /** 时间段 */
    private List<TimeVo> timeVos;

    public FreqChartVo(String pointNo, List<TimeVo> timeVos) {
        this.pointNo = pointNo;
        this.timeVos = timeVos;
    }
}
