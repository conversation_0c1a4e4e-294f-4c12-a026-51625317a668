package com.hdec.data.service;

import com.hdec.data.domain.DiagramVar;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 过程线图变量解析
 *
 * <AUTHOR>
 */
@Service
public class DiagramVarParseService extends VarParseBase {

    /** 参数长度 */
    private static final int VAR_LEN = 7;

    /**
     * 解析字符串变量为实体类
     */
    public DiagramVar parseVar2Bean(String fieldNum, String var) throws Exception {
        String[] arr = var.split("/");
        if (arr.length != VAR_LEN) {
            throw new Exception("非法参数");
        }

        List<String> fanIds = super.getSeaFacilityIds(fieldNum, arr[0]);
        Boolean isDoubleAxis = getIsDoubleAxis(arr[1]);
        int pointNum = Integer.parseInt(arr[2]);
        Boolean isDirectDivided = Boolean.parseBoolean(arr[3]);
        int occupyWidth = getOccupyWidth(arr[4]);
        List<String> attrNames = getAttrNames(arr[5], isDoubleAxis);
        Integer direct = super.getDirect(arr[6]);
        return new DiagramVar(fanIds, isDoubleAxis, pointNum, isDirectDivided, occupyWidth, attrNames, direct);
    }

    /**
     * 解析分量名
     */
    private List<String> getAttrNames(String s, Boolean isDoubleAxis) throws Exception {
        String[] arr = s.split("&");
        // 非法校验
        if ((!isDoubleAxis && arr.length != 1) || (isDoubleAxis && arr.length != 2)) {
            throw new Exception("非法参数");
        }

        List<String> attrNames = new ArrayList<>();
        if (isDoubleAxis) {
            attrNames.add(arr[0]);
            attrNames.add(arr[1]);
        } else {
            attrNames.add(arr[0]);
        }
        return attrNames;
    }

    /**
     * 所占宽度
     */
    private int getOccupyWidth(String s) throws Exception {
        if (s.contains("整行")) {
            return 1;
        }
        if (s.contains("一半")) {
            return 2;
        }
        if (s.contains("三分之一")) {
            return 3;
        }
        if (s.contains("四分之一")) {
            return 4;
        }
        throw new Exception("非法参数");
    }
    /**
     * 是否是双坐标轴
     */
    private Boolean getIsDoubleAxis(String s) throws Exception {
        if ("单纵坐标".equals(s)) {
            return true;
        }
        if ("双纵坐标".equals(s)) {
            return true;
        }
        throw new Exception("非法参数");
    }


}
