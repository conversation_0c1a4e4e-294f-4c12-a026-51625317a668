package com.hdec.data.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * 测值评判Vo类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class ValueJudgeVo {

    /** 测点编号 */
    private String pointNo;

    /** 方向 */
    private Integer directId;

    /** 方向 */
    private String direct;

    /** 异常率 */
    private String abnormalRate;

    /** 异常率描述 */
    private String abnormalRateDesc;

    /** 缺测率 */
    private String missRate;

    /** 缺测时长 */
    private String missLength;

    /** 最近一天是否完全缺测 */
    private Boolean isAllMissLastDay = false;

    private List<TimeVo> timeVos;

    public ValueJudgeVo(String pointNo, String direct, String abnormalRate, String missRate) {
        this.pointNo = pointNo;
        this.direct = direct;
        this.abnormalRate = abnormalRate;
        this.missRate = missRate;
    }

    public ValueJudgeVo(String pointNo, String direct, String abnormalRate, String missRate, String missLength) {
        this.pointNo = pointNo;
        this.direct = direct;
        this.abnormalRate = abnormalRate;
        this.missRate = missRate;
        this.missLength = missLength;
    }

    public ValueJudgeVo(String pointNo, Integer directId, String direct, String abnormalRate, String missRate, String missLength, List<TimeVo> timeVos, Boolean isAllMissLastDay) {
        this.pointNo = pointNo;
        this.directId = directId;
        this.direct = direct;
        this.abnormalRate = abnormalRate;
        this.missRate = missRate;
        this.missLength = missLength;
        this.timeVos = timeVos;
        this.isAllMissLastDay = isAllMissLastDay;
    }

    public ValueJudgeVo(String pointNo, Integer directId, String direct, String abnormalRate, String abnormalRateDesc, String missRate, String missLength, List<TimeVo> timeVos, Boolean isAllMissLastDay) {
        this.pointNo = pointNo;
        this.directId = directId;
        this.direct = direct;
        this.abnormalRate = abnormalRate;
        this.abnormalRateDesc = abnormalRateDesc;
        this.missRate = missRate;
        this.missLength = missLength;
        this.timeVos = timeVos;
        this.isAllMissLastDay = isAllMissLastDay;
    }
}
