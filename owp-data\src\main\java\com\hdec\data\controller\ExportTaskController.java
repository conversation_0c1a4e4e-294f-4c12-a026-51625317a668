package com.hdec.data.controller;

import com.github.pagehelper.PageInfo;
import com.hdec.common.domain.R;
import com.hdec.common.domain.ResourceCommon;
import com.hdec.common.util.TimeUtil;
import com.hdec.data.domain.v2.ExportTask;
import com.hdec.data.qo.AuthQo;
import com.hdec.data.service.ExportTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Api(tags = "数据导出任务")
@Slf4j
@Validated
@RestController
@RequestMapping("api/data/export/task")
public class ExportTaskController {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private ExportTaskService exportTaskService;

    @ApiOperation("校验密码")
    @PostMapping("auth")
    public R auth(@NotBlank(message = "风场编码不允许为空") @RequestHeader("fieldNum") String fieldNum,
                  @RequestBody @Valid AuthQo qo) {
        boolean auth = exportTaskService.secretVerify(fieldNum, qo.getSecret(), qo.getSalt());
        return R.success(auth);
    }

    @ApiOperation("查询")
    @GetMapping("page")
    public R page(@NotBlank(message = "风场编码不允许为空") @RequestHeader("fieldNum") String fieldNum,
                  @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                  @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                  @ModelAttribute ExportTask task) {
        if (task != null && task.getEndTime() != null) {
            task.setEndTime(TimeUtil.addDays(task.getEndTime(), 1));
        }
        PageInfo<ExportTask> page = exportTaskService.page(fieldNum, pageNum, pageSize, task);
        return R.success(page);
    }

    @ApiOperation("添加")
    @PostMapping("add")
    public R add(@NotBlank(message = "风场编码不允许为空") @RequestHeader("fieldNum") String fieldNum,
                 @RequestHeader(value = "sessionId") String sessionId,
                 @RequestBody @Valid ExportTask task) throws Exception {
        ResourceCommon resource = (ResourceCommon) redisTemplate.opsForValue().get(sessionId);
        if (resource == null) {
            return R.error("用户未登录");
        }
        task.setEndTime(TimeUtil.addDays(task.getEndTime(), 1));
        task.setCreateBy(resource.getUserId());
        task.setCreator(resource.getUsername());
        task.setFieldNum(fieldNum);
        try {
            exportTaskService.addTask(task);
        } catch (Exception e) {
            return R.error(e.getMessage());
        }
        return R.success();
    }

    @ApiOperation("删除")
    @DeleteMapping("{id}")
    public R delete(@PathVariable("id") @NotNull Integer id) throws Exception {
        exportTaskService.deleteTask(id);
        return R.success();
    }

    @ApiOperation("下载")
    @GetMapping("download/{id}")
    public void download(@PathVariable("id") @NotNull Integer id, HttpServletResponse response) throws Exception {
        try {
            exportTaskService.download(id, response);
        } catch (Exception e) {
            log.error("下载文件失败", e);
        }
    }

    @ApiOperation("删除文件")
    @DeleteMapping("file/{id}")
    public R deleteFile(@PathVariable("id") @NotNull Integer id) throws Exception {
        exportTaskService.deleteFile(id);
        return R.success();
    }
}
