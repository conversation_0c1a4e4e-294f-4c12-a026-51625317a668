package com.hdec.data.netty;

import cn.hutool.core.util.ByteUtil;
import com.hdec.common.domain.msg.Inst;
import com.hdec.data.service.DataAccessService;
import com.hdec.data.service.KafkaService;
import com.hdec.data.util.HexUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

/**
 * DM客户端消息处理类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class NettyHandlerRadar extends ChannelInboundHandlerAdapter {

    private static NettyHandlerRadar handler;

    @PostConstruct
    public void init() {
        handler = this;
    }

    @Autowired
    private KafkaService kafkaService;

    @Resource
    private DataAccessService accessService;

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) {
        ByteBuf byteBuf = (ByteBuf) msg;
        byte[] bytes = new byte[byteBuf.readableBytes()];
        byteBuf.readBytes(bytes);
        byteBuf.release();

        String hex = HexUtil.bytesToHex(bytes, true);
        if (hex.toLowerCase().startsWith("eb94")) {
            List<Inst> insts = parseRateLow(ctx, bytes);
//            handler.kafkaService.consume(insts, "radar");
            handler.accessService.input(insts, "radar");
        } else if (hex.toLowerCase().startsWith("eb93")) {
            List<Inst> insts = parseRateHigh(ctx, bytes);
//            handler.kafkaService.consume(insts, "radarHz");
            handler.accessService.input(insts, "radarHz");
        }
    }

    /**
     * 低频数据（十分钟潮汐、有效波高、十分之一大波、五十分之一大波、有效周期、波向）
     */
    private static List<Inst> parseRateLow(ChannelHandlerContext ctx, byte[] bytes) {
        int deviceNo = ByteUtil.bytesToShort(getBytesByIndex(bytes, 2, 4));

        List<Date> dates = new ArrayList<>();
        List<Float> values = new ArrayList<>();

        dates.add(new Date(ByteUtil.bytesToLong(getBytesByIndex(bytes, 8, 16))));
        values.add(Float.parseFloat(String.valueOf(ByteUtil.bytesToInt(getBytesByIndex(bytes, 16, 20)))));

        dates.add(new Date(ByteUtil.bytesToLong(getBytesByIndex(bytes, 20, 28))));
        values.add(Float.parseFloat(String.valueOf(ByteUtil.bytesToInt(getBytesByIndex(bytes, 28, 32)))));

        dates.add(new Date(ByteUtil.bytesToLong(getBytesByIndex(bytes, 32, 40))));
        values.add(Float.parseFloat(String.valueOf(ByteUtil.bytesToInt(getBytesByIndex(bytes, 40, 44)))));

        dates.add(new Date(ByteUtil.bytesToLong(getBytesByIndex(bytes, 44, 52))));
        values.add(Float.parseFloat(String.valueOf(ByteUtil.bytesToInt(getBytesByIndex(bytes, 52, 56)))));

        dates.add(new Date(ByteUtil.bytesToLong(getBytesByIndex(bytes, 56, 64))));
        values.add(Float.parseFloat(String.valueOf(ByteUtil.bytesToInt(getBytesByIndex(bytes, 64, 68)))));

        dates.add(new Date(ByteUtil.bytesToLong(getBytesByIndex(bytes, 68, 76))));
        values.add(Float.parseFloat(String.valueOf(ByteUtil.bytesToFloat(getBytesByIndex(bytes, 76, 80)))));

        List<Inst> insts = getInsts(deviceNo, dates, values);

        /* 待回复时间 */
        List<byte[]> responseTimes = new ArrayList<>();
        responseTimes.add(getBytesByIndex(bytes, 8, 16));
        responseTimes.add(getBytesByIndex(bytes, 20, 28));
        responseTimes.add(getBytesByIndex(bytes, 32, 40));
        responseTimes.add(getBytesByIndex(bytes, 44, 52));
        responseTimes.add(getBytesByIndex(bytes, 56, 64));
        responseTimes.add(getBytesByIndex(bytes, 68, 76));

        /* 回复 */
        byte[] resBytes = bytesMerge(responseTimes);
        response(ctx, resBytes);
        return insts;
    }

    private static List<Inst> getInsts(int deviceNo, List<Date> dates, List<Float> values) {
        List<Inst> insts = new ArrayList<>();

        for (int i = 0; i < dates.size(); i++) {
            Map<Integer, Float[]> channelData = new HashMap<>();
            channelData.put(0, new Float[] {values.get(i)});

            Inst inst = new Inst(String.valueOf(deviceNo), deviceNo, i, dates.get(i), channelData);
            insts.add(inst);
        }
        return insts;
    }

    /**
     * 高频数据
     */
    private static List<Inst> parseRateHigh(ChannelHandlerContext ctx, byte[] bytes) {
        int deviceNo = ByteUtil.bytesToShort(getBytesByIndex(bytes, 2, 4));

        List<Date> dates = new ArrayList<>();
        List<Float> values = new ArrayList<>();

        List<byte[]> responseTimes = new ArrayList<>();
        // A数据
        for (int i = 8; i < 120 + 8; i += 12) {
            dates.add(new Date(ByteUtil.bytesToLong(getBytesByIndex(bytes, i, i + 8))));
            values.add(Float.parseFloat(String.valueOf(ByteUtil.bytesToInt(getBytesByIndex(bytes, i + 8, i + 12)))));
            responseTimes.add(getBytesByIndex(bytes, i, i + 8));
        }

        // B数据
        for (int i = 120 + 8; i < 240 + 8; i += 12) {
            dates.add(new Date(ByteUtil.bytesToLong(getBytesByIndex(bytes, i, i + 8))));
            values.add(Float.parseFloat(String.valueOf(ByteUtil.bytesToInt(getBytesByIndex(bytes, i + 8, i + 12)))));
            responseTimes.add(getBytesByIndex(bytes, i, i + 8));
        }

        // C数据
        for (int i = 240 + 8; i < 360 + 8; i += 12) {
            dates.add(new Date(ByteUtil.bytesToLong(getBytesByIndex(bytes, i, i + 8))));
            values.add(Float.parseFloat(String.valueOf(ByteUtil.bytesToInt(getBytesByIndex(bytes, i + 8, i + 12)))));
            responseTimes.add(getBytesByIndex(bytes, i, i + 8));
        }

        List<Inst> insts = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            List<Date> dates1 = dates.subList(i * 3, i * 3 + 3);
            List<Float> values1 = values.subList(i * 3, i * 3 + 3);
            insts.addAll(getInsts(deviceNo, dates1, values1));
        }

        /* 回复 */
        byte[] resBytes = bytesMerge(responseTimes);
        response(ctx, resBytes);
        return insts;
    }

    private static void response(ChannelHandlerContext ctx, byte[] resBytes) {
        ctx.writeAndFlush(Unpooled.copiedBuffer(resBytes));
    }

    /**
     * 按下标获取子字节数组
     */
    public static byte[] getBytesByIndex(byte[] bytes, int start, int end) {
        int len = end - start;
        byte[] target = new byte[len];
        System.arraycopy(bytes, start, target, 0, len);
        return target;
    }

    /**
     * 字节数组按顺序合并
     */
    public static byte[] bytesMerge(List<byte[]> arrays) {
        int totalLength = 0;
        for (byte[] array : arrays) {
            totalLength += array.length;
        }
        byte[] result = new byte[totalLength];
        int currentIndex = 0;
        for (byte[] array : arrays) {
            System.arraycopy(array, 0, result, currentIndex, array.length);
            currentIndex += array.length;
        }
        return result;
    }

}
