package com.hdec.data.util;

/**
 * TD工具类
 *
 * <AUTHOR>
 */
public class TdUtil {

    /**
     * 解析方向到数据(双向 -> [1, 2])
     */
    public static Integer[] parseDirect2Arr(String instDirect) {
        switch (instDirect) {
            case "单向":
                return new Integer[] {1};
            case "双向":
                return new Integer[] {1, 2};
            case "三向":
                return new Integer[] {1, 2, 3};
        }
        return null;
    }

}
