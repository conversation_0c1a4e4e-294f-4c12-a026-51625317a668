package com.hdec.data.mapper;

import com.hdec.data.domain.Stat;
import com.hdec.data.qo.StatQo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 数据统计Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DataStatMapper {

    /**
     * 统计表不存在就创建
     */
    void createStatTableIfNotExist(@Param("fieldNum") String fieldNum,
                                   @Param("monitorId") Integer monitorId,
                                   @Param("attrId") Integer attrId);

    /**
     * 查询时间范围内相应测点分量的数据集
     */
    List<Stat> getStatDataByTimePointAttrRate(@Param("fieldNum") String fieldNum,
                                              @Param("startTime") String startTime, @Param("endTime") String endTime,
                                              @Param("pointIds") List<Integer> pointIds, @Param("attrId") Integer attrId,
                                              @Param("monitorId") Integer monitorId,
                                              @Param("rate") String rate);

    List<Stat> getStatFirstLastTime(@Param("fieldNum") String fieldNum,
                                    @Param("startTime") String startTime, @Param("endTime") String endTime,
                                    @Param("pointIds") List<Integer> pointIds, @Param("attrId") Integer attrId,
                                    @Param("monitorId") Integer monitorId);

    /**
     * 查询统计数据
     */
    List<Stat> select(@Param("qo") StatQo qo);

    /**
     * 按时间、测点清空统计数据
     */
    void deleteByDayPointAttr(@Param("fieldNum") String fieldNum,
                             @Param("day") String day,
                             @Param("monitorId") Integer monitorId,
                             @Param("attrId") Integer attrId,
                             @Param("pointId") Integer pointId);

    void save(@Param("fieldNum") String fieldNum, @Param("s") Stat s);

    void delBatch(@Param("fieldNum") String fieldNum, @Param("statList") List<Stat> statList);

    /**
     * 批量保存统计数据
     */
    void saveBatch(@Param("fieldNum") String fieldNum,
                   @Param("monitorId") Integer monitorId,
                   @Param("attrId") Integer attrId,
                   @Param("statList") List<Stat> statList);

    /**
     * 截断统计表数据(表存在时)
     */
    void dropStatTableIfExist(@Param("fieldNum") String fieldNum,
                              @Param("monitorId") Integer monitorId,
                              @Param("attrId") Integer attrId);

    List<String> getNames();

    /**
     * 获取某风场下所有统计表名
     */
    List<String> getStatTableNamesByField(@Param("fieldNum") String fieldNum);

    /**
     * 按名称删除表
     */
    void dropTableByName(@Param("tableName") String tableName);

    /**
     * 创建高频数量统计表
     */
    void createHighStatTableIfNotExist(@Param("fieldNum") String fieldNum);

    /**
     * 获取高频统计数量
     */
    Integer getHighStatCount(@Param("fieldNum") String fieldNum, @Param("pointIds") Integer[] pointIds, @Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 删除高频统计数量
     */
    void delHighStatCount(@Param("fieldNum") String fieldNum, @Param("pointId") Integer pointId, @Param("day") String day);

    /**
     * 保存高频统计数量
     */
    void saveHighStatCount(@Param("fieldNum") String fieldNum, @Param("pointId") Integer pointId, @Param("day") String day, @Param("count") Integer count);
}