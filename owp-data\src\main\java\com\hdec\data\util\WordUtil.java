package com.hdec.data.util;

import com.hdec.data.domain.ContentVo;
import com.spire.doc.Document;
import com.spire.doc.FileFormat;
import com.spire.doc.Section;
import com.spire.doc.documents.BreakType;
import com.spire.doc.documents.MarginsF;
import com.spire.doc.documents.Paragraph;
import com.spire.doc.documents.ParagraphStyle;
import org.springframework.util.ObjectUtils;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/7/25
 */
public class WordUtil {

    public static void html2Word(List<ContentVo> contentVos, String fileName, HttpServletResponse response) throws Exception {
        Document doc = new Document();

        /* 定义标题的样式 */
        ParagraphStyle titleStyle1 = new ParagraphStyle(doc);
        titleStyle1.setName("S1");
        titleStyle1.getCharacterFormat().setFontName("宋体");
        titleStyle1.getCharacterFormat().setFontSize(36f);
        doc.getStyles().add(titleStyle1);

        ParagraphStyle titleStyle2 = new ParagraphStyle(doc);
        titleStyle2.setName("S2");
        titleStyle2.getCharacterFormat().setFontName("宋体");
        titleStyle2.getCharacterFormat().setFontSize(30f);
        doc.getStyles().add(titleStyle2);

        ParagraphStyle titleStyle3 = new ParagraphStyle(doc);
        titleStyle3.setName("S3");
        titleStyle3.getCharacterFormat().setFontName("宋体");
        titleStyle3.getCharacterFormat().setFontSize(24f);
        doc.getStyles().add(titleStyle3);

        ParagraphStyle titleStyle4 = new ParagraphStyle(doc);
        titleStyle4.setName("S4");
        titleStyle4.getCharacterFormat().setFontName("宋体");
        titleStyle4.getCharacterFormat().setFontSize(18f);
        doc.getStyles().add(titleStyle4);

        Section section = doc.addSection();
        setPageMargin(section);

        for (ContentVo contentVo : contentVos) {
            String content = contentVo.getContent().replace("\n", "");
            if (!ObjectUtils.isEmpty(contentVo.getSeaName())) {
                content = content.replace("@{风机编号}", contentVo.getSeaName());
            }

            Paragraph paragraphTitle = section.addParagraph();
            int level = contentVo.getLevel() <= 4 ? contentVo.getLevel() : 4;
            paragraphTitle.applyStyle("S" + level);
            paragraphTitle.appendHTML(contentVo.getName());

            String[] contents = content.split("<!-- pagebreak -->");
            for (int i = 0; i < contents.length; i++) {
                if (ObjectUtils.isEmpty(contents[i])) {
                    continue;
                }
                Paragraph paragraph = section.addParagraph();
                paragraph.appendHTML(contents[i]);
                if (i != contents.length - 1) {
                    section.addParagraph().appendBreak(BreakType.Page_Break);
                }
            }
        }

        ByteArrayOutputStream os = new ByteArrayOutputStream();
        doc.saveToStream(os, FileFormat.Docx);

        InputStream is = new ByteArrayInputStream(os.toByteArray());

        //输出文件
        response.setContentType("application/msword");//导出word格式
        response.addHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes("GB2312"), "ISO8859-1") + ".docx");
        ServletOutputStream outputStream = response.getOutputStream();

        int len;
        byte []by = new byte[1024];
        while((len = is.read(by))!=-1) {
            outputStream.write(by,0,len);
        }
        outputStream.flush();
        outputStream.close();
        is.close();

    }

    private static void setPageMargin(Section section) {
        //获取第一节的页边距
        MarginsF pageMargin = section.getPageSetup().getMargins();

        //设置第一节的上下左右页边距
        pageMargin.setTop(57.9f);
        pageMargin.setBottom(57.9f);
        pageMargin.setLeft(57.9f);
        pageMargin.setRight(57.9f);
    }

    /**
     *
     * @param response
     * @param content  富文本内容
     * @param fileName 生成word名字
     * @throws Exception
     */
    public static void exportWord(HttpServletResponse response, String content, String fileName) throws Exception {

        content = content.replace("\n", "");
        try {
            //新建Document对象
            Document document = new Document();
            //添加section
            Section section = document.addSection();

            //获取第一节的页边距
            MarginsF pageMargin = section.getPageSetup().getMargins();

            //设置第一节的上下左右页边距
            pageMargin.setTop(57.9f);
            pageMargin.setBottom(57.9f);
            pageMargin.setLeft(57.9f);
            pageMargin.setRight(57.9f);

            ByteArrayOutputStream os = new ByteArrayOutputStream();
            //添加段落并写入HTML文本
            String[] contents = content.split("<!-- pagebreak -->");
            for (int i = 0; i < contents.length; i++) {
                section.addParagraph().appendHTML(contents[i]);
                if (i != contents.length - 1) {
                    section.addParagraph().appendBreak(BreakType.Page_Break);
                }
            }

            document.saveToStream(os, FileFormat.Docx);

            InputStream is = new ByteArrayInputStream(os.toByteArray());

            //输出文件
            response.setContentType("application/msword");//导出word格式
            response.addHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes("GB2312"), "ISO8859-1") + ".docx");
            ServletOutputStream outputStream = response.getOutputStream();

            int len;
            byte []by = new byte[1024];
            while((len = is.read(by))!=-1) {
                outputStream.write(by,0,len);
            }
            outputStream.flush();
            outputStream.close();
            is.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


}
