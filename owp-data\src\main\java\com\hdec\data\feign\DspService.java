package com.hdec.data.feign;

import com.hdec.common.config.FeignConfiguration;
import com.hdec.data.domain.dsp.BasicFreqProcessBody;
import com.hdec.data.domain.dsp.BasicFreqProcessResp;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * RPC - 信号处理服务
 *
 * <AUTHOR>
 * @date 2025-08-13
 */
@FeignClient(name = "owp-dsp",
        url = "${dsp.server.url}",
        configuration = FeignConfiguration.class)
public interface DspService {
    @ApiOperation("主频计算处理")
    @PostMapping(value = "/api/frequency/basic/process")
    BasicFreqProcessResp analyseImage(@RequestBody BasicFreqProcessBody body);
}
