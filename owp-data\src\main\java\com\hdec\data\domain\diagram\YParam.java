package com.hdec.data.domain.diagram;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * 过程线图参数(Y轴)
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class YParam {

    /** 线名称 */
    private String name;

    /** 线数据 */
    private List<Float> data;

    /** 线类型 */
    private String type = "line";

    /** 线下标 */
    private Integer yAxisIndex;

    /** 线样式 */
    private LineStyle lineStyle = new LineStyle();

}
