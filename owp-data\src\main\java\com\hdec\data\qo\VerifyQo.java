package com.hdec.data.qo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class VerifyQo {

    private String rate;

    /** 测点编号 */
    private Integer pointId;

    /** 方向编号 */
    private Integer direct;

    /** 时间 */
    private String time;

    /** 属性ID */
    private Integer attrId;

    /** 属性ID和方向 */
    private String attrAndDirect;

    /** 审核状态（1：通过  2：未通过） */
    private Integer approval;

    private Float newVal;

    private String delReason;

    public VerifyQo(String rate, Integer pointId, String time) {
        this.rate = rate;
        this.pointId = pointId;
        this.time = time;
    }
}
