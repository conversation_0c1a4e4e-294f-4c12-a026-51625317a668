package com.hdec.data.domain;

import com.hdec.common.domain.BaseTree;
import com.hdec.data.domain.template.Mould;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * 报告大纲导航
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class ReportOutlineNav extends BaseTree {

    private Integer id;

    private String seqNum;

    private List<Mould> moulds;

    /** 孩子节点 */
    private List<ReportOutlineNav> children;

    /** 是否告警 */
    private Boolean isWarning;

    public ReportOutlineNav(Integer index, String bid, String name) {
        this.index = index;
        this.bid = bid;
        this.name = name;
    }

    public ReportOutlineNav(Integer index, String bid, String name, Boolean isWarning) {
        this.index = index;
        this.bid = bid;
        this.name = name;
        this.isWarning = isWarning;
    }

    public ReportOutlineNav(Integer id, Integer index, String bid, String name, Boolean isWarning) {
        this.id = id;
        this.index = index;
        this.bid = bid;
        this.name = name;
        this.isWarning = isWarning;
    }

}
