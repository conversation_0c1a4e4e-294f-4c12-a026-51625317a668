package com.hdec.data.mapper;

import com.hdec.data.domain.Redo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 重做业务Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface RedoMapper {

    /**
     * 查询最先入库的一条
     */
    Redo getFirstOne();

    /**
     * 查询最早的N条任务
     */
    List<Redo> selectTop(@Param("N") int N);

    /**
     * 批量更新
     */
    void updateList(@Param("redoList") List<Redo> redoList);

    List<Redo> listByField(@Param("fieldNum") String fieldNum);

    void delete(@Param("id") Integer id);

    /**
     * 更新
     */
    void update(@Param("redo") Redo redo);

    List<Integer> taskExist(@Param("redo") Redo redo);

    void insert(@Param("redo") Redo redo);

    List<String> getAllStatFieldNums();

    void resetExecuteFlag();
}