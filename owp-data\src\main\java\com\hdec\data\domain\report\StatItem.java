package com.hdec.data.domain.report;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 统计项
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class StatItem {

    /** 海上设施ID */
    private String seaFacilityId;

    /** 海上设施名称 */
    private String seaFacilityName;

    /** 监测项目 */
    private String monitorItem;

    /** 部位（基础顶/塔筒顶） */
    private String position;

    /** 测点ID */
    private Integer pointId;

    /** 测点编号 */
    private String pointNo;

    /** 方向(X/Y) */
    private String direct;

    /** 最小值 */
    private Double min;

    /** 最大值 */
    private Double max;

    /** 平均值 */
    private Double avg;

    public StatItem(String monitorItem, String seaFacilityId, String seaFacilityName, String position, String direct, Double min, Double max, Double avg) {
        this.monitorItem = monitorItem;
        this.seaFacilityId = seaFacilityId;
        this.seaFacilityName = seaFacilityName;
        this.position = position;
        this.direct = direct;
        this.min = min;
        this.max = max;
        this.avg = avg;
    }

    public StatItem(String monitorItem, String seaFacilityId, String seaFacilityName, String position, String pointNo, String direct, Double min, Double max, Double avg) {
        this.monitorItem = monitorItem;
        this.seaFacilityId = seaFacilityId;
        this.seaFacilityName = seaFacilityName;
        this.position = position;
        this.pointNo = pointNo;
        this.direct = direct;
        this.min = min;
        this.max = max;
        this.avg = avg;
    }

    public StatItem(String monitorItem, String seaFacilityId, String seaFacilityName, Integer pointId, String pointNo, String direct, Double min, Double max, Double avg) {
        this.monitorItem = monitorItem;
        this.seaFacilityId = seaFacilityId;
        this.seaFacilityName = seaFacilityName;
        this.pointId = pointId;
        this.pointNo = pointNo;
        this.direct = direct;
        this.min = min;
        this.max = max;
        this.avg = avg;
    }
}
