<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hdec.data.mapper.RedoMapper">

    <sql id="common_sql">`day`, point_id, rate, field_num, exe_flag, create_time</sql>

    <select id="getFirstOne" resultType="com.hdec.data.domain.Redo">
        SELECT
        id, <include refid="common_sql"/>
        FROM
        owp_redo
        WHERE
        exe_flag = 0
        ORDER BY id
        LIMIT 1
    </select>

    <select id="selectTop" resultType="com.hdec.data.domain.Redo">
        SELECT
            *
        FROM
            owp_redo
        WHERE
            exe_flag = 0
        ORDER BY create_time
        LIMIT #{N}
    </select>

    <select id="listByField" resultType="com.hdec.data.domain.Redo">
        SELECT DISTINCT
            `day`,
            point_id,
            direct
        FROM
            owp_stat_${fieldNum}
    </select>

    <select id="getAllStatFieldNums" resultType="string">
        SHOW TABLES LIKE 'owp_stat_owp%'
    </select>

    <delete id="delete">
        DELETE
        FROM
            owp_redo
        WHERE
            id = #{id}
    </delete>

    <update id="resetExecuteFlag">
        UPDATE owp_redo
        SET
            exe_flag = 0
    </update>

    <update id="update" parameterType="com.hdec.data.domain.Redo">
        UPDATE owp_redo
        <trim prefix="set" suffixOverrides=",">
            <if test="redo.day != null">`day` = #{redo.day},</if>
            <if test="redo.pointId != null">point_id = #{redo.pointId},</if>
            <if test="redo.direct != null">direct = #{redo.direct},</if>
            <if test="redo.fieldNum != null">field_num = #{redo.fieldNum},</if>
            <if test="redo.exeFlag != null">exe_flag = #{redo.exeFlag},</if>
        </trim>
        WHERE
        id = #{redo.id}
    </update>

    <update id="updateList" parameterType="com.hdec.data.domain.Redo">
        <foreach collection="redoList" item="redo" separator=";">
            UPDATE owp_redo
            <trim prefix="set" suffixOverrides=",">
                <if test="redo.day != null">`day` = #{redo.day},</if>
                <if test="redo.pointId != null">point_id = #{redo.pointId},</if>
                <if test="redo.rate != null">rate = #{redo.rate},</if>
                <if test="redo.exeFlag != null">exe_flag = #{redo.exeFlag},</if>
                <if test="redo.fieldNum != null">field_num = #{redo.fieldNum},</if>
            </trim>
            WHERE
            id = #{redo.id}
        </foreach>
    </update>

    <select id="taskExist" resultType="int">
        SELECT
            1
        FROM
            owp_redo
        WHERE
            `day` = #{redo.day} AND point_id = #{redo.pointId} AND rate = #{redo.rate}
            AND field_num = #{redo.fieldNum} AND exe_flag = false LIMIT 1
    </select>

    <insert id="insert" parameterType="com.hdec.data.domain.Redo">
        INSERT INTO owp_redo (<include refid="common_sql"/>)
        VALUES
        (#{redo.day}, #{redo.pointId}, #{redo.rate}, #{redo.fieldNum}, #{redo.exeFlag}, NOW())
    </insert>

</mapper>