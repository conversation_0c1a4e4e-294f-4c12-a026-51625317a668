package com.hdec.data.domain.template;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * 报告变量Vo类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class ReportVarVo {

    /** 基础变量 */
    private List<ReportVar> baseVars;

    /** 复用变量 */
    private List<ReportVar> multiVars;

    /** 图表编号变量 */
    private List<ReportVar> chartNumVars;

    public ReportVarVo(List<ReportVar> baseVars, List<ReportVar> multiVars, List<ReportVar> chartNumVars) {
        this.baseVars = baseVars;
        this.multiVars = multiVars;
        this.chartNumVars = chartNumVars;
    }
}
