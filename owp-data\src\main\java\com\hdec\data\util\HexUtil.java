package com.hdec.data.util;

import java.math.BigInteger;

/**
 * 十六进制处理类
 *
 * <AUTHOR>
 */
public class HexUtil {

    static char[] HEX_CHARS = new char[]{'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};

    /**
     * 十六进制转有符号整型
     */
    public static int hex2SignInt(String hex, int start, int end) {
        String substring = hex.substring(start * 2, end * 2);
        return new BigInteger(substring, 16).intValue();
    }

    public static String bytesToHexNoZero(byte[] bytes, boolean bigEndian) {
        StringBuilder builder = new StringBuilder();
        for (byte b : bytes) {
            //前位数
            builder.append(HEX_CHARS[(b & 0xF0) >> 4]);
            //后位数
            builder.append(HEX_CHARS[b & 0x0F]);
        }
        return bigEndian ? builder.toString() : builder.reverse().toString();
    }

    /**
     * 十六进制转有符号长整型
     */
    public static long hex2SignLong(String hex, int start, int end) {
        return new BigInteger(hex.substring(start * 2, end * 2), 16).longValue();
    }

    /**
     * 十六进制转有符号整型（3字节）
     */
    public static int hex2SignInt3(String strHex) {
        if (strHex.length() != 6 || "FFFFFF".equals(strHex)) {
            return 0;
        }
        int x = 0;
        //带符号十六进制转换十进制
        String fristNum = strHex.substring(0, 1);
        String hexStr2Byte = parseHexStr2Byte(fristNum);
        String flag = hexStr2Byte.substring(0, 1);
        if ("1".equals(flag)) {
            StringBuffer sb = new StringBuffer();
            for (int i = 0; i < strHex.length(); i++) {
                String num = strHex.substring(i, i + 1);
                int decNum = Integer.parseInt(num, 16);
                int a = decNum ^ 15;
                sb.append(intToHex(a));
            }
            x = -Integer.parseInt(sb.toString(), 16) - 1;
        } else {
            x = Integer.parseInt(strHex, 16);
        }
        return x;
    }

    //十进制转16进制
    private static String intToHex(int n) {
        StringBuffer s = new StringBuffer();
        String a;
        char[] b = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};
        while (n != 0) {
            s = s.append(b[n % 16]);
            n = n / 16;
        }
        a = s.reverse().toString();
        return a;
    }

    /**
     * 将16进制转换为二进制
     *
     * @param hexStr
     * @return
     */
    public static String parseHexStr2Byte(String hexStr) {
        if (hexStr.length() == 0)
            return null;
        int sint = Integer.valueOf(hexStr, 16);
        //十进制在转换成二进制的字符串形式输出!
        String bin = Integer.toBinaryString(sint);
        for (int i = bin.length(); i < 4; i++) {
            bin = "0" + bin;
        }
        return bin;
    }

    public static byte[] hexToBytes(String hex, boolean bigEndian) {
        hex = preHandHex(hex);
        if (!hex.matches("^[\\dA-F]+$")) {
            throw new IllegalArgumentException(hex + " 不是十六进制格式");
        }
        byte[] bytes = new byte[hex.length() % 2 == 0 ? hex.length() / 2 : hex.length() / 2 + 1];
        for (int i = hex.length() - 1; i >= 0; i -= 2) {
            //后位数
            int hinderValue = getValue(hex.charAt(i));
            //前位数，没有则为0
            int aheadValue = i - 1 < 0 ? 0x0 : getValue(hex.charAt(i - 1));
            if (bigEndian) {
                bytes[i / 2] = (byte) (aheadValue << 4 | hinderValue);
            } else {
                //倒序写入
                bytes[bytes.length - 1 - i / 2] = (byte) (hinderValue << 4 | aheadValue);
            }
        }
        return bytes;
    }

    public static byte[] hexToBytes(String s) {
        byte[] data = new byte[s.length() / 2];
        for (int i = 0; i < s.length(); i += 2) {
            data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4) + Character.digit(s.charAt(i+1), 16));
        }
        return data;
    }

    public static String bytesToHex(byte[] bytes, boolean bigEndian) {
        StringBuilder builder = new StringBuilder();
        for (byte b : bytes) {
            //前位数
            builder.append(HEX_CHARS[(b & 0xF0) >> 4]);
            //后位数
            builder.append(HEX_CHARS[b & 0x0F]);
        }
//        return removeZero(bigEndian ? builder.toString() : builder.reverse().toString());
        return bigEndian ? builder.toString() : builder.reverse().toString();
    }

//    public static String bytesToHex(byte[] bytes) {
//        StringBuilder builder = new StringBuilder();
//        for (byte b : bytes) {
//            //前位数
//            builder.append(HEX_CHARS[(b & 0xF0) >> 4]);
//            //后位数
//            builder.append(HEX_CHARS[b & 0x0F]);
//        }
//        return removeZero(builder.toString());
//    }

//    public static String bytesToHex(byte[] bytes) {
//        StringBuilder hexString = new StringBuilder();
//        for (byte b : bytes) {
//            String hex = Integer.toHexString(0xff & b);
//            if (hex.length() == 1) {
//                // 如果是单位数字，前面补0
//                hexString.append('0');
//            }
//            hexString.append(hex);
//        }
//        return hexString.toString();
//    }

    /**
     * 字节数组转十六进制字符串
     */
    public static String bytesToHex(byte[] bytes) {
        StringBuilder hex = new StringBuilder(bytes.length * 2);
        for (byte b : bytes) {
            hex.append(String.format("%02X", b));
        }
        return hex.toString();
    }

    private static int getValue(char c) {
        for (int i = 0; i < HEX_CHARS.length; i++) {
            if (HEX_CHARS[i] == c) {
                return i;
            }
        }
        throw new IllegalArgumentException();
    }

    /**
     * 去除0
     *
     * @param hex
     * @return
     */
    private static String removeZero(String hex) {
        return hex.replaceAll("^0+", "");
    }

    /**
     * 预处理
     *
     * @param hex
     * @return
     */
    private static String preHandHex(String hex) {
        return removeZero(hex.trim().toUpperCase());
    }

    /*大端，高字节在后*/

    /**
     * 字节数组转3字节int
     */
    public static int bytesToInt3(byte[] bytes) {
        byte[] resBytes = new byte[4];

        Integer sign = getBitByByte(bytes[0], 0);
        if (sign == 0) {
            resBytes[0] = 0b00000000;
        } else {
            resBytes[0] = (byte) 0b10000000;
        }
        resBytes[1] = (byte) (bytes[0] & 0b01111111);
        resBytes[2] = bytes[1];
        resBytes[3] = bytes[2];

        return bytesToInt(resBytes);
    }

    public static byte[] ffffffff(byte[] msgBytes) {
        byte[] resBytes = new byte[4];

        Integer sign = getBitByByte(msgBytes[0], 0);
        if (sign == 0) {
            resBytes[0] = 0b00000000;
        } else {
            resBytes[0] = (byte) 0b10000000;
        }
        resBytes[1] = (byte) (msgBytes[0] & 0b01111111);
        resBytes[2] = msgBytes[1];
        resBytes[3] = msgBytes[2];

        return resBytes;
    }

    /**
     * 获取单个字节中某一位bit的值,采用字符取值方式
     */
    public static Integer getBitByByte(byte b, int index) {
        if (index >= 8) {
            return null;
        }
        String binStr = byteToBin(b);
        return Integer.parseInt(String.valueOf(binStr.charAt(index)));
    }

    /**
     * 把单个字节转换成二进制字符串
     */
    public static String byteToBin(byte b) {
        String zero = "00000000";
        String binStr = Integer.toBinaryString(b & 0xFF);
        if (binStr.length() < 8) {
            binStr = zero.substring(0, 8 - binStr.length()) + binStr;
        }
        return binStr;
    }


    /**
     * 字节数组转int
     */
    public static int bytesToInt(byte[] bytes) {
        return bytes[3] & 0xFF |
                (bytes[2] & 0xFF) << 8 |
                (bytes[1] & 0xFF) << 16 |
                (bytes[0] & 0xFF) << 24;
    }

    // 任意长度
    public static int toInt(byte[] bRefArr) {
        int iOutcome = 0;
        byte bLoop;
        for (int i = 0; i < bRefArr.length; i++) {
            bLoop = bRefArr[i];
            iOutcome += (bLoop & 0xFF) << (8 * i);
        }
        return iOutcome;
    }

    /**
     * 字节数组转short
     */
    public static short bytesToShort(byte[] bytes) {
        return (short) (bytes[1] & 0xFF |
                (bytes[0] & 0xFF) << 8);
    }

    /**
     * 字节数组截取
     */
    public static byte[] subArr(byte[] arr, int start, int end) {
        int len = end - start;
        byte[] res = new byte[len];
        int index = 0;
        for (int i = start; i < end; i++) {
            res[index++] = arr[i];
        }
        return res;
    }


}
