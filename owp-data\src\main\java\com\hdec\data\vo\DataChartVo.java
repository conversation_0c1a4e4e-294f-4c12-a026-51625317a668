package com.hdec.data.vo;

import com.hdec.data.domain.Datum;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * 数据图Vo类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class DataChartVo {

    /** 测点ID */
    private Integer pointId;

    /** 方向ID */
    private Integer direct;

    /** 分量ID */
    private Integer attrId;

    /** 线名称 */
    private String name;

    /** 数据集合 */
    List<Object[]> data;

    /** 是否采样 */
    private Boolean isSampling;

    public DataChartVo(Integer pointId, Integer direct) {
        this.pointId = pointId;
        this.direct = direct;
    }

    public DataChartVo(Integer pointId, Integer direct, Integer attrId) {
        this.pointId = pointId;
        this.direct = direct;
        this.attrId = attrId;
    }

    public DataChartVo(Integer pointId, Integer direct, Integer attrId, String name, List<Object[]> data) {
        this.pointId = pointId;
        this.direct = direct;
        this.attrId = attrId;
        this.name = name;
        this.data = data;
    }
}
