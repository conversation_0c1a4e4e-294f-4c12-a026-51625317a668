package com.hdec.data.service;

import com.alibaba.fastjson.JSON;
import com.hdec.common.domain.PointCommon;
import com.hdec.common.domain.SettingsCommon;
import com.hdec.common.util.CommonUtil;
import com.hdec.common.util.TimeUtil;
import com.hdec.data.domain.Datum;
import com.hdec.data.feign.MonitorService;
import com.hdec.data.feign.WindService;
import com.hdec.data.qo.DynamicGraphSloshConf;
import com.hdec.data.qo.SloshQo;
import com.hdec.data.vo.SloshVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 动态图形业务类
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@Slf4j
@Service
public class DynamicGraphService {

    private static final String SLOSH_CONF_CODE = "DYNAMIC_GRAPH_SLOSH";

    @Resource
    private WindService windService;

    @Resource
    private MonitorService monitorService;

    @Resource
    private TdBaseService tdBaseService;

    public void settingSlosh(String fieldNum, DynamicGraphSloshConf conf) {
        String value = JSON.toJSONString(conf);
        SettingsCommon settings = new SettingsCommon();
        settings.setFieldNum(fieldNum);
        settings.setCode(SLOSH_CONF_CODE);
        settings.setValue(value);
        settings.setDescription("动态图形中晃点图配置信息");
        windService.saveSettings(settings);
    }

    public DynamicGraphSloshConf getSloshConf(String fieldNum) {
        SettingsCommon settings = windService.getSettings(fieldNum, SLOSH_CONF_CODE);
        if (settings == null||settings.getValue()==null) {
            log.error("动态图形-晃点图-未获取到配置");
            return null;
        }
        return JSON.parseObject(settings.getValue(), DynamicGraphSloshConf.class);
    }

    public SloshVo calcSlosh(Integer pointId, Date startTime, Date endTime) {
        /*  获取该测点绑定风机的设计泥面高程  */
        Float designElevation = windService.getDesignElevation(pointId);
        if (designElevation == null) {
            log.error("动态图形-晃点图-请先维护该测点绑定海上设施的设计泥面高程");
            return null;
        }

        /*  获取该测点所属海上设施下的所有测点并检查安装高程  */
        List<PointCommon> points = monitorService.getSameGroupPoints(pointId);
        for (PointCommon point : points) {
            if (ObjectUtils.isEmpty(point.getInstallElevation())) {
                log.error("动态图形-晃点图-请先维护测点{}的安装高程", point.getNo());
                return null;
            }
            Float numInstallElevation = CommonUtil.extractDecimal(point.getInstallElevation());
            if (numInstallElevation == null) {
                log.error("动态图形-晃点图-测点{}的安装高程中未识别到唯一数字", point.getNo());
                return null;
            } else {
                point.setInstallElevationNum(numInstallElevation);
            }
        }
        /*  按安装高程升序  */
        points.sort(Comparator.comparing(PointCommon::getInstallElevationNum));

        /*  过滤需要计算的层  */
        PointCommon point = points.stream().filter(p -> Objects.equals(p.getId(), pointId)).findFirst().orElse(null);
        if (point == null || point.getInstallElevationNum() == null) {
            log.error("动态图形-晃点图-未找到该测点或该测点的安装高程:{}", point);
            return null;
        }

        /*  获取配置的分量名称  */
        if (point.getFieldNum()==null){
            log.error("动态图形-晃点图-获取配置参数 fieldNum 为空");
        }
        DynamicGraphSloshConf conf = getSloshConf(point.getFieldNum());
        if (conf==null||conf.getAttrId()==null){
            log.error("动态图形-晃点图-获取配置参数 attrId 为空");
            return null;
        }
        Integer attrId = conf.getAttrId();

        List<PointCommon> calcPoints = points.stream().filter(p -> p.getInstallElevationNum() < point.getInstallElevationNum())
                .collect(Collectors.toList());
        calcPoints.add(point);

        /*  查询数据  */
        List<Integer> pointIds = calcPoints.stream().map(PointCommon::getId).collect(Collectors.toList());
        Map<Integer, Pair<Datum, Datum>> dataMap = selectSloshData(point.getInstId(), attrId, pointIds, startTime, endTime);

        /* 逐一计算偏移 */
        SloshVo slosh = null;
        SloshVo preSlosh = new SloshVo(startTime,0D,0D);
        Float baseHeight = designElevation;
        String prePointNo = null;
        for (PointCommon p : calcPoints) {
            float height = p.getInstallElevationNum() - baseHeight;
            Pair<Datum, Datum> datum = dataMap.get(p.getId());
            if (datum == null) {
                log.error("动态图形-晃点图-未查询到该测点的{}晃点数据", p.getNo());
                continue;
            }
            SloshQo qo = buildSloshQo(p, startTime, endTime);
            slosh = calcPointSloshVo(qo, height, datum.getLeft(), datum.getRight());
            if (slosh != null) {
                /*  修正角度  */
                correctAngle(qo.getAngle(), slosh);
                /*  累计偏移量  */
                addPreOffset(prePointNo, slosh, preSlosh);
                baseHeight = p.getInstallElevationNum();
                preSlosh = slosh;
                prePointNo = p.getNo();
            }
        }

        return slosh;
    }

    /**
     * 修正角度
     */
    public void correctAngle(Double angel, SloshVo slosh) {
        if (angel==null||slosh == null) {
            return;
        }
        /*  坐标系转换  */
        double radian = angel * Math.PI / 180;
        Double x = slosh.getMainDis();
        Double y = slosh.getVerMainDis();
        slosh.setMainDis(x * Math.cos(radian) - y * Math.sin(radian));
        slosh.setVerMainDis(x * Math.sin(radian) + y * Math.cos(radian));
    }

    /**
     * 加上前面的偏移量
     */
    public void addPreOffset(String pointNo, SloshVo slosh, SloshVo preSlosh) {
        if (slosh == null || preSlosh == null) {
            log.error("动态图形-晃点图-叠加偏移量:测点[{}]数据缺失，已忽略该时刻数据[{}-{}]", pointNo, slosh, preSlosh);
            return;
        }
        if (!ObjectUtils.isEmpty(preSlosh)) {
            Double preMainDis = preSlosh.getMainDis();
            Double preVerMainDis = preSlosh.getVerMainDis();
            if (preMainDis != null && preVerMainDis != null) {
                slosh.setMainDis(slosh.getMainDis() + preMainDis);
                slosh.setVerMainDis(slosh.getVerMainDis() + preVerMainDis);
            }
        }
    }


    /**
     * 计算偏移量
     */
    public SloshVo calcPointSloshVo(SloshQo qo, float height, Datum xDatum, Datum yDatum) {
        SloshVo vo = new SloshVo(xDatum.getTime(), Math.tan(xDatum.getAttrVal() * Math.PI / 180) * height, Math.tan(yDatum.getAttrVal() * Math.PI / 180) * height);
        if (qo.getIsMainOpp()) {
            vo.setMainDis(-vo.getMainDis());
        }
        if (qo.getIsVerticalOpp()) {
            vo.setVerMainDis(-vo.getVerMainDis());
        }
        return vo;
    }

    private Map<Integer, Pair<Datum, Datum>> selectSloshData(Integer instId, Integer attrId, List<Integer> pointIds, Date start, Date end) {
        String ps = pointIds.stream().map(String::valueOf).collect(Collectors.joining(","));
        String sql = ("select last(ts) ts, point, AVG(a_#{attr_id}_1) d_1, AVG(a_#{attr_id}_2) d_2 from high_#{inst_id} " +
                "where point in (#{point_ids}) and ts >= '#{start}' and ts < '#{end}' " +
                "group by point")
                .replace("#{inst_id}", instId + "")
                .replace("#{attr_id}", attrId + "")
                .replace("#{point_ids}", ps)
                .replace("#{start}", TimeUtil.format2Second(start)).replace("#{end}", TimeUtil.format2Second(end));
        List<Map<String, Object>> record = tdBaseService.selectMulti(sql, "高频");
        Map<Integer, Pair<Datum, Datum>> data = new HashMap<>();
        record.forEach(r -> {
            Integer pointId = (Integer) r.get("point");
            Date ts = (Date) r.get("ts");
            Double d1 = (Double) r.get("d_1");
            Double d2 = (Double) r.get("d_2");
            Datum x = new Datum();
            x.setPointId(pointId);
            x.setTime(ts);
            x.setAttrVal(d1 == null ? null : d1.floatValue());
            x.setDirection(1);
            Datum y = new Datum();
            y.setPointId(pointId);
            y.setTime(ts);
            y.setAttrVal(d2 == null ? null : d2.floatValue());
            y.setDirection(2);
            data.put(pointId, Pair.of(x, y));
        });
        return data;
    }

    public SloshQo buildSloshQo(PointCommon point, Date startTime, Date endTime) {
        SloshQo sloshQo = new SloshQo();
        sloshQo.setPointID(point.getId());
        sloshQo.setStartTime(TimeUtil.format2Second(startTime));
        sloshQo.setEndTime(TimeUtil.format2Second(endTime));
        sloshQo.setAngle(point.getAngle() == null ? 0D : point.getAngle().intValue());
        sloshQo.setIsMainOpp(point.getIsMainOpp() != null && point.getIsMainOpp());
        sloshQo.setIsVerticalOpp(point.getIsVerticalOpp() != null && point.getIsVerticalOpp());
        return sloshQo;
    }
}
