package com.hdec.data.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/8/10
 */
@Data
@ToString
@NoArgsConstructor
public class ExistStatVo {

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone="GMT+8")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone="GMT+8")
    private Date endTime;

    /** 测次 */
    private Integer measureNum;

    public ExistStatVo(Date startTime, Date endTime) {
        this.startTime = startTime;
        this.endTime = endTime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ExistStatVo that = (ExistStatVo) o;
        return Objects.equals(startTime, that.startTime) &&
                Objects.equals(endTime, that.endTime);
    }

    @Override
    public int hashCode() {
        return Objects.hash(startTime, endTime);
    }
}
