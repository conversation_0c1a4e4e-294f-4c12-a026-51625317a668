# 国能宸泰腐蚀电位检测

## 原文档ModbusTCP描述

所支持协议说明：ModbusTCP 数据转发以太网

| 通道号 | 变量名  | 变量类型   | 通道名称        | 读写类型 | 寄存器名称      | 数据类型        | 寄存器地址 | 通道采集频次 |
|-----|------|--------|-------------|------|------------|-------------|-------|--------|
| 0   | 电位 1 | SINGLE | 读写 4WUB0001 | 读写   | [4 区]输出寄存器 | 16 位 无符号二进制 | 1     | 1      |
| 1   | 电位 2 | SINGLE | 读写 4WUB0002 | 读写   | [4 区]输出寄存器 | 16 位 无符号二进制 | 2     | 1      |
| 2   | 电位 3 | SINGLE | 读写 4WUB0003 | 读写   | [4 区]输出寄存器 | 16 位 无符号二进制 | 3     | 1      |
| 3   | 电位 4 | SINGLE | 读写 4WUB0004 | 读写   | [4 区]输出寄存器 | 16 位 无符号二进制 | 4     | 1      |

## 代码示例

pom

```xml

<dependency>
    <groupId>com.ghgande</groupId>
    <artifactId>j2mod</artifactId>
    <version>3.2.0</version>
</dependency>
```

java

```java

private static final String ADDR = "***************";

public static void main(String[] args) throws Exception {
    ModbusTCPMaster master = new ModbusTCPMaster(ADDR);
    master.connect();
    for (int i = 0; i < 100; i++) {
        Register[] registers = master.readMultipleRegisters(0, 4);
        log.info("registers:{}", Arrays.toString(registers));
        Thread.sleep(1000);
    }
    master.disconnect();
}
```

