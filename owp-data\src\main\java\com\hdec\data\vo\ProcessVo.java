package com.hdec.data.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 推送进度类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class ProcessVo {

    /** 录入ID */
    private Integer id;

    /** 进度 */
    private String process;

    /** 1:进度  2:告警 */
    private Integer flag;

    public ProcessVo(Integer flag) {
        this.flag = flag;
    }

    public ProcessVo(Integer id, String process, Integer flag) {
        this.id = id;
        this.process = process;
        this.flag = flag;
    }

}
