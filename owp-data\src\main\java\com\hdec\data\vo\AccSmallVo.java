package com.hdec.data.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * 小范围加速度数据返回类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class AccSmallVo {

    /** 时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone="GMT+8")
    private Date time;

    /** 值 */
    private Float val;

    public AccSmallVo(Date time, Float val) {
        this.time = time;
        this.val = val;
    }

    public AccSmallVo(Float val) {
        this.val = val;
    }
}
