package com.hdec.data.qo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 频率批量分析查询类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class FreqTaskQo {

    /** 当前页 */
    private Integer pageNum;

    /** 总页数 */
    private Integer pageSize;

    /** 测点编号 */
    private String pointNos;

    /** 开始时间 */
    private String startTime;

    /** 结束时间 */
    private String endTime;

    /** 分析指标 */
    private String target;

    /** 滤波器（巴特沃斯/切比雪夫） */
    private String waveFilter;

    /** 滤波类型（带通/高通/低通） */
    private String filterType;

    /** 任务状态 */
    private Integer status;

}
