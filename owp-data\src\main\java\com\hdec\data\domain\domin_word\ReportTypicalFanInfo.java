package com.hdec.data.domain.domin_word;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * 报告-典型风机信息
 *
 * <AUTHOR>
 * @date 2022/12/28
 */
@Data
@ToString
@NoArgsConstructor
public class ReportTypicalFanInfo {

    /** 风机编号 */
    private String no;

    /** 倾斜-基础顶 */
    private Double baseMinX;
    private Double baseMaxX;
    private Double baseAvgX;

    private Double baseMinY;
    private Double baseMaxY;
    private Double baseAvgY;

    /** 倾斜-塔筒顶 */
    private Double topMinX;
    private Double topMaxX;
    private Double topAvgX;

    private Double topMinY;
    private Double topMaxY;
    private Double topAvgY;

    /** 振动 */
    private Double shockMaxX;
    private Double shockMaxY;

    /** 应力-文字 */
    private Double stressMin;
    private Double stressMax;
    private Double stressAvgMin;
    private Double stressAvgMax;

    /** 应力-图片 */
    private Integer picIndex;
    private String stressPicBase64;

    /** 应力-表格 */
    private List<StressPoint> stressTables;

    /** 腐蚀 */
    private Integer corrodeSize;
    private String corrodePointNos;
    private String corrodePointValues;
    private String corrodePicBase64;

    public Integer getCorrodeSize() {
        if (corrodeSize == null) {
            return 0;
        }
        return corrodeSize;
    }

    public ReportTypicalFanInfo(String no) {
        this.no = no;
    }

    public ReportTypicalFanInfo(String no, Double baseMinX, Double baseMaxX, Double baseAvgX, Double baseMinY, Double baseMaxY, Double baseAvgY, Double topMinX, Double topMaxX, Double topAvgX, Double topMinY, Double topMaxY, Double topAvgY) {
        this.no = no;
        this.baseMinX = baseMinX;
        this.baseMaxX = baseMaxX;
        this.baseAvgX = baseAvgX;
        this.baseMinY = baseMinY;
        this.baseMaxY = baseMaxY;
        this.baseAvgY = baseAvgY;
        this.topMinX = topMinX;
        this.topMaxX = topMaxX;
        this.topAvgX = topAvgX;
        this.topMinY = topMinY;
        this.topMaxY = topMaxY;
        this.topAvgY = topAvgY;
    }

    @Override
    public String toString() {
        return "ReportTypicalFanInfo{" +
                "no='" + no + '\'' +
                ", baseMinX=" + baseMinX +
                ", baseMaxX=" + baseMaxX +
                ", baseAvgX=" + baseAvgX +
                ", baseMinY=" + baseMinY +
                ", baseMaxY=" + baseMaxY +
                ", baseAvgY=" + baseAvgY +
                ", topMinX=" + topMinX +
                ", topMaxX=" + topMaxX +
                ", topAvgX=" + topAvgX +
                ", topMinY=" + topMinY +
                ", topMaxY=" + topMaxY +
                ", topAvgY=" + topAvgY +
                ", shockMaxX=" + shockMaxX +
                ", shockMaxY=" + shockMaxY +
                ", stressMin=" + stressMin +
                ", stressMax=" + stressMax +
                ", stressAvgMin=" + stressAvgMin +
                ", stressAvgMax=" + stressAvgMax +
                ", picIndex=" + picIndex +
                ", stressPicBase64='" + (stressPicBase64 == null ? null : stressPicBase64.substring(0, 10)) + '\'' +
                ", stressTables=" + stressTables +
                ", corrodeSize=" + corrodeSize +
                ", corrodePointNos='" + corrodePointNos + '\'' +
                ", corrodePointValues='" + corrodePointValues + '\'' +
                ", corrodePicBase64='" + (corrodePicBase64 == null ? null : corrodePicBase64.substring(0, 10)) + '\'' +
                '}';
    }
}
