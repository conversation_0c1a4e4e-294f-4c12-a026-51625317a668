package com.hdec.data.service;

import com.hdec.common.domain.AttrInfo;
import com.hdec.common.domain.Direct;
import com.hdec.common.domain.PointCommon;
import com.hdec.common.util.DataServiceUtil;
import com.hdec.common.util.TimeUtil;
import com.hdec.common.util.Utils;
import com.hdec.data.domain.Stat;
import com.hdec.data.feign.MonitorService;
import com.hdec.data.mapper.DataStatApprovalMapper;
import com.hdec.data.mapper.DataStatMapper;
import com.hdec.data.mapper.PointValueJudgeMapper;
import com.hdec.data.qo.ValueJudgeQo;
import com.hdec.data.qo.StatQo;
import com.hdec.data.util.ExcelUtil;
import com.hdec.data.vo.TimeVo;
import com.hdec.data.vo.ValueJudgeVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.language.Nysiis;
import org.aspectj.weaver.ast.Var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletResponse;
import java.sql.Time;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ValueJudgeService {

    @Autowired
    private PointValueJudgeMapper judgeMapper;

    @Autowired
    private TdService tdService;

    @Autowired
    private DataStatMapper statMapper;

    @Autowired
    private MonitorService monitorService;

    @Autowired
    private TdBaseService tdBaseService;

    /**
     * 测值评判列表
     */
    public List<ValueJudgeVo> list(String fieldNum, ValueJudgeQo qo) throws Exception {
        List<ValueJudgeVo> vos = new ArrayList<>();

        /* 获取分量、相关测点信息 */
        AttrInfo attrInfo = monitorService.getAttrInfo(qo.getAttrId(), "withPoint");
        List<Direct> directs = Utils.parseDirect(attrInfo.getInstDirect(), attrInfo.getIsPolar());
        if (attrInfo == null || ObjectUtils.isEmpty(attrInfo.getPoints()) || ObjectUtils.isEmpty(directs)) {
            return vos;
        }
        List<Integer> pointIds = attrInfo.getPoints().stream().map(PointCommon::getId).collect(Collectors.toList());

        /* 获取测点审核不通过数量、采集总数、最后一天数据情况 */
        Integer monitorId = monitorService.getMonitorIdByInst(attrInfo.getInstId());
        List<Stat> stats = new ArrayList<>();
        try {
            stats = statMapper.getStatDataByTimePointAttrRate(fieldNum, qo.getStartTime(), qo.getEndTime(), pointIds, qo.getAttrId(), monitorId, null);
        } catch (Exception e) {
        }
        Map<String, List<Stat>> pointDirectMap = stats.stream().collect(Collectors.groupingBy(e -> e.getPointId() + "-" + e.getDirect()));

        Map<String, Integer> pointDirectApprovalFailMap = getPointApprovalFailNumInfo(pointDirectMap);
        Map<String, Integer> pointDirectTotalNumMap = getPointTotalNumMap(pointDirectMap);
        List<Stat> lastDayStats = new ArrayList<>();
        try {
            lastDayStats = statMapper.getStatDataByTimePointAttrRate(fieldNum, TimeUtil.completeStart(qo.getEndTime().substring(0, 10)), qo.getEndTime(), pointIds, qo.getAttrId(), monitorId, null);
        } catch (Exception e) {
        }
        Map<String, Integer> lastDayMap = getPointTotalNumMap(lastDayStats.stream().collect(Collectors.groupingBy(e -> e.getPointId() + "-" + e.getDirect())));

        /* 缺测率 */
        List<Map<String, Object>> records = tdBaseService.selectMulti(makeMissRateSql(attrInfo, directs, qo), attrInfo.getRate());

        /* 按开始结束时间填充两端数据 */
        fillBoth(records, attrInfo.getId(), directs, qo.getStartTime(), qo.getEndTime(), qo.getMissInterval());

        int totalSeconds = TimeUtil.secondsBetween(qo.getStartTime(), qo.getEndTime());

        sortByNo(attrInfo.getPoints());

        for (PointCommon point : attrInfo.getPoints()) {
            for (Direct direct : directs) {
                /* 统计异常率 */
                Integer approvalFailNum = pointDirectApprovalFailMap.get(point.getId() + "-" + direct.getId());
                Integer totalNum = pointDirectTotalNumMap.get(point.getId() + "-" + direct.getId());
                double abnormalRate = 0;
                String abnormalRateDesc;
                if (approvalFailNum != null && totalNum != null) {
                    abnormalRate = approvalFailNum * 100.0 / totalNum;
                    abnormalRateDesc = "审核不通过：" + approvalFailNum + "条<br/>采集总数：" + totalNum + "条";
                } else {
                    abnormalRateDesc = "审核不通过：0条<br/>采集总数：0条";
                }

                /* 统计缺测率 */
                String d1ColName = tdService.enD1ColName(qo.getAttrId(), direct.getId());

                List<Map<String, Object>> missList = records.stream()
                        .filter(e -> point.getId().equals(e.get("point")))
                        .filter(e -> e.get(d1ColName) != null && (Integer) e.get(d1ColName) > qo.getMissInterval()).collect(Collectors.toList());

                List<TimeVo> timeVos = new ArrayList<>(missList.size());
                for (Map<String, Object> record : missList) {
                    Date curDate = (Date) record.get("ts");
                    Date date = new Date();
                    date.setTime((curDate.getTime() / 1000 - (Integer) record.get(d1ColName)) * 1000);
                    timeVos.add(new TimeVo(TimeUtil.format2Second(date), TimeUtil.format2Second(curDate)));
                }

                /* 都为空的时候 */
                int seconds = missList.stream().map(e -> (Integer) e.get(d1ColName) - 1).mapToInt(e -> e).sum();
                if (ObjectUtils.isEmpty(missList)) {
                    Integer num = pointDirectTotalNumMap.get(point.getId() + "-" + direct.getId());
                    if (num == null || num == 0) {
                        seconds = totalSeconds;
                        timeVos.add(new TimeVo(qo.getStartTime(), qo.getEndTime()));
                    }
                }

                Integer lastDayNum = lastDayMap.get(point.getId() + "-" + direct.getId());
                vos.add(new ValueJudgeVo(point.getNo(), direct.getId(), direct.getName(),
                        String.format("%.1f", abnormalRate), abnormalRateDesc,
                        String.format("%.1f", seconds * 100.0 / totalSeconds),
                        seconds == 0 ? "0" : TimeUtil.second2Human(seconds),
                        timeVos,
                        lastDayNum == null || lastDayNum == 0));
            }
        }
        return vos;
    }




    /**
     * 按测点编号排序
     */
    public static void sortByNo(List<PointCommon> points) {
        if (ObjectUtils.isEmpty(points)) {
            return;
        }

        Collections.sort(points, (p1, p2) -> {
            if (p1.getNo() == null || p2.getNo() == null) {
                return 0;
            }

            String[] arr1 = p1.getNo().split("-");
            String[] arr2 = p2.getNo().split("-");
            if (arr1.length != 2 && arr2.length != 2) {
                return arr1[0].compareTo(arr2[0]);
            } else if (arr1.length == 2 && arr2.length != 2) {
                return -1;
            } else if (arr1.length != 2 && arr2.length == 2) {
                return 1;
            }

            String letter1 = extractLetter(arr1[0]);
            String letter2 = extractLetter(arr2[0]);
            if (letter1 == null && letter2 == null) {
                return 0;
            } else if (letter1 != null && letter2 == null) {
                return -1;
            } else if (letter1 == null && letter2 != null) {
                return 1;
            } else {
                int res = letter1.compareTo(letter2);
                if (res == 0) {
                    for (int i = 0; i < 2; i++) {
                        int num1 = extractNum(arr1[i]);
                        int num2 = extractNum(arr2[i]);
                        if (num1 == num2) {
                            continue;
                        } else {
                            return num1 - num2;
                        }
                    }
                } else {
                    return res;
                }
            }
            return 0;
        });
    }

    /**
     * 提取字母
     */
    private static String extractLetter(String s) {
        if (ObjectUtils.isEmpty(s)) {
            return null;
        }

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i <s.length(); i++) {
            char c = s.charAt(i);
            if (Character.isLetter(c)) {
                sb.append(c);
            }
        }
        if (sb.length() == 0) {
            return null;
        }
        return sb.toString();
    }

    /**
     * 从字符串的最后一位往前提取数字
     */
    private static Integer extractNum(String s) {
        if (ObjectUtils.isEmpty(s)) {
            return Integer.MAX_VALUE;
        }

        StringBuilder sb = new StringBuilder();
        for (int i = s.length() - 1; i >= 0; i--) {
            char c = s.charAt(i);
            if (Character.isDigit(c)) {
                sb.append(c);
            } else {
                continue;
            }
        }
        if (sb.length() == 0) {
            return Integer.MAX_VALUE;
        }
        return Integer.parseInt(sb.reverse().toString());
    }



    /**
     * 导出Excel
     */
    public void exportExcel(ValueJudgeQo qo, HttpServletResponse response) throws Exception {
        List<ValueJudgeVo> vos = this.list(qo.getFieldNum(), qo);
        List<String> titles = new ArrayList<>();
        titles.add("测点编号");
        titles.add("方向");
        Map<String, List<Map<String, Object>>> map = new HashMap<>();

        List<Map<String, Object>> list = new ArrayList<>();
        map.put("缺测详情", list);

        for (ValueJudgeVo vo : vos) {
            Map<String, Object> oneData = new HashMap<>();
            list.add(oneData);
            oneData.put("测点编号", vo.getPointNo());
            oneData.put("方向", vo.getDirect());
            int i = 1;
            for (TimeVo timeVo : vo.getTimeVos()) {
                if (!titles.contains("缺测时段" + i + "起始时间")) {
                    titles.add("缺测时段" + i + "起始时间");
                }
                if (!titles.contains("缺测时段" + i + "终止时间")) {
                    titles.add("缺测时段" + i + "终止时间");
                }
                if (!titles.contains("缺测时段" + i + "缺测时长")) {
                    titles.add("缺测时段" + i + "缺测时长");
                }
                oneData.put("缺测时段" + i + "起始时间", timeVo.getS());
                oneData.put("缺测时段" + i + "终止时间", timeVo.getE());
                oneData.put("缺测时段" + i + "缺测时长", TimeUtil.second2Human(TimeUtil.secondsBetween(timeVo.getS(), timeVo.getE()) - 1));
                i++;
            }
        }

        ExcelUtil.exportNormalExcel("缺测统计表", titles.toArray(new String[titles.size()]), titles.toArray(new String[titles.size()]), map, response, true, "yyyy-MM-dd HH:mm:ss", false);
    }

    /**
     * 按开始结束时间填充两端数据
     */
    private void fillBoth(List<Map<String, Object>> records, Integer attrId, List<Direct> directs, String startTime, String endTime, Integer missInterval) throws ParseException {
        /* 按测点分组 */
        Map<Integer, List<Map<String, Object>>> pointRecordMap = records.stream().collect(Collectors.groupingBy(e -> (Integer) e.get("point")));
        for (Map.Entry<Integer, List<Map<String, Object>>> entry : pointRecordMap.entrySet()) {
            List<Map<String, Object>> pointRecords = entry.getValue();
            for (Direct direct : directs) {
                String d1Name = tdService.enD1ColName(attrId, direct.getId());
                String d2Name = tdService.enD2ColName(attrId, direct.getId());
                List<Map<String, Object>> directRecords = pointRecords.stream()
                        .filter(e -> (e.get(d1Name) != null && (Integer) e.get(d1Name) > missInterval) || e.get(d2Name) != null && (Integer) e.get(d2Name) > missInterval)
                        .collect(Collectors.toList());

                if (!ObjectUtils.isEmpty(directRecords)) {
                    Map<String, Object> first = directRecords.get(0);

                    /* 修正第一条 */
                    Integer iFirst = (Integer) first.get(d1Name);
                    long startMillis = TimeUtil.parse2Second(startTime).getTime();
                    int maxFirst = (int) ((((Date) first.get("ts")).getTime() - startMillis) / 1000);
                    first.put(d1Name, iFirst <= maxFirst ? iFirst : maxFirst);

                    /* 添加最后一天 */
                    Map<String, Object> last = directRecords.get(directRecords.size() - 1);
                    Integer iLast = (Integer) last.get(d2Name);
                    int maxLast = (int) ((TimeUtil.parse2Second(endTime).getTime() - ((Date) last.get("ts")).getTime()) / 1000);
                    Date realLastTime = TimeUtil.addSeconds((Date) last.get("ts"), iLast <= maxLast ? iLast : maxLast);
                    Map<String, Object> realLast = directRecords.stream().filter(e -> realLastTime.getTime() == ((Date) e.get("ts")).getTime()).findFirst().orElse(null);
                    if (realLast == null) {
                        Map<String, Object> map = new HashMap<>();
                        map.put("ts", realLastTime);
                        map.put("point", entry.getKey());
                        map.put(d1Name, iLast <= maxLast ? iLast : maxLast);
                        records.add(map);
                    }
                }
            }
        }
    }

    /**
     * 组装缺测率sql
     */
    private String makeMissRateSql(AttrInfo attrInfo, List<Direct> directs, ValueJudgeQo qo) {
        String sTableName = DataServiceUtil.buildSTableName(attrInfo.getInstId(), attrInfo.getRate());

        /* 查询字段 */
        StringBuilder fields = new StringBuilder();
        for (Direct direct : directs) {
            String colName = tdService.enColName(attrInfo.getId(), direct.getId());
            String d1Name = tdService.enD1ColName(attrInfo.getId(), direct.getId());
            String d2Name = tdService.enD2ColName(attrInfo.getId(), direct.getId());
            fields.append(colName + "," + d1Name + "," + d2Name + ",");
        }

        /* 查询条件 */
        StringBuilder conditions = new StringBuilder();
        for (Direct direct : directs) {
            String d1Name = tdService.enD1ColName(attrInfo.getId(), direct.getId());
            String d2Name = tdService.enD2ColName(attrInfo.getId(), direct.getId());
            conditions.append(d1Name + " > " + qo.getMissInterval() + " OR ");
            conditions.append(d2Name + " > " + qo.getMissInterval() + " OR ");
        }

        fields.deleteCharAt(fields.length() - 1);
        conditions.delete(conditions.length() - 4, conditions.length() - 1);
        return "select ts, point, " + fields + " from " + sTableName + " where ts between '" + qo.getStartTime() + "' and '" + qo.getEndTime() + "' and (" + conditions.toString() + ")";
    }

    /**
     * 按开始结束时间填充两端数据
     */
    private List<Map<String, Object>> fillBothDataBySETime(List<Map<String, Object>> records, Integer attrId, List<Direct> directs,
                                                           String startTime, String endTime, Map<String, List<Stat>> firstLastTimeMap) throws ParseException {
        List<Map<String, Object>> res = new ArrayList<>();
        /* 按测点分组 */
        Map<Integer, List<Map<String, Object>>> pointRecordMap = records.stream().collect(Collectors.groupingBy(e -> (Integer) e.get("point")));
        for (Map.Entry<Integer, List<Map<String, Object>>> en : pointRecordMap.entrySet()) {
            Integer pointId = en.getKey();
            List<Map<String, Object>> pointRecords = en.getValue();
            fillBoth(pointRecords, pointId, directs, attrId, startTime, endTime, firstLastTimeMap);

//            Collections.sort(records, Comparator.comparing(r -> ((Date) r.get("ts"))));
//
//            for (Direct d : directs) {
//                String colName = tdService.enColName(attrId, d.getId());
//                for (int i = 1; i < records.size(); i++) {
//                    Map<String, Object> curRecord = records.get(i);
//                    if (curRecord.get(colName) == null) {
//                        continue;
//                    }
//                    for (int j = i - 1; j >= 0; j--) {
//                        Map<String, Object> beforeRecord = records.get(j);
//                        if (beforeRecord.get(colName) != null) {
//                            long interval = ((Date) curRecord.get("ts")).getTime() - ((Date) beforeRecord.get("ts")).getTime();
//                            curRecord.put(tdService.enDColName(attrId, d.getId()), interval / 1000);
//                            break;
//                        }
//                    }
//                }
//            }
            res.addAll(records);
        }
        return res;
//        pointMap.forEach((point, records) -> {
//            /* 将每个测点的两端所有分量都填充值 */
//            Map<Date, List<Map<String, Object>>> timeMap = toTimeMap(records);
//            timeMap.put(TimeUtil.parse2Second(startTime), 0f);
//            timeMap.put(TimeUtil.parse2Second(endTime), 0f);
//            records = toList(timeMap);
//
//            try {
//                long startMillis = TimeUtil.parse2Second(startTime).getTime();
//                Map<String, Object> first = records.stream().filter(e -> startMillis == ((Date) e.get("ts")).getTime()).findFirst().orElse(null);
//                if (first == null) {
//                    Map<String, Object> map = new HashMap<>();
//                    map.put("ts", TimeUtil.parse2Second(startTime));
//                    map.put("point", point);
//                    for (Direct d : directs) {
//                        map.put(tdService.enColName(attrId, d.getId()), 1f);
//                        map.put(tdService.enDColName(attrId, d.getId()), null);
//                    }
//                    records.add(map);
//                }
//
//                long endMillis = TimeUtil.parse2Second(endTime).getTime();
//                Map<String, Object> last = records.stream().filter(e -> endMillis == ((Date) e.get("ts")).getTime()).findFirst().orElse(null);
//                if (last == null) {
//                    Map<String, Object> map = new HashMap<>();
//                    map.put("ts", TimeUtil.parse2Second(endTime));
//                    map.put("point", point);
//                    for (Direct d : directs) {
//                        map.put(tdService.enColName(attrId, d.getId()), 1f);
//                        map.put(tdService.enDColName(attrId, d.getId()), null);
//                    }
//                    records.add(map);
//                }
//
//                Collections.sort(records, Comparator.comparing(r -> ((Date) r.get("ts"))));
//
//                for (Direct d : directs) {
//                    String colName = tdService.enColName(attrId, d.getId());
//                    for (int i = 1; i < records.size(); i++) {
//                        Map<String, Object> curRecord = records.get(i);
//                        if (curRecord.get(colName) == null) {
//                            continue;
//                        }
//                        for (int j = i - 1; j >= 0; j--) {
//                            Map<String, Object> beforeRecord = records.get(j);
//                            if (beforeRecord.get(colName) != null) {
//                                long interval = ((Date) curRecord.get("ts")).getTime() - ((Date) beforeRecord.get("ts")).getTime();
//                                curRecord.put(tdService.enDColName(attrId, d.getId()), interval);
//                                break;
//                            }
//                        }
//                    }
//                }
//                res.addAll(records);
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        });
    }

    private void fillBoth2222(List<Map<String, Object>> records, Integer pointId, List<Direct> directs, Integer attrId,
                              String startTime, String endTime, Map<String, List<Stat>> firstLastTimeMap) throws ParseException {
//        /* 填充两边 */
//        Map<String, Object> mapStart = new HashMap<>();
//        mapStart.put("ts", TimeUtil.parse2Second(startTime));
//        mapStart.put("point", pointId);
//        for (Direct d : directs) {
//            mapStart.put(tdService.enColName(attrId, d.getId()), 1f);
//            mapStart.put(tdService.enDColName(attrId, d.getId()), null);
//        }
//        records.add(mapStart);
//
//        Map<String, Object> mapEnd = new HashMap<>();
//        mapEnd.put("ts", TimeUtil.parse2Second(endTime));
//        mapEnd.put("point", pointId);
//        for (Direct d : directs) {
//            mapEnd.put(tdService.enColName(attrId, d.getId()), 1f);
//            mapEnd.put(tdService.enDColName(attrId, d.getId()), null);
//        }
//        records.add(mapEnd);
//
//        /* 填充已经存在的数据 */
//
//        firstLastTimeMap.forEach((pointDirect, stats) -> {
//            String[] arr = pointDirect.split("-");
//            if (!pointId.equals(Integer.parseInt(arr[0]))) {
//                return;
//            }
//            stats.get()
//            Integer directId = Integer.parseInt(arr[1]);
//            Map<String, Object> map = new HashMap<>();
//            map.put("ts", TimeUtil.parse2Second(endTime));
//            map.put("point", pointId);
//
//            map.put(tdService.enColName(attrId, directId), 1f);
//            map.put(tdService.enDColName(attrId, directId), null);
//            records.add(map);
//        });
    }

    private void addExist(List<Map<String, Object>> records, Integer pointId, List<Direct> directs, Map<String, Integer> firstLastTimeMap) {
//        for (Direct direct : directs) {
//            records.stream().filter(e -> e.get("ts"))
//        }
    }

    private void fillBoth(List<Map<String, Object>> records, Integer pointId, List<Direct> directs, Integer attrId,
                           String startTime, String endTime, Map<String, List<Stat>> firstLastTimeMap) throws ParseException {
//        /* 确保每个测点数据都有开始和结束时刻数据 */
//        long startMillis = TimeUtil.parse2Second(startTime).getTime();
//        Map<String, Object> startData = records.stream().filter(e -> startMillis == ((Date) e.get("ts")).getTime()).findFirst().orElse(null);
//        if (startData == null) {
//            Map<String, Object> map = new HashMap<>();
//            map.put("ts", TimeUtil.parse2Second(startTime));
//            map.put("point", pointId);
//            for (Direct d : directs) {
//                map.put(tdService.enColName(attrId, d.getId()), 1f);
//                map.put(tdService.enDColName(attrId, d.getId()), null);
//            }
//            records.add(map);
//        }
//
//        long endMillis = TimeUtil.parse2Second(endTime).getTime();
//        Map<String, Object> endData = records.stream().filter(e -> endMillis == ((Date) e.get("ts")).getTime()).findFirst().orElse(null);
//        if (endData == null) {
//            Map<String, Object> map = new HashMap<>();
//            map.put("ts", TimeUtil.parse2Second(endTime));
//            map.put("point", pointId);
//            for (Direct d : directs) {
//                map.put(tdService.enColName(attrId, d.getId()), 1f);
//                map.put(tdService.enDColName(attrId, d.getId()), null);
//            }
//            records.add(map);
//        }
//
//        Collections.sort(records, Comparator.comparing(r -> ((Date) r.get("ts"))));
//
//        /* 结束时间的间隔补全 */
//        endData = records.get(records.size() - 1);
//        for (Direct d : directs) {
//            String dColName = tdService.enDColName(attrId, d.getId());
//            if (endData.get(dColName) == null) {
//                List<Stat> stats = firstLastTimeMap.get(pointId + "-" + d.getId());
//                if (!ObjectUtils.isEmpty(stats)) {
//                    endData.put(dColName, endMillis - stats.get(stats.size() - 1).getLastTime().getTime());
//                }
//            }
//        }

//        /* 开始时间的间隔修正 */
//        secondData = records.get(1);
//
//
//        startData = records.stream().filter(e -> startMillis == ((Date) e.get("ts")).getTime()).findFirst().orElse(null);
//        for (Direct d : directs) {
//            String dColName = tdService.enDColName(attrId, d.getId());
//
//        }
//
//
//
//
//
//        long endMillis = TimeUtil.parse2Second(endTime).getTime();
//        Map<String, Object> last = records.stream().filter(e -> endMillis == ((Date) e.get("ts")).getTime()).findFirst().orElse(null);
//        if (last == null) {
//            /* 所有方向都要计算 */
//            Map<String, Object> map = new HashMap<>();
//            map.put("ts", TimeUtil.parse2Second(endTime));
//            map.put("point", pointId);
//            for (Direct d : directs) {
////                map.put(tdService.enColName(attrId, d.getId()), 1f);
//                List<Stat> stats = firstLastTimeMap.get(pointId + "-" + d.getId());
//                if (!ObjectUtils.isEmpty(stats)) {
//                    map.put(tdService.enDColName(attrId, d.getId()), endMillis - stats.get(stats.size() - 1).getLastTime().getTime());
//                }
//            }
//            records.add(map);
//        } else {
//            /* 只计算没有间隔的方向 */
//            Map<String, Object> map = new HashMap<>();
//            map.put("ts", TimeUtil.parse2Second(endTime));
//            map.put("point", pointId);
//            for (Direct d : directs) {
//                if (map.get(tdService.enDColName(attrId, d.getId())) == null) {
//                    List<Stat> stats = firstLastTimeMap.get(pointId + "-" + d.getId());
//                    if (!ObjectUtils.isEmpty(stats)) {
//                        map.put(tdService.enDColName(attrId, d.getId()), endMillis - stats.get(stats.size() - 1).getLastTime().getTime());
//                    }
//                }
//            }
//            records.add(map);
//        }


//        /* 补充第一个时间和最后一个时间 */
//        for (Direct direct : directs) {
//            List<Stat> stats = firstLastTimeMap.get(pointId + "-" + direct.getId());
//            if (ObjectUtils.isEmpty(stats)) {
//                continue;
//            }
//            Stat firstStat = stats.get(0);
//
//            Map<String, Object> firstT = records.stream().filter(e -> firstStat.getFirstTime().getTime() == ((Date) e.get("ts")).getTime()).findFirst().orElse(null);
//            if (firstT == null) {
//                Map<String, Object> map = new HashMap<>();
//                map.put("ts", firstStat.getFirstTime());
//                map.put("point", pointId);
//                map.put(tdService.enColName(attrId, direct.getId()), 1f);
//                map.put(tdService.enDColName(attrId, direct.getId()), null);
//                records.add(map);
//            }
//
//
//            Stat lastStat = stats.get(stats.size() - 1);
//
//            Map<String, Object> lastT = records.stream().filter(e -> lastStat.getLastTime().getTime() == ((Date) e.get("ts")).getTime()).findFirst().orElse(null);
//            if (lastT == null) {
//                Map<String, Object> map = new HashMap<>();
//                map.put("ts", lastStat.getLastTime());
//                map.put("point", pointId);
//                map.put(tdService.enColName(attrId, direct.getId()), 1f);
//                map.put(tdService.enDColName(attrId, direct.getId()), null);
//                records.add(map);
//            }
//        }
    }

    private List<Map<String, Object>> toList(Map<String, Map<String, Object>> timeMap) {
        if (ObjectUtils.isEmpty(timeMap)) {
            return Collections.emptyList();
        }

        List<Map<String, Object>> list = new ArrayList<>();
        timeMap.forEach((k, v) -> {
            v.put("ts", TimeUtil.parse2Second(k));
            list.add(v);
        });
        return list;
    }


    private Map<String, Map<String, Object>> toTimeMap(List<Map<String, Object>> records) {
        if (ObjectUtils.isEmpty(records)) {
            return Collections.emptyMap();
        }

        Map<String, Map<String, Object>> map = new HashMap<>();
        for (Map<String, Object> record : records) {
            Date ts = (Date) record.get("ts");
            map.put(TimeUtil.format2Second(ts), record);
        }
        return map;
    }

    /**
     * 获取测点审核不通过数量信息
     */
    private Map<String, Integer> getPointApprovalFailNumInfo(Map<String, List<Stat>> pointDirectMap) {
        Map<String, Integer> map = new HashMap<>(pointDirectMap.size());
        pointDirectMap.forEach((pointDirect, statList) -> {
            map.put(pointDirect, statList.stream().mapToInt(e -> e.getApprovalCountFail()).sum());
        });
        return map;
    }

    /**
     * 获取测点采集数量信息
     */
    private Map<String, Integer> getPointTotalNumMap(Map<String, List<Stat>> pointDirectMap) {
        Map<String, Integer> map = new HashMap<>();
        pointDirectMap.forEach((pointDirect, statList) -> {
            map.put(pointDirect, statList.stream().mapToInt(e -> e.getApprovalCountInit() + e.getApprovalCountPass() + e.getApprovalCountFail()).sum());
        });
        return map;
    }

}
